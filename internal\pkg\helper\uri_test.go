package helper

import (
	"fmt"
	"testing"
	"time"
)

// Test GetParamValue
func TestGetParamValue(t *testing.T) {
	encodedString := "charset=UTF-8&bizType=3001&itemTitle=%E3%80%90%E5%88%B8%E5%8C%85%E6%B5%8B%E8%AF%95%E3%80%91%E6%B5%8B%E8%AF%95%E6%B5%8B%E8%AF%95%E6%B5%8B%E8%AF%95%E4%B8%8D%E5%8F%91%E8%B4%A7+%E5%8B%BF%E6%8B%8D&sign=E15CEC560B8B9149C81373EBEFC85E26&outerIdP=p_239_74085EE807A715F497963D167C3A2485&frequency=1&isPerfReq=false&idCardType=1&organizerId=4047676779&requestId=16992647745423400&mainOrderId=3608930593585680512&timestamp=2023-11-06+17%3A59%3A34%2C542&amount=1&method=send&validStart=2023-11-06+00%3A00%3A00&smsTemplate=%E9%AA%8C%E8%AF%81%E7%A0%81%24code.%E6%82%A8%E5%B7%B2%E6%88%90%E5%8A%9F%E8%AE%A2%E8%B4%AD%E9%A5%BF%E4%BA%86%E4%B9%88%E6%97%97%E8%88%B0%E5%BA%97%E6%8F%90%E4%BE%9B%E7%9A%84%E3%80%90%E5%88%B8%E5%8C%85%E6%B5%8B%E8%AF%95%E3%80%91%E6%B5%8B%E8%AF%95%E6%B5%8B%E8%AF%95%E6%B5%8B%E8%AF%95%E4%B8%8D%E5%8F%91%E8%B4%A7+%E5%8B%BF%E6%8B%8D%2C%E6%9C%89%E6%95%88%E6%9C%9F2023%2F11%2F06%E8%87%B32023%2F12%2F05%2C%E6%B6%88%E8%B4%B9%E6%97%B6%E8%AF%B7%E5%87%BA%E7%A4%BA%E6%9C%AC%E7%9F%AD%E4%BF%A1%E4%BB%A5%E9%AA%8C%E8%AF%81.%E5%A6%82%E6%9C%89%E7%96%91%E9%97%AE%2C%E8%AF%B7%E8%81%94%E7%B3%BB%E5%8D%96%E5%AE%B6.&sendStyle=1&mobile=19145546524&mobileType=0&version=2.0&token=4f095f1bcb84abf20f97aa9d5469b125&skuProperties=%E9%9D%A2%E5%80%BC%3A%E9%A5%BF%E4%BA%86%E4%B9%88%E5%88%B8%E5%8C%85%E6%B5%8B%E8%AF%95&organizerNick=%E9%A5%BF%E4%BA%86%E4%B9%88%E6%97%97%E8%88%B0%E5%BA%97&itemId=741079090386&totalFee=1.0&validEnd=2023-12-05+23%3A59%3A59&outerId=3608930593585680512&outerIdSKU=p_239_74085EE807A715F497963D167C3A2485"
	key := "validEnd"
	res, err := GetUriParamValue(encodedString, key)

	ss := time.Now()
	fmt.Println(ss)

	if err != nil {
		t.Fatal(err)
	}
	t.Logf("%+v", res)
}
