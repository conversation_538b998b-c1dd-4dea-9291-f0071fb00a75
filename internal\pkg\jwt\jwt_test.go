package jwt

import (
	"testing"

	"ccb/internal/biz/do"
	"github.com/golang-jwt/jwt/v4"
)

func NewTestJWT() *JWT {
	publicKey, err := jwt.ParseRSAPublicKeyFromPEM([]byte(`-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAugN9kUs5lYXsxf3XyUPV
Rr2+gBpolKJj33CphESUExPuMjRaQ/mHku6ArKtLNGZLTx8psyQgvoJH908GfTEa
gceqjzhpUAF8LcegFCMqJ2GGdUatMsLHiAXH0I7DJSfyKAeeRPaskjFVk0XvE9vA
WDByLt4aKWcOJukOKjw5VscKkywrcS7Cuk0UConm7SLH1keG6IGedk96hyuvNIqL
rE1Jw9wGYekoskOg7jnIKJIfyPB0USRAlXr/RzZYudE76519KuAQsoZppWRqDfTg
rTb1IQE6eKDWw2Cl7UYUlO6qyodoaXkr7fXxgy+Pd/AlLFmkDr9XXLlCF67Zdc0r
zQIDAQAB
-----END PUBLIC KEY-----`))
	if err != nil {
		panic(err)
	}
	privateKey, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`))
	if err != nil {
		panic(err)
	}
	return &JWT{rasPublic: publicKey, rasPrivate: privateKey}
}

// TestParseToken
func TestParseToken(t *testing.T) {
	// 测试ComparePassword
	t.Run("测试ComparePassword", func(t *testing.T) {
		jwtTest := NewTestJWT()
		token := "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************.RGxGv3ZvCDD8tsMzGHZE4R-99ZIq06ni9gXoJv7QJXHfi9e69qfajRr0vU7e-iKaMLI6i8qPpSgZ5nAbNwHpggCpekvY8763FPH8ohxhBPRO3nRkT1aQKoE9lYQtioNqPjcwVOFNovKZUcvJzIbTY3C-qbt4oIUQMrQrwq3a9z1ReKI0Puj7eDZZfRmE7SNLjh0PBbcDv1X4j3m4XR5j08VbfDLD9m7s5HkPLfgEeP7MSdHjRZN1P1bUHTJOowbCRu9zqcNLNTc0KfbCVNKJxezs-_C6WHmo9nYthKPcv-EwtDZFuoBwKTrnSbdnaT4vWJgah5hb2sqJM581zBFJcw"
		// token := "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.G5-hQrT_QJWYgt2IpHqjwNNz9lpFJY77xOhOjD6mmWVE6LZ2MZLixrWbISM0RsisjXLIOdeDFZm7_4MsGVi63RQpW2fLb3AxQYgNBp9o8Z4ZA2jzgzw9c0Sr9wQRBKPkkYp595L7T704ajEWz0EDUSuNneSkAcbm9AW9RrQlr5S_xve4eWvTvhdaPvc42x-GHAETzgtRRsY3FRyyHfJmPwVpnAxSJxcVTdSdZT9pM_sDka7k712-PY7QAoz2zURY6OqdJkSHOSEAcfMopSDbfZQDHVelNdAIHO_JErAulGER_0wEn7Hv8gFWitEzagmRLj5j3CChHykmggPhcx9APQ"
		cliaim, err := jwtTest.ParseToken(token)
		if err != nil {
			t.Errorf("EncryptPassword() error = %v", err)
			return
		}
		t.Logf("EncryptPassword() got = %+v", cliaim)
	})
}

// TestGenerateToken
func TestGenerateToken(t *testing.T) {
	// 测试ComparePassword
	t.Run("测试ComparePassword", func(t *testing.T) {
		jwtTest := NewTestJWT()
		res, err := jwtTest.GenerateToken(&do.AdminUserDo{
			ID:         1,
			Account:    "***********",
			Username:   "lsxd",
			Password:   "",
			Status:     1,
			RoleType:   0,
			CreateTime: 0,
			UpdateTime: 0,
		})
		if err != nil {
			t.Errorf("EncryptPassword() error = %v", err)
			return
		}
		t.Logf("EncryptPassword() got = %v", res)
	})
}
