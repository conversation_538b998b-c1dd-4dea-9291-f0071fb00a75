package data

import (
	"ccb/internal/conf"
	"testing"
)

func TestNewWechat(t *testing.T) {
	c := &conf.<PERSON>trap{
		Wechat: []*conf.Wechat{
			{
				MchID:                      "123456",
				MchCertificateSerialNumber: "123456",
				WechatPayPublicKeyID:       "123456",
				Name:                       "test",
			},
			{
				MchID:                      "123456",
				MchCertificateSerialNumber: "123456",
				Name:                       "test",
			},
		},
	}
	got, err := NewWechat(c)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("NewWechat() error = %v", err)
		return
	}
	t.Logf("NewWechat() = %v", got)
}
