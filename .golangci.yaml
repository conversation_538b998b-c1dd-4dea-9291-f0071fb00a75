run:
  go: '1.20'
  tests: false
  skip-dirs:
    - api
    - third_party
    - internal/data/ent
linters:
#  disable-all: true  # 关闭其他linter
  enable: #下面注释的部分是因为golang1.20和 golangci-lint 有兼容问题
    - asasalint
    - asciicheck
    - bidichk
#    - bodyclose
    - durationcheck
    - errcheck
    - errorlint
    - exhaustive
    - exportloopref
    - gosec
    - govet
    - loggercheck
    - makezero
#    - nilerr
#    - noctx
    - reassign
    - staticcheck
    - typecheck
linters-settings:
#  errcheck:
#    check-type-assertions: true   # 检查类型断言
  errorlint:
    errorf: true                # 检查fmt.Errorf错误是否用%w
  gosec:
    excludes:
      - G401
      - G501
      - G502
      - G503
      - G504
      - G505