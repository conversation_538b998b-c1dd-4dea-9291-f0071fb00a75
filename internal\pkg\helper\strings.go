package helper

import "strings"

func TruncateAccount(address string) string {
	if Is<PERSON><PERSON><PERSON>(address) {
		return TruncatePhoneNumber(address)
	}

	return address
}

func TruncatePhoneNumber(phoneNumber string) string {
	return phoneNumber[:3] + "****" + phoneNumber[7:]
}

func TruncateEmail(address string) string {
	atIndex := strings.Index(address, "@")
	if atIndex == -1 {
		return address
	}

	username := address[:atIndex]
	obfuscatedUsername := obfuscateString(username, len(username)/2)
	return obfuscatedUsername + address[atIndex:]
}

func obfuscateString(str string, visibleChars int) string {
	length := len(str)
	if length <= visibleChars {
		return str
	}

	return str[0:1] + "***" + str[length-visibleChars:]
}
