package base

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	http2 "net/http"
	"strconv"
	"strings"

	"ccb/api/myerr"
	"ccb/internal/pkg/helper"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/pkg/errors"
)

// BaseService service 公共方法
type BaseService struct{}

// LoginUser 登录用户信息
type LoginUser struct {
	Id          int
	Phone       string
	UserName    string
	RealName    string
	AccountType int
	GroupCodes  string
	DingUserId  string
	ID          string `json:"jti,omitempty"`
}

type BaseResponse struct {
	Code  int         `json:"code"`
	Data  interface{} `json:"data"`
	Error string      `json:"error"`
}

const (
	UsersGroupSeller  = "VCL_SELLER"  // 销售角色组
	UsersGroupOperate = "VCL_OPERATE" // 运营角色组
	UsersGroupCashier = "VCL_CASHIER" // 出纳角色组

	AdministratorUserID = 1 // 管理员id

)

// privilegeUserIDs 特权用户id
var privilegeUserIDs = []int{1, 17, 20, 29}

// GetLoginClaim 获取登录用户信息
func (b *BaseService) GetLoginClaim(ctx context.Context) (*LoginUser, error) {
	rq, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return nil, errors.New("无法获取 header 数据")
	}
	userId, _ := convertor.ToInt(rq.Header.Get("User-Id"))
	if userId <= 0 {
		calim, _ := b.parseToken(rq.Header.Get("Authorization"))
		userId = int64(calim.Id)
		if userId <= 0 {
			return nil, myerr.ErrorNotLogin("未登录")
		}
		return calim, nil
	}
	accountType, _ := convertor.ToInt(rq.Header.Get("Account-Type"))
	user := &LoginUser{
		Id:          int(userId),
		Phone:       rq.Header.Get("User-Phone"),
		UserName:    rq.Header.Get("User-Name"),
		RealName:    rq.Header.Get("Real-Name"),
		AccountType: int(accountType),
		GroupCodes:  rq.Header.Get("Group-Codes"),
		DingUserId:  rq.Header.Get("Ding-User-Id"),
	}
	return user, nil
}

// GetLoginedUserIdX 获取登录用户 id，未登录会 panic
func (b *BaseService) GetLoginedUserIdX(ctx context.Context) int {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		panic(err)
	}
	return user.Id
}

// GetLoginedUserId32X 获取登录用户 id，未登录会 panic
func (b *BaseService) GetLoginedUserId32X(ctx context.Context) int32 {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		panic(err)
	}
	return int32(user.Id)
}

// GetGroupCodesX 获取用户组码
func (b *BaseService) GetGroupCodesX(ctx context.Context) []string {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		panic(err)
	}
	return strings.Split(user.GroupCodes, ",")
}

// IsCashierX 是否是销售
func (b *BaseService) IsCashierX(ctx context.Context) bool {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		panic(err)
	}
	return strings.Contains(user.GroupCodes, UsersGroupSeller)
}

// IsAdministratorX 是否是管理员
func (b *BaseService) IsAdministratorX(ctx context.Context) bool {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		panic(err)
	}
	return user.Id == AdministratorUserID
}

// GetUserName 获取用户名
func (b *BaseService) GetUserName(ctx context.Context) string {
	user, err := b.GetLoginClaim(ctx)
	if err != nil {
		return "系统用户"
	}
	if user.RealName != "" {
		return user.RealName
	}
	if user.UserName != "" {
		return user.UserName
	}
	return strconv.Itoa(user.Id)
}

// IsPrivilege 是否是特权用户
func (b *BaseService) IsPrivilege(userID int) bool {
	for _, v := range privilegeUserIDs {
		if userID == v {
			return true
		}
	}
	return false
}

// GetRequestUrl 获取请求url
func (b *BaseService) GetRequestUrl(ctx context.Context) string {
	request, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return ""
	}
	return request.URL.Path
}

// GetRequestIPLong 获取请求ip
func (b *BaseService) GetRequestIPLong(ctx context.Context) uint {
	rq, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return 0
	}
	clientIP := helper.GetClientIP(rq)
	return helper.IPString2Long(clientIP)
}

// ResponseOK 返回 json 成功
func (b *BaseService) ResponseOK(ctx http.Context, code int, data interface{}, error string) error {
	return ctx.JSON(http2.StatusOK, &BaseResponse{
		Code:  code,
		Data:  data,
		Error: error,
	})
}

// ResponseError 返回 json 错误
func (b *BaseService) ResponseError(ctx http.Context, error string) error {
	return ctx.JSON(http2.StatusOK, &BaseResponse{
		Code:  600,
		Error: error,
	})
}

// ResponseErrorWithCode 返回 json 错误带 code
func (b *BaseService) ResponseErrorWithCode(ctx http.Context, code int, error string) error {
	return ctx.JSON(http2.StatusOK, &BaseResponse{
		Code:  code,
		Error: error,
	})
}

// ExportExcel 导出 excel 头设置
func (b *BaseService) ExportExcel(ctx http.Context, encodedFilename string) http.ResponseWriter {
	disposition := fmt.Sprintf("attachment; filename=%s", encodedFilename)
	ctx.Response().Header().Set("Content-Type", "application/vnd.openxmlformats; charset=utf-8")
	ctx.Response().Header().Set("Content-Disposition", disposition)
	ctx.Response().Header().Set("Access-Control-Expose-Headers", "Content-Disposition")
	return ctx.Response()
}

// parseToken 解析 token
func (b *BaseService) parseToken(token string) (*LoginUser, error) {
	authorization := strings.ReplaceAll(token, "Bearer ", "")
	parts := strings.Split(authorization, ".")
	if len(parts) != 3 {
		return nil, errors.New("token contains an invalid number of segments")
	}
	info, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}
	claims := new(LoginUser)
	err = json.Unmarshal(info, &claims)
	if err != nil {
		return nil, err
	}
	id, _ := strconv.Atoi(claims.ID)
	return &LoginUser{
		Id:          id,
		Phone:       claims.Phone,
		UserName:    claims.UserName,
		RealName:    claims.RealName,
		AccountType: claims.AccountType,
		GroupCodes:  claims.GroupCodes,
		DingUserId:  claims.DingUserId,
	}, nil
}
