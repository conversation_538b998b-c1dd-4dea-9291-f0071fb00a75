<!doctype html><html lang="en"><head>
    <link rel="shortcut icon" href="http://test.user.1688sup.com/logo.svg">
    <meta charset="utf-8"><meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1,user-scalable=yes"><title>福利商城 API 文档</title><link rel="stylesheet" href="//cdn.jsdelivr.net/gh/highlightjs/cdn-release@9.18.1/build/styles/default.min.css"><script src="//cdn.jsdelivr.net/gh/highlightjs/cdn-release@9.18.1/build/highlight.min.js"></script><script defer="defer" src="rapidoc-min.js"></script></head><script>window.addEventListener('DOMContentLoaded', (event) => {
    const docEl = document.getElementById('thedoc');
    docEl.addEventListener('before-try', (e) => {
        e.detail.request.headers.append('App-Id', '1');

        const jwt = localStorage.getItem('jwt');
        e.detail.request.headers.append('Authorization', 'Bearer ' + jwt);
    });i
  });</script>
<style>
    #btnSave {
        position: relative;
        overflow: hidden;
        z-index: 1;
        margin-left: 20px;
        padding: 0px 20px;
    }
    #btnSave:before {
        content: '保 存';
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        transform: scale(3);
        background: #EFEFEF;
        transition: all 0.3s;
        opacity: 0;
        z-index: 2;
    }
    #btnSave:hover:before {
        transform: scale(1);
        opacity: 1;
    }

</style>
<body><rapi-doc spec-url="/doc/openapi.yaml" id="thedoc" theme="Light" render-style="read" schema-style="table" show-header="false" show-method-in-nav-bar="true" use-path-in-nav-bar="true" show-components="true" show-info="true" show-header="true" allow-search="true" allow-advanced-search="true" allow-spec-url-load="false" allow-spec-file-download="false" allow-server-selection="false" allow-authentication="true" update-route="false" match-type="regex" persist-auth="true">
    <img
            slot="nav-logo"
            src="https://user.1688sup.com/favicon.ico" width="110px"
    />
    <div slot="auth">
        JWT：<input name="jwt" style="width:50%"> <button id="btnSave" onclick="saveJwt()">保 存</button>
    </div>
</rapi-doc></body>
<script>
const jwt = localStorage.getItem('jwt');
const node = document.querySelector('input')
if (jwt) {
    node.value = jwt;
}
function saveJwt() {
    const jwt = node.value;
    localStorage.setItem('jwt', jwt);
    alert('保存成功');
}
</script>
</html>