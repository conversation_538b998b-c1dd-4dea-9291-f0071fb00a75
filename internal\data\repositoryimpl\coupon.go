package repositoryimpl

import (
	"context"
	"errors"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/data"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/coupon"
	"ccb/internal/data/ent/predicate"
)

type CouponRepoImpl struct {
	Base[ent.Coupon, do.CouponDo, ent.CouponQuery]
	data   *data.Data
	ccbrpc rpc.CCBRpc
}

func NewCouponRepoImpl(data *data.Data, ccbrpc rpc.CCBRpc) repository.CouponRepo {
	return &CouponRepoImpl{data: data, ccbrpc: ccbrpc}
}

// ToEntity 转换成实体
func (r *CouponRepoImpl) ToEntity(po *ent.Coupon) *do.CouponDo {
	if po == nil {
		return nil
	}
	if po.MerchantID != "" {
		if po.Code != "" {
			if val, err := r.ccbrpc.DecryptCpn(po.MerchantID, []byte(po.Code)); err == nil && len(val) > 0 {
				po.Code = string(val)
			}
		}
		if po.Link != "" {
			if val, err := r.ccbrpc.DecryptCpn(po.MerchantID, []byte(po.Link)); err == nil && len(val) > 0 {
				po.Link = string(val)
			}
		}
	}
	dc := &do.CouponDo{
		Id:            po.ID,
		Code:          po.Code,
		Link:          po.Link,
		OrderId:       po.OrderID,
		MerchantId:    po.MerchantID,
		ProductId:     po.ProductID,
		ProductCode:   po.ProductCode,
		Status:        valobj.CouponStatus(po.Status),
		Total:         po.Total,
		Residue:       po.Residue,
		TransactionId: po.TransactionID,
		ExpireTime:    &po.ExpireTime,
		CreateTime:    &po.CreateTime,
		UpdateTime:    &po.UpdateTime,
		Product:       productRepoImpl.ToEntity(po.Edges.Product),
	}
	return dc
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (r *CouponRepoImpl) ToEntities(pos []*ent.Coupon) []*do.CouponDo {
	if pos == nil {
		return nil
	}
	entities := make([]*do.CouponDo, len(pos))
	for k, p := range pos {
		entities[k] = r.ToEntity(p)
	}
	return entities
}

func (r *CouponRepoImpl) CreateE(ctx context.Context, d *do.CouponDo) (*do.CouponDo, error) {
	if d.MerchantId != "" {
		if d.Code != "" {
			if val, err := r.ccbrpc.EncryptCpn(d.MerchantId, []byte(d.Code)); err == nil && len(val) > 0 {
				d.Code = string(val)
			}
		}
		if d.Link != "" {
			if val, err := r.ccbrpc.EncryptCpn(d.MerchantId, []byte(d.Link)); err == nil && len(val) > 0 {
				d.Link = string(val)
			}
		}
	}
	row, err := r.data.GetDb(ctx).Coupon.Create().
		SetCode(d.Code).
		SetLink(d.Link).
		SetMerchantID(d.MerchantId).
		SetProductID(d.ProductId).
		SetProductCode(d.ProductCode).
		SetOrderID(d.OrderId).
		SetStatus(d.Status.GetValue()).
		SetTotal(d.Total).
		SetResidue(d.Residue).
		SetTransactionID(d.TransactionId).
		SetExpireTime(*d.ExpireTime).
		SetCreateTime(time.Now()).
		Save(ctx)
	return r.ToEntity(row), err
}

func (r *CouponRepoImpl) UpdateE(ctx context.Context, d *do.CouponDo) (*do.CouponDo, error) {
	row, err := r.data.GetDb(ctx).Coupon.UpdateOneID(d.Id).
		SetStatus(d.Status.GetValue()).
		SetResidue(d.Residue).
		SetExpireTime(*d.ExpireTime).
		SetTransactionID(d.TransactionId).
		SetUpdateTime(time.Now()).
		Save(ctx)
	return r.ToEntity(row), err
}

func (r *CouponRepoImpl) UpdateStatus(ctx context.Context, id, residue int, oldStatus, status valobj.CouponStatus, transactionID string) (*do.CouponDo, error) {
	row, err := r.data.GetDb(ctx).Coupon.UpdateOneID(id).
		Where(coupon.StatusEQ(oldStatus.GetValue())).
		SetStatus(status.GetValue()).
		AddResidue(residue).
		SetTransactionID(transactionID).
		SetUpdateTime(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return r.ToEntity(row), nil
}

func (r *CouponRepoImpl) FindByCode(ctx context.Context, channelId, code string) (*do.CouponDo, error) {
	if channelId != "" {
		if code != "" {
			if val, err := r.ccbrpc.EncryptCpn(channelId, []byte(code)); err == nil && len(val) > 0 {
				code = string(val)
			}
		}
	}
	row, err := r.data.GetDb(ctx).Coupon.Query().
		Where(coupon.CodeEQ(code)).WithProduct().
		First(ctx)
	return r.ToEntity(row), err
}

func (r *CouponRepoImpl) FindByOrderId(ctx context.Context, orderId int) ([]*do.CouponDo, error) {
	rows, err := r.data.GetDb(ctx).Coupon.Query().
		Where(coupon.OrderID(orderId)).
		All(ctx)
	return r.ToEntities(rows), err
}

func (r *CouponRepoImpl) FindPageList(ctx context.Context, reqBo *bo.CouponListBo) ([]*do.CouponDo, *bo.RespPageBo, error) {
	where := r.setQuery(reqBo)
	if len(where) == 0 {
		return nil, nil, errors.New("条件不能为空")
	}
	respPage := new(bo.RespPageBo)
	query := r.data.GetDb(ctx).Coupon.Query().Where(where...)
	if reqBo.ReqPageBo != nil {
		r.SetPageByBo(query, reqBo.ReqPageBo)
		respPage = r.QueryRespPage(ctx, query, reqBo.ReqPageBo)
	}
	entities, err := query.Order(ent.Asc(coupon.FieldCreateTime)).WithProduct().All(ctx)
	if err != nil {
		return nil, nil, err
	}
	return r.ToEntities(entities), respPage, nil
}

func (r *CouponRepoImpl) setQuery(reqBo *bo.CouponListBo) []predicate.Coupon {
	where := make([]predicate.Coupon, 0)
	if len(reqBo.Ids) > 0 {
		where = append(where, coupon.IDIn(reqBo.Ids...))
	}
	if reqBo.Code != "" {
		if reqBo.ChannelId != "" {
			if val, err := r.ccbrpc.EncryptCpn(reqBo.ChannelId, []byte(reqBo.Code)); err == nil && len(val) > 0 {
				reqBo.Code = string(val)
			}
		}
		where = append(where, coupon.Code(reqBo.Code))
	}
	if reqBo.BeginDate != nil {
		where = append(where, coupon.UpdateTimeGTE(*reqBo.BeginDate))
	}
	if reqBo.EndDate != nil {
		where = append(where, coupon.UpdateTimeLT(*reqBo.EndDate))
	}
	if len(reqBo.Status) > 0 {
		where = append(where, coupon.StatusIn(reqBo.Status...))
	}
	return where
}
