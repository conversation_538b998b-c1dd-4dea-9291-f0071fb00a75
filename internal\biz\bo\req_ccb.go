package bo

import (
	"ccb/internal/biz/valobj"
	"encoding/json"
)

type CCBCoupon struct {
	CouponCode string `json:"couponCode"`
	Password   string `json:"password,omitempty"`
	Link       string `json:"link,omitempty"`
}

type CCBCouponOrderNotifyReq struct {
	UserId     string                            `json:"userId"`
	OrderId    string                            `json:"orderId"`
	Status     valobj.CCBCouponOrderNotifyStatus `json:"status"` // 2-失败，3-成功
	Desc       string                            `json:"desc,omitempty"`
	UseType    valobj.CCBCouponUseType           `json:"useType"` // 1兑换码 4动态核销码 5静态核销码 6直接发放
	CompleteTm string                            `json:"completeTm"`
	Coupons    []CCBCoupon                       `json:"coupons,omitempty"`
}

type CCBCouponStatusNotifyReq struct {
	ProductId  string                    `json:"productId,omitempty"`
	OrderId    string                    `json:"orderId,omitempty"`
	CouponCode string                    `json:"couponCode"`
	Operation  valobj.CCBCouponOperation `json:"operation"`          // 011019-未使用->已使用  011020-已使用->未使用
	UseOrderId string                    `json:"useOrderId"`         // 核销订单号
	OrderAmt   string                    `json:"orderAmt,omitempty"` // 本次使用优惠券时的订单金额
	PayAmt     string                    `json:"payAmt,omitempty"`   // 本次使用优惠券时的用户支付金额
	PrftAmt    string                    `json:"prftAmt,omitempty"`  // 本次使用优惠券时所享受到的优惠金额
	StoreId1   string                    `json:"storeId_1,omitempty"`
	StoreId2   string                    `json:"storeId_2,omitempty"`
	UseTm      string                    `json:"useTm,omitempty"`
	ScnId      string                    `json:"scnId,omitempty"`
}

type CCBRechargeNotifyReq struct {
	UserId        string                            `json:"userId,omitempty"`
	OrderId       string                            `json:"orderId"`
	PltfrmOrderId string                            `json:"pltfrmOrderId,omitempty"`
	Status        valobj.CCBCouponOrderNotifyStatus `json:"status"`
	Desc          string                            `json:"desc,omitempty"`
	CompleteTm    string                            `json:"completeTm"`
}

type CCBCouponReceiveReq struct {
	UserId       string            `json:"userId"`
	MaskedUserId string            `json:"maskedUserId"`
	UserIdMd5    string            `json:"userIdMd5"`
	ProductId    string            `json:"productId"`
	OrderId      string            `json:"orderId"`
	RetryCnt     int               `json:"retryCnt"`
	Num          int               `json:"num"`
	DccpAvyId    string            `json:"dccpAvyId"`
	PaySummary   PaySummary        `json:"paySummary"`
	ExtrBsnData  ExtrBsnDataString `json:"extrBsnData,omitempty"`
}

type ExtrBsnDataString string

func (e *ExtrBsnDataString) Unmarshal() *ExtrBsnData {
	if e == nil || *e == "" {
		return nil
	}
	ebd := &ExtrBsnData{}
	_ = json.Unmarshal([]byte(*e), &ebd)
	return ebd
}

type ExtrBsnData struct {
	WechtAppidCd  string `json:"WeCht_appid_Cd"`  // 微信主体
	WechtOpenidCd string `json:"WeCht_openid_Cd"` // 用户openid
	CrdTpCd       string `json:"Crd_TpCd"`        // 卡标识，01借记卡、02贷记卡
}

type PaySummary struct {
	PayAmt      string `json:"payAmt"`      // 用户支付金额
	IntegralAmt string `json:"integralAmt"` // 积分抵扣金额
	ECNY        bool   `json:"eCNY"`        // 是否使用数字人民币支付
	MrchCost    string `json:"mrchCost"`    // 商户补贴金额
	BankCost    string `json:"bankCost"`    // 银行补贴金额
}

type CCBUserRechargeReq struct {
	UserId      string `json:"userId"`
	ProductId   string `json:"productId"`
	AccountType string `json:"accountType"`
	OrderId     string `json:"orderId"`
	RetryCnt    string `json:"retryCnt"`
	Amount      int    `json:"amount"`
	DccpAvyId   string `json:"dccpAvyId"`
}

type CCBCouponStatusReq struct {
	UserId     string `json:"userId"`
	ProductId  string `json:"productId"`
	CouponCode string `json:"couponCode"`
}

type CCBReconciliationNotifyReq struct {
	BatchNum string `json:"batchNum"`
	Result   string `json:"result"`
	Msg      string `json:"msg"`
}

type PushReconciliationReq struct {
	Number    string `json:"number"`
	Status    []int  `json:"status"`
	BeginDate string `json:"beginDate"`
}

type ProductListReq struct {
	PageSize  int    `json:"pageSize"`
	PageNo    int    `json:"pageNo"`
	ProductId string `json:"productId"`
	InsId     string `json:"insId"`
	MrchId    string `json:"mrchId"`
	PdCl      string `json:"pdCl"`
}

type CCBProductStockReq struct {
	ProductId string `json:"productId"`
	DccpAvyId string `json:"dccpAvyId"`
}

type CCBCouponCancelReq struct {
	OrderId    string `json:"orderId"`
	ProductId  string `json:"productId"`
	CouponCode string `json:"couponCode"`
	UserId     string `json:"userId"`
	Operation  string `json:"operation"`
}
