package repository

import (
	"context"

	"ccb/internal/biz/bo"

	"ccb/internal/biz/do"
)

type CouponLogRepo interface {
	CreateE(ctx context.Context, d *do.CouponLogDo) (*do.CouponLogDo, error)
	FindLastLog(ctx context.Context, req *bo.CouponLogBo) (*do.CouponLogDo, error)
	FindPageList(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, *bo.RespPageBo, error)
	FindAll(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, error)
	DayRows(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, error)
}
