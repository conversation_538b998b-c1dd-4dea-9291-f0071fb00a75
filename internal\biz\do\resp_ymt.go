package do

type OrderResponse struct {
	OutBizNo       string `json:"out_biz_no"`
	Key            string `json:"key"`
	Url            string `json:"url"`
	TradeNo        string `json:"trade_no"`
	ValidBeginTime string `json:"valid_begin_time"`
	ValidEndTime   string `json:"valid_end_time"`
	UsableNum      int    `json:"usable_num"`
	Status         int    `json:"status"` //状态 1:正常 2:已核销 3:已作废 4:已过期（过期无回调通知，若过期可调用查询接口获取明细）
}
