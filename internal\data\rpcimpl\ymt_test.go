package rpcimpl

import (
	"fmt"
	"testing"

	"ccb/internal/initialize"

	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
)

func TestOrder(t *testing.T) {
	bc := initialize.LoadConfig()
	ip := NewYmtRpcImpl(bc, nil)
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
}
