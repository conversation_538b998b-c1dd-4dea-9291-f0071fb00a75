FROM registry.cn-chengdu.aliyuncs.com/lsxd/golang:tool-v1 AS builder

COPY . /src
WORKDIR /src

ARG CODEUP_USER
ARG CODEUP_PASS
RUN echo "machine codeup.aliyun.com login ${CODEUP_USER} password ${CODEUP_PASS}" > ~/.netrc && \
    make generate && \
    go env -w GONOPROXY="codeup.aliyun.com" && \
    GOPROXY="https://goproxy.cn,direct" make build

FROM registry.cn-chengdu.aliyuncs.com/lsxd/debian:stable-slim

RUN cp -pv /etc/apt/sources.list /etc/apt/sources.list.bak && \
        sed -i -e 's/deb.debian.org/mirrors.aliyun.com/g' -e 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
        apt-get update && apt-get install -y --no-install-recommends \
		ca-certificates  \
        netbase \
        && rm -rf /var/lib/apt/lists/ \
        && apt-get autoremove -y && apt-get autoclean -y

COPY --from=builder /src/bin /app
#正式环境不要打包 configs，本身也不会使用
COPY --from=builder /src/configs_prod/ /app/configs/

#删除config文件，防止读取到非线上配置文件，线上是通过挂载进来的
RUN rm -rf /app/configs/config.yaml

WORKDIR /app

EXPOSE 8000
VOLUME /data/conf
ENV ROCKETMQ_GO_LOG_LEVEL=error TZ=Asia/Shanghai


CMD ["./server", "-conf", "./configs"]
