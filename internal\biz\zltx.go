package biz

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/conf"
	"ccb/internal/data/ent"

	openapi_go_sdk "gitee.com/chengdu_blue_brothers/openapi-go-sdk"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/api"
	"github.com/duke-git/lancet/v2/retry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type ZLTXBiz struct {
	c             *conf.Bootstrap
	log           *log.Helper
	zltxrpc       rpc.ZLTXRpc
	ccbrpc        rpc.CCBRpc
	orderRepo     repository.OrderRepo
	couponRepo    repository.CouponRepo
	couponLogRepo repository.CouponLogRepo
	stockRepo     repository.StockRepo
	transRepo     repository.TransactionRepo
}

func NewZLTXBiz(
	c *conf.Bootstrap,
	log *log.Helper,
	zltxrpc rpc.ZLTXRpc,
	ccbrpc rpc.CCBRpc,
	orderRepo repository.OrderRepo,
	couponRepo repository.CouponRepo,
	couponLogRepo repository.CouponLogRepo,
	stockRepo repository.StockRepo,
	transRepo repository.TransactionRepo,
) *ZLTXBiz {
	return &ZLTXBiz{
		c:             c,
		log:           log,
		zltxrpc:       zltxrpc,
		ccbrpc:        ccbrpc,
		orderRepo:     orderRepo,
		couponRepo:    couponRepo,
		couponLogRepo: couponLogRepo,
		stockRepo:     stockRepo,
		transRepo:     transRepo,
	}
}

// OrderNotify 订单回调
func (b *ZLTXBiz) OrderNotify(ctx http.Context) error {
	reqBo, err := b.zltxrpc.ParseOrderNotify(ctx.Request())
	if err != nil {
		return err
	}
	b.log.Infof("直连天下订单回调:%+v", reqBo)
	order, err := b.orderRepo.FindByOrderNo(ctx, reqBo.OutTradeNo)
	if err != nil {
		b.log.Errorf("没有找到%s订单", reqBo.OutTradeNo)
		return nil
	}
	codePwd := ""
	codeLink := ""
	code := ""
	if reqBo.CardCode.Value() != "" {
		code = b.decryptApiCode(reqBo.CardCode.Value(), order.UpstreamMerchantID)
		if code == "" {
			b.log.Errorf("直连天下解密订单%s卡密失败:cardCode:%s", reqBo.OutTradeNo, reqBo.CardCode.Value())
			return errors.New("直连天下解密失败")
		}
		if strings.Contains(code, "http") {
			codeLink = code
			link, err := url.Parse(code)
			if err == nil && link.Path != "" {
				code = strings.TrimLeft(link.Path, "/")
			}
		}
	}
	now := time.Now()
	exprtime := now.AddDate(1, 0, 0)
	orderStatus := valobj.CCBCouponOrderNotifyFail
	var (
		coupon = new(do.CouponDo)
		dberr  error
	)
	if reqBo.Status.IsSuccess() {
		if order.Type == valobj.OrderTypeDirect {
			if order.Status == valobj.OrderStatusRecharging {
				_, dberr = b.orderRepo.UpdateStatus(ctx, order.Id, valobj.OrderStatusRecharging, valobj.OrderStatusCompleted)
				if dberr != nil {
					return dberr
				}
			}
		} else {
			coupon, err = b.couponRepo.FindByCode(ctx, defaultChannelId, code)
			if err != nil && ent.IsNotFound(err) {
				_ = retry.Retry(func() error {
					err = b.transRepo.Exec(context.Background(), func(cx context.Context) error {
						coupon, dberr = b.couponRepo.CreateE(cx, &do.CouponDo{
							Code:        code,
							Link:        codeLink,
							OrderId:     order.Id,
							ProductId:   order.ProductId,
							ProductCode: order.ProductCode,
							MerchantId:  order.MerchantId,
							Status:      valobj.CouponStatusUnkown,
							Total:       order.Number,
							Residue:     order.Number,
							ExpireTime:  &exprtime,
							CreateTime:  &now,
						})
						if dberr != nil {
							return dberr
						}
						_, dberr = b.orderRepo.UpdateStatus(cx, order.Id, valobj.OrderStatusRecharging, valobj.OrderStatusCompleted)
						return dberr
					})
					return err
				}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
				if err != nil {
					return err
				}
			} else {
				if coupon != nil {
					if coupon.Status != valobj.CouponStatusUnkown { // 已经被处理了 直接返回
						return nil
					}
				}
			}
		}
		orderStatus = valobj.CCBCouponOrderNotifySuccess
	} else {
		if order.Status != valobj.OrderStatusRechargeFailed {
			_, err = b.orderRepo.UpdateStatus(context.Background(), order.Id, order.Status, valobj.OrderStatusRechargeFailed)
			if err != nil {
				return err
			}
			stock, _ := b.stockRepo.FindOne(context.Background(), &bo.ProductStockBo{
				ProductCode: order.ProductCode,
				ProductId:   order.ProductId,
				MerchantId:  order.MerchantId,
			})
			if stock.ID > 0 && stock.Total > stock.Residue {
				err = b.stockRepo.UpdateResidue(context.Background(), stock.ID, 1)
			}
		}
	}
	ret := &do.CodeMsg{}
	_ = retry.Retry(func() error {
		if order.Type == valobj.OrderTypeDirect {
			ret, err = b.ccbrpc.RechargeNotify(order.MerchantId, &bo.CCBRechargeNotifyReq{
				UserId:        order.UserId,
				OrderId:       order.OrderNumber,
				PltfrmOrderId: "",
				Status:        orderStatus,
				Desc:          "",
				CompleteTm:    time.Now().Format(defaultFormatLayout),
			})
		} else {
			notifyReq := &bo.CCBCouponOrderNotifyReq{
				UserId:     order.UserId,
				OrderId:    order.OrderNumber,
				Status:     orderStatus,
				Desc:       "",
				UseType:    valobj.CCBCouponUseTypeKey,
				CompleteTm: time.Now().Format(defaultFormatLayout),
			}
			if orderStatus == 3 {
				notifyReq.Coupons = make([]bo.CCBCoupon, 0)
				notifyReq.Coupons = append(notifyReq.Coupons, bo.CCBCoupon{
					CouponCode: code,
					Password:   codePwd,
					Link:       codeLink,
				})
			}
			ret, err = b.ccbrpc.CouponOrderNotify(order.MerchantId, notifyReq)
		}
		return err
	}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
	if err != nil {
		b.log.Errorf("订单%s推送失败,状态值%d,错误:%s", order.OrderNumber, orderStatus, err.Error())
		return err
	} else {
		if ret.Code != "0000" {
			b.log.Errorf("订单%s推送失败,状态值%s,错误:%s", order.OrderNumber, ret.Code, ret.Msg)
			return errors.New(ret.Msg)
		}
		_, _ = b.couponRepo.UpdateStatus(ctx, coupon.Id, 0, coupon.Status, valobj.CouponStatusUnused, coupon.TransactionId)
		b.log.Infof("订单%s推送成功,状态值%d", order.OrderNumber, orderStatus)
	}
	return nil
}

func (b *ZLTXBiz) CouponStatusChange(ctx http.Context) error {
	param := b.getBody(ctx)
	if len(param) == 0 {
		return errors.New("参数不能为空")
	}
	coupon, err := b.couponRepo.FindByCode(ctx, defaultChannelId, param.Get("code"))
	if err != nil {
		return err
	}
	s, _ := strconv.Atoi(param.Get("status"))
	status := valobj.CouponStatus(s)
	if coupon.Status == status {
		return nil
	}
	operation := valobj.CCBCouponOperationUnkown
	residue := -1
	if coupon.Status == valobj.CouponStatusUnused && status == valobj.CouponStatusUsed {
		operation = valobj.CCBCouponOperationToUsed
	} else if coupon.Status == valobj.CouponStatusUsed && status == valobj.CouponStatusUnused {
		operation = valobj.CCBCouponOperationToUnUsed
		residue = 1
	}
	if operation == valobj.CCBCouponOperationUnkown {
		return nil
	}
	coupon, err = b.couponRepo.UpdateStatus(ctx, coupon.Id, residue, coupon.Status, status, coupon.TransactionId)
	if err != nil {
		return err
	}
	dateStatus := fmt.Sprintf("%d,%d", coupon.Status, status)
	now := time.Now()
	couponLog, err := b.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
		BeginDate: &now,
		CouponId:  coupon.Id,
	})
	if err != nil && !ent.IsNotFound(err) {
		return err
	}
	if couponLog.Id > 0 {
		ss := strings.Split(couponLog.DateStatus, ",")
		dateStatus = fmt.Sprintf("%s,%d", ss[0], status)
	}
	reqBody := make(map[string]string)
	for k := range param {
		reqBody[k] = param.Get(k)
	}
	js, _ := json.Marshal(reqBody)
	_, err = b.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
		CouponId:     coupon.Id,
		BeforeStatus: coupon.Status,
		Status:       status,
		DateStatus:   dateStatus,
		RequestData:  string(js),
	})
	if err != nil {
		return err
	}
	order, err := b.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		return err
	}
	if param.Get("storeId") == "" {
		param.Set("storeId", "lsxd")
	}
	if param.Get("orderId") == "" {
		param.Set("orderId", strings.Replace(order.OrderNumber, "rv", "LSXD", 1))
	}
	_, err = b.ccbrpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
		ProductId:  coupon.ProductCode,
		OrderId:    order.OrderNumber,
		CouponCode: coupon.Code,
		Operation:  operation,
		UseOrderId: param.Get("orderId"),
		OrderAmt:   "0",
		PayAmt:     "0",
		PrftAmt:    "0",
		StoreId2:   param.Get("storeId"),
		UseTm:      param.Get("useTime"),
	})
	return err
}

func (b *ZLTXBiz) getBody(ctx http.Context) url.Values {
	_ = ctx.Request().ParseForm()
	return ctx.Request().PostForm
}

func (b *ZLTXBiz) ProductList(ctx http.Context) (*api.RechargeProductResp, error) {
	param := b.getBody(ctx)
	return b.zltxrpc.Product(param.Get("merchantId"))
}

func (b *ZLTXBiz) decryptApiCode(apicode, platformId string) string {
	code, err := openapi_go_sdk.GetDecodeCardText(apicode, b.c.ZltxMap[platformId].SecretKey)
	if err != nil {
		b.log.Errorf("直充天下订单:解密失败:%s", err.Error())
	}
	return code
}
