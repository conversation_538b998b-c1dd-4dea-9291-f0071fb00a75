package data

import (
	"ccb/internal/conf"
	"ccb/internal/pkg/helper"
	"context"
	"crypto/x509"
	"fmt"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"os"
)

type Wechat struct {
	Clients map[string]*core.Client
}

func NewWechat(c *conf.Bootstrap) (*Wechat, error) {

	clients := make(map[string]*core.Client, len(c.Wechat))

	for _, wechat := range c.Wechat {
		client, err := buildWechat(wechat)
		if err != nil {
			return nil, err
		}
		clients[wechat.MchID] = client
	}

	return &Wechat{Clients: clients}, nil
}

func (this *Wechat) Get(mchId string) (*core.Client, error) {

	if len(this.Clients) == 0 {
		return nil, fmt.Errorf("微信调用client不存在")
	}

	if mchId == "" {
		return nil, fmt.Errorf("微信商户ID不能为空")
	}

	if client, ok := this.Clients[mchId]; ok {
		return client, nil
	}

	return nil, fmt.Errorf("微信调用client不存在[%s]", mchId)
}

func buildWechat(w *conf.Wechat) (*core.Client, error) {

	if len(w.WechatPayPublicKeyID) > 0 {
		return buildNewWechat(w)
	}
	return buildOldWechat(w)
}

func buildOldWechat(c *conf.Wechat) (*core.Client, error) {

	dir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]获取目的地址有误[%v]", c.MchID, c.Name, err)
	}

	filePath := fmt.Sprintf("%s/%s/%s/%s", dir, "cert", "wechat", c.MchID)
	if !helper.FileExists(filePath) {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]微信密钥证书信息不存在,请联系技术人员处理", c.MchID, c.Name)
	}

	// 商户相关配置,商户API私钥
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(fmt.Sprintf("%s/%s", filePath, "wechat_private_key.pem"))
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]商户API私钥加载有误[%v]", c.MchID, c.Name, err)
	}

	// 微信支付平台配置
	wechatPayCertificate, err := utils.LoadCertificateWithPath(fmt.Sprintf("%s/%s", filePath, "wechat_cert.pem"))
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]微信平台证书有误[%v]", c.MchID, c.Name, err)
	}

	client, err := core.NewClient(
		context.Background(),
		option.WithMerchantCredential(
			c.MchID,
			c.MchCertificateSerialNumber,
			mchPrivateKey,
		),
		option.WithWechatPayCertificate([]*x509.Certificate{wechatPayCertificate}),
	)
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]创建 wechat client 有误[%v]", c.MchID, c.Name, err)
	}

	return client, nil
}

func buildNewWechat(c *conf.Wechat) (*core.Client, error) {

	dir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]获取目的地址有误[%v]", c.MchID, c.Name, err)
	}

	filePath := fmt.Sprintf("%s/%s/%s/%s", dir, "cert", "wechat", c.MchID)

	if !helper.FileExists(filePath) {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]微信密钥证书信息不存在,请联系技术人员处理", c.MchID, c.Name)
	}

	// 商户API私钥
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(fmt.Sprintf("%s/%s", filePath, "wechat_private_key.pem"))
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]商户API私钥加载有误[%v]", c.MchID, c.Name, err)
	}

	// 微信支付平台配置
	wechatPayPublicKey, err := utils.LoadPublicKeyWithPath(fmt.Sprintf("%s/%s", filePath, "pub_key.pem"))
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]微信平台公钥有误[%v]", c.MchID, c.Name, err)
	}

	opts := []core.ClientOption{
		option.WithWechatPayPublicKeyAuthCipher(
			c.MchID,
			c.MchCertificateSerialNumber,
			mchPrivateKey,
			c.WechatPayPublicKeyID,
			wechatPayPublicKey,
		),
	}

	client, err := core.NewClient(context.Background(), opts...)
	if err != nil {
		return nil, fmt.Errorf("商户ID[%s]商户名称[%s]创建 wechat client 有误[%v]", c.MchID, c.Name, err)
	}

	return client, nil
}
