package data

import (
	"context"
	"fmt"

	"ccb/internal/conf"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/intercept"
	"ccb/internal/pkg/sms"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/entcache"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/event"
	ent2 "entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"github.com/go-kratos/kratos/v2/log"
	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"
)

// Data .
type Data struct {
	SmsCli *sms.Sms
	// Db 数据库client
	Db *ent.Client
	// Rdb Redis的client
	Rdb *redis.Client
	// Mq 的 producer
	MqProducer *event.Producer
}

// NewData .
func NewData(c *conf.Bootstrap, hLog *log.Helper) (*Data, func(), error) {
	// 构建 redis
	rdb := buildRdb(c.Data, hLog)
	// 构建 database
	db, errDb := buildDb(c.Data, hLog, rdb)

	// 构建 mq producer
	mqProducer, errMq := buildMqProducer(c.EventRocketMQ, hLog)

	cleanup := func() {
		if db != nil {
			if err := db.Close(); err != nil {
				hLog.Error("关闭 Db 失败：", err)
			}
		}

		if rdb != nil {
			if err := rdb.Close(); err != nil {
				hLog.Error("关闭 redis 失败：", err)
			}
		}

		if mqProducer != nil {
			if err := mqProducer.Shutdown(); err != nil {
				hLog.Error("关闭 rocketMQ producer 失败：", err)
			}
		}
		fmt.Println("关闭 data 中的连接资源已完成")
	}
	var resErr error = nil
	if errDb != nil {
		resErr = errDb
	} else if errMq != nil {
		resErr = errMq
	}

	return &Data{Db: db, Rdb: rdb, MqProducer: mqProducer, SmsCli: sms.NewSms(c.GetSendSms(), hLog)}, cleanup, resErr
}

// GetDb 获取DB client，此方法会判断 context 是否有事务，如果有会返回带事务的 client
func (d Data) GetDb(ctx context.Context) *ent.Client {
	tx, ok := ctx.Value(ContextTxKey{}).(*ent.Tx)
	if ok {
		return tx.Client()
	}
	return d.Db
}

// buildDb 构建db client
func buildDb(c *conf.Data, hLog *log.Helper, rdb *redis.Client) (*ent.Client, error) {
	// 生成 Db
	drv, err := sql.Open(c.Database.GetDriver(), c.Database.GetSource())
	if err != nil {
		fmt.Println("连接db 失败: ", err)
		return nil, err
	}
	drv.DB().SetMaxIdleConns(int(c.Database.MaxIdle))
	drv.DB().SetMaxOpenConns(int(c.Database.MaxOpen))
	drv.DB().SetConnMaxLifetime(c.Database.MaxLifetime.AsDuration())

	var client *ent.Client
	if c.Database.UseEntcache {
		cacheDrv := entcache.NewDriver(
			drv,
			entcache.TTL(c.Database.EntcacheTTL.AsDuration()),
			entcache.Levels(entcache.NewRedis(rdb)),
		)
		client = ent.NewClient(ent.Driver(cacheDrv))
	} else {
		client = ent.NewClient(ent.Driver(drv))
	}

	// 关闭默认启用的 select distinct
	client.Intercept(
		intercept.Func(func(ctx context.Context, q intercept.Query) error {
			// Skip setting Unique if the modifier was set explicitly.
			if ent2.QueryFromContext(ctx).Unique == nil {
				q.Unique(false)
			}
			return nil
		}),
	)
	if c.Database.IsDebug {
		client = client.Debug()
	}
	return client, nil
}

// nolint
// buildRdb 构建redis client
func buildRdb(c *conf.Data, hLog *log.Helper) *redis.Client {
	if c == nil || c.Redis == nil {
		return nil
	}
	rdb := redis.NewClient(&redis.Options{
		Addr:            c.Redis.Addr,
		Password:        c.Redis.Password,
		ReadTimeout:     c.Redis.ReadTimeout.AsDuration(),
		WriteTimeout:    c.Redis.WriteTimeout.AsDuration(),
		PoolSize:        int(c.Redis.PoolSize),
		MinIdleConns:    int(c.Redis.MinIdleConns),
		ConnMaxIdleTime: c.Redis.ConnMaxIdleTime.AsDuration(),
		DB:              0,
	})
	// 此时并没有发起连接，在使用时才会
	return rdb
}

// buildMqProducer rocket producer
func buildMqProducer(c *conf.EventRocketMQ, hLog *log.Helper) (*event.Producer, error) {
	if c == nil {
		return nil, nil
	}
	credentials := primitive.Credentials{
		AccessKey: c.AccessKey,
		SecretKey: c.SecretKey,
	}
	p, err := event.NewProducer(c.Addr, producer.WithCredentials(credentials))
	if err != nil {
		fmt.Println("创建 rocketMQ producer 失败: ", err)
		return nil, err
	}
	err = p.Start()
	if err != nil {
		fmt.Println("rocketMQ producer start 失败: ", err)
		return nil, err
	}
	// 此时并没有发起连接，在使用时才会
	return p, nil
}
