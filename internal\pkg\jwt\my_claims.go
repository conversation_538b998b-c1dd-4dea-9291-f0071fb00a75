package jwt

import (
	"strconv"

	"ccb/api/myerr"
	"ccb/internal/biz/valobj"
	jwtV4 "github.com/golang-jwt/jwt/v4"
)

// MyClaims jwt 自定义 Claims
type MyClaims struct {
	jwtV4.RegisteredClaims

	Account  string
	Username string
	Status   valobj.AdminUserStatus
	RoleType valobj.AdminUserRoleType // 预留字段
}

// GetID 返回MyClaims的int类型ID
func (m *MyClaims) GetID() int {
	id, err := strconv.Atoi(m.ID)
	if err != nil {
		panic(myerr.ErrorException("无效的登录的id：%s", err))
	}
	return id
}
