package helper

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
)

func TestSliceConvertSlice(t *testing.T) {
	type testCase[T interface {
		int | int32 | int64 | int8 | string
	}, O interface{ int | int32 | int64 | int8 }] struct {
		name string
		args []T
		want []O
	}
	intTo32Test := testCase[int, int32]{name: "int 到 int32", args: []int{1, 2, 3}, want: []int32{1, 2, 3}}
	intTo64Test := testCase[int, int64]{name: "int 到 int64", args: []int{1, 2, 3}, want: []int64{1, 2, 3}}
	int32ToIntTest := testCase[int32, int]{name: "int32 到 int", args: []int32{1, 2, 3}, want: []int{1, 2, 3}}
	stingToIntTest := testCase[string, int]{name: "string 到 int", args: []string{"1", "abc", "3"}, want: []int{1, 3}}

	convey.Convey("整数互转", t, func() {
		convey.Convey("int 到 int32", func() {
			convey.So(SliceConvertSlice[int32, int](intTo32Test.args), convey.ShouldResemble, intTo32Test.want)
		})
		convey.Convey("int 到 int64", func() {
			convey.So(SliceConvertSlice[int64, int](intTo64Test.args), convey.ShouldResemble, intTo64Test.want)
		})
		convey.Convey("int32 到 int", func() {
			convey.So(SliceConvertSlice[int, int32](int32ToIntTest.args), convey.ShouldResemble, int32ToIntTest.want)
		})
	})
	convey.Convey("字符到整型", t, func() {
		convey.Convey("string 到 int", func() {
			convey.So(SliceConvertSlice[int, string](stingToIntTest.args), convey.ShouldResemble, stingToIntTest.want)
		})
	})
}
