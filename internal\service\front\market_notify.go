package front

import (
	"ccb/internal/biz"
	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/conf"
	"io"
	"net/url"
	"strconv"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type MarketNotifyService struct {
	logger    *log.Helper
	c         *conf.Bootstrap
	marketBiz *biz.MarketBiz
}

func NewMarketNotifyService(logger *log.Helper, c *conf.Bootstrap, marketBiz *biz.MarketBiz) *MarketNotifyService {
	return &MarketNotifyService{logger: logger, c: c, marketBiz: marketBiz}
}

func (srv *MarketNotifyService) CouponNotify(ctx http.Context) error {
	body, _ := io.ReadAll(ctx.Request().Body)
	defer ctx.Request().Body.Close()
	srv.logger.Infof("market coupon notify body: %s", string(body))
	if strings.HasPrefix(string(body), "event") {
		verifyReq := new(bo.MarketCouponVerifyNotifyReq)
		formMap, _ := url.ParseQuery(string(body))
		codeStatus, _ := strconv.Atoi(formMap.Get("codeStatus"))
		merchantId, _ := strconv.Atoi(formMap.Get("merchantId"))
		planId, _ := strconv.Atoi(formMap.Get("planId"))
		keyBatchId, _ := strconv.Atoi(formMap.Get("keyBatchId"))
		usageTime, _ := strconv.Atoi(formMap.Get("usageTime"))
		channel, _ := strconv.Atoi(formMap.Get("channel"))
		verifyReq = &bo.MarketCouponVerifyNotifyReq{
			Event:      formMap.Get("event"),
			CodeStatus: codeStatus,
			MerchantId: merchantId,
			CardCode:   formMap.Get("cardCode"),
			Url:        formMap.Get("url"),
			PlanId:     planId,
			KeyBatchId: keyBatchId,
			Account:    formMap.Get("account"),
			UsageTime:  usageTime,
			CouponId:   formMap.Get("couponId"),
			StockId:    formMap.Get("stockId"),
			TradeNo:    formMap.Get("tradeNo"),
			Channel:    channel,
			Sign:       formMap.Get("sign"),
		}
		if verifyReq.Event == "WECHAT_COUPON.USED" { //立减金核销
			err := srv.marketBiz.CouponVerifyNotify(verifyReq)
			if err != nil {
				srv.logger.Errorf("market coupon verify notify error: %v", err)
				ctx.String(200, err.Error())
				return nil
			}
		}
		ctx.String(200, "ok")
	} else {
		reqBo := new(bo.MarketCouponReceiveNotifyReq)
		formMap, _ := url.ParseQuery(string(body))
		reqBo = &bo.MarketCouponReceiveNotifyReq{
			TradeNo:      formMap.Get("tradeNo"),
			VoucherId:    formMap.Get("voucherId"),
			VoucherCode:  formMap.Get("voucherCode"),
			CnclSt:       formMap.Get("cnclSt"),
			RedeemResult: formMap.Get("redeemResult"),
			MrchntNo:     formMap.Get("mrchntNo"),
			Sign:         formMap.Get("sign"),
		}
		srv.logger.Infof("market coupon notify form: %v", reqBo)
		if reqBo.VoucherId == "" || reqBo.VoucherCode == "" || reqBo.MrchntNo == "" || reqBo.TradeNo == "" {
			return ctx.JSON(200, map[string]string{
				"code": "400",
				"msg":  "参数错误",
			})
		}
		err := srv.marketBiz.CouponReceiveNotify(reqBo)
		if err != nil {
			srv.logger.Errorf("coupon verify notify error: %s", err)
			return ctx.JSON(200, do.MarketNotifyResp{
				Code:    "4000",
				Msg:     err.Error(),
				TradeNo: reqBo.TradeNo,
			})
		}
		return ctx.JSON(200, do.MarketNotifyResp{
			Code:    "0000",
			Msg:     "ok",
			TradeNo: reqBo.TradeNo,
		})
	}
	return nil
}

func (srv *MarketNotifyService) CouponIgnore(ctx http.Context) error {
	return ctx.JSON(200, do.MarketNotifyResp{
		Code:    "0000",
		Msg:     "ok",
		TradeNo: "",
	})
}

func (srv *MarketNotifyService) CouponCancel(ctx http.Context) error {
	return ctx.JSON(200, do.MarketNotifyResp{
		Code:    "0000",
		Msg:     "ok",
		TradeNo: "",
	})
}
