package rpc

import (
	"ccb/internal/biz/do"

	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
)

type YmtRpc interface {
	GetKey(in *key.OrderRequest, ymtKey string) (rsp do.OrderResponse, err error)
	Notify(req *key.Notify, ymtKey string) (*key.NotifyData, error)
	Query(outBizNo, ymtKey string) (rsp do.OrderResponse, err error)
	Discard(outBizNo, ymtKey string) (rsp do.OrderResponse, err error)
}
