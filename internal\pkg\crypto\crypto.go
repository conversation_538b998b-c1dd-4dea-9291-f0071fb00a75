package crypto

import (
	"crypto/cipher"
	"crypto/rand"
	"errors"
	"fmt"
	"io"
)

type Cipher struct {
	GroupMode  GroupMode
	FillMode   FillMode
	DecodeType int
	Key        []byte
	Iv         []byte
	Output     CipherText
}

func (c *Cipher) DataEncrypt(block cipher.Block, plainData []byte) (err error) {
	c.Output = make([]byte, len(plainData))
	if len(c.Iv) < 1 && block.BlockSize() <= len(c.Key) {
		c.Iv = c.Key[:block.BlockSize()]
	}
	switch c.GroupMode {
	case CFBMode:
		cipher.NewCFBEncrypter(block, c.Iv).XORKeyStream(c.Output, plainData)
	case CTRMode:
		cipher.NewCTR(block, c.Iv).XORKeyStream(c.Output, plainData)
	case OFBMode:
		cipher.NewOFB(block, c.Iv).XORKeyStream(c.Output, plainData)
	case CBCMode:
		cipher.NewCBCEncrypter(block, c.Iv).CryptBlocks(c.Output, plainData)
	case ECBMode:
		c.NewECBEncrypter(block, plainData)
	case GCMMode:
		err = c.NewGCMEncrypter(block, plainData)
	default: // todo
		return
	}
	return
}

func (c *Cipher) DataDecrypt(block cipher.Block, cipherData []byte) (err error) {
	if len(c.Iv) < 1 && block.BlockSize() <= len(c.Key) {
		c.Iv = c.Key[:block.BlockSize()]
	}
	c.Output = make([]byte, len(cipherData))
	switch c.GroupMode {
	case CFBMode:
		cipher.NewCFBDecrypter(block, c.Iv).XORKeyStream(c.Output, cipherData)
	case CTRMode:
		cipher.NewCTR(block, c.Iv).XORKeyStream(c.Output, cipherData)
	case OFBMode:
		cipher.NewOFB(block, c.Iv).XORKeyStream(c.Output, cipherData)
	case CBCMode:
		cipher.NewCBCDecrypter(block, c.Iv).CryptBlocks(c.Output, cipherData)
	case ECBMode:
		c.NewECBDecrypter(block, cipherData)
	case GCMMode:
		err = c.NewGCMDecrypter(block, cipherData)
	default: // todo
		return
	}
	return
}

func (c *Cipher) Value() []byte {
	switch c.DecodeType {
	case PrintBase64:
		return []byte(c.Output.base64Encode())
	case PrintHex:
		return []byte(c.Output.hexEncode())
	default:
		return c.Output
	}
}

func (c *Cipher) Decode(cipherText string) ([]byte, error) {
	switch c.DecodeType {
	case PrintHex:
		return hexDecode(cipherText)
	case PrintBase64:
		return base64Decode(cipherText)
	default:
		return []byte(cipherText), nil
	}
}

func (c *Cipher) Fill(plainText []byte, blockSize int) []byte {
	if c.FillMode == PkcsZero {
		return c.FillMode.zeroPadding(plainText, blockSize)
	} else {
		return c.FillMode.pkcsPadding(plainText, blockSize)
	}
}

func (c *Cipher) UnFill(plainText []byte) (data []byte, err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			err, ok = r.(error)
			if !ok {
				err = fmt.Errorf("%v", r)
			}
		}
	}()
	if c.FillMode == Pkcs7 {
		return c.FillMode.pkcsUnPadding(plainText), nil
	} else if c.FillMode == PkcsZero {
		return c.FillMode.unZeroPadding(plainText), nil
	} else {
		return nil, errors.New("unsupported fill mode")
	}
}

func (c *Cipher) NewECBEncrypter(block cipher.Block, plainData []byte) {
	tempText := c.Output
	for len(plainData) > 0 {
		block.Encrypt(tempText, plainData[:block.BlockSize()])
		plainData = plainData[block.BlockSize():]
		tempText = tempText[block.BlockSize():]
	}
}

func (c *Cipher) NewECBDecrypter(block cipher.Block, cipherData []byte) {
	tempText := c.Output
	for len(cipherData) > 0 {
		block.Decrypt(tempText, cipherData[:block.BlockSize()])
		cipherData = cipherData[block.BlockSize():]
		tempText = tempText[block.BlockSize():]
	}
}

func (c *Cipher) NewGCMEncrypter(block cipher.Block, plainData []byte) error {
	if len(plainData) > 0 {
		aesGcm, err := cipher.NewGCM(block)
		if err != nil {
			return err
		}
		nonce := make([]byte, aesGcm.NonceSize())
		if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
			return err
		}
		c.Output = aesGcm.Seal(nonce, nonce, plainData, nil)
		return nil
	}
	return errors.New("plaintext is null")
}

func (c *Cipher) NewGCMDecrypter(block cipher.Block, plainData []byte) error {
	if len(plainData) < 12 {
		return errors.New("cipherByte is error")
	}
	for len(plainData) > 0 {
		nonce, plainData := plainData[:12], plainData[12:]
		aesGcm, err := cipher.NewGCM(block)
		if err != nil {
			return err
		}
		c.Output, err = aesGcm.Open(nil, nonce, plainData, nil)
		if err != nil {
			return err
		}
		return nil
	}
	return errors.New("plaintext is null")
}
