package jwt

import (
	"context"
	"crypto/rsa"
	"strconv"
	"strings"
	"time"

	"ccb/api/myerr"
	"ccb/internal/biz/do"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt/v4"
)

type JWT struct {
	logger *log.Helper
	c      *conf.Bootstrap

	rasPublic  *rsa.PublicKey
	rasPrivate *rsa.PrivateKey
}

const (
	TokenIssuer = "ccb"
)

func NewJWT(logger *log.Helper, c *conf.Bootstrap) *JWT {
	publicKey, err := jwt.ParseRSAPublicKeyFromPEM([]byte(c.Jwt.GetPublicKey()))
	if err != nil {
		logger.Error("解析 jwt 公钥失败", err)
		panic(err)
	}
	privateKey, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(c.Jwt.GetPrivateKey()))
	if err != nil {
		logger.Error("解析 jwt 私钥失败", err)
		panic(err)
	}
	return &JWT{logger: logger, c: c, rasPublic: publicKey, rasPrivate: privateKey}
}

// GenerateToken 生成后台登录 token
func (j *JWT) GenerateToken(userDo *do.AdminUserDo) (string, error) {
	return j.generate(userDo, TokenIssuer)
}

func (j *JWT) generate(userDo *do.AdminUserDo, issuer string) (string, error) {
	var ttl time.Duration
	if j.c != nil {
		ttl = j.c.Jwt.GetTimeout().AsDuration()
	} else {
		// 测试
		ttl = 30 * time.Hour
	}

	timeout := time.Now().Add(ttl)
	my := MyClaims{
		Account:  userDo.Account,
		Username: userDo.Username,
		Status:   userDo.Status,
		RoleType: userDo.RoleType,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        strconv.Itoa(userDo.ID),
			ExpiresAt: jwt.NewNumericDate(timeout),
			Issuer:    issuer,
		},
	}
	method := jwt.GetSigningMethod("RS256")
	claims := jwt.NewWithClaims(method, my)
	signedString, err := claims.SignedString(j.rasPrivate)
	if err != nil {
		return "", myerr.ErrorException("生成登录token失败:%s", err)
	}
	return signedString, nil
}

// ParseToken 解析登录 token
func (j *JWT) ParseToken(reqToken string) (*MyClaims, error) {
	if reqToken == "" {
		return nil, myerr.ErrorNotLogin("未登录")
	}
	claims := &MyClaims{}
	token, err := jwt.ParseWithClaims(reqToken, claims, func(token *jwt.Token) (interface{}, error) {
		return j.rasPublic, nil
	})
	if err != nil {
		// 过期验证也会不通过
		return nil, myerr.ErrorNotLogin("登录超时，请重新登录")
	}
	if !token.Valid {
		return nil, myerr.ErrorNotLogin("无效的登录凭证，请重新登录")
	}
	if claims.GetID() <= 0 {
		return nil, myerr.ErrorNotLogin("无效的登录ID，请重新登录")
	}
	if claims.Issuer != TokenIssuer {
		return nil, myerr.ErrorNotLogin("无效的登录来源，请重新登录")
	}

	return claims, nil
}

// GetLoginClaim 获取 jwt 中存储信息
func (j *JWT) GetLoginClaim(ctx context.Context) (*MyClaims, error) {
	if header, ok := transport.FromServerContext(ctx); ok {
		auths := strings.SplitN(header.RequestHeader().Get("Authorization"), " ", 2)
		if len(auths) != 2 || !strings.EqualFold(auths[0], "Bearer") {
			panic(myerr.ErrorNotLogin("token 格式错误，无 Bearer 头"))
		}
		jwtToken := auths[1]
		return j.ParseToken(jwtToken)
	}
	panic(myerr.ErrorNotLogin("无登录信息"))
}

// GetLoginUserIdX 获取登录的用户 Id
func (j *JWT) GetLoginUserIdX(ctx context.Context) int {
	claim, err := j.GetLoginClaim(ctx)
	if err != nil {
		panic(myerr.ErrorNotLogin("未登录：%s", err))
	}
	userId, err := strconv.Atoi(claim.ID)
	if err != nil {
		panic(myerr.ErrorNotLogin("无效的登录的id：%s", err))
	}
	return userId
}
