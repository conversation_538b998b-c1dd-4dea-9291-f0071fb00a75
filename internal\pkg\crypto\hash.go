package crypto

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"strings"
)

func Sha256Sign(data []byte) string {
	hash := sha256.New()
	hash.Write(data)
	hashed := hash.Sum(nil)
	return strings.ToUpper(hex.EncodeToString(hashed))
}

func Sha256WithRsaVerify(data, signature string, pubKey *rsa.PublicKey) error {
	hashed := sha256.Sum256([]byte(data))
	decodedBytes, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return err
	}
	return rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, hashed[:], decodedBytes)
}

func Sha256WithRsaSign(data []byte, priKey *rsa.PrivateKey) (string, error) {
	hashed := sha256.Sum256(data)
	txt, err := rsa.SignPKCS1v15(rand.<PERSON>, pri<PERSON>ey, crypto.SHA256, hashed[:])
	// return strings.ToUpper(hex.EncodeToString(txt)), err
	return base64.StdEncoding.EncodeToString(txt), err
}
