package wechatrepositoryimpl

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/wechatrepository"
	"ccb/internal/conf"
	"ccb/internal/data"
	"ccb/internal/pkg/request"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/cashcoupons"
)

// CpnRepoImpl .
type CpnRepoImpl struct {
	bc      *conf.Bootstrap
	wechat  *data.Wechat
	options *request.Options
}

func NewCpnRepoImpl(bc *conf.Bootstrap, wechat *data.Wechat) wechatrepository.WechatCpnRepo {
	h := http.Header{
		"Content-Type": []string{"application/json"},
	}
	hc := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,              // 最大空闲连接数
			MaxIdleConnsPerHost: 20,               // 每个主机的最大空闲连接数
			IdleConnTimeout:     30 * time.Second, // 空闲连接超时时间
		},
	}

	isSuccess := func(code int) bool {
		return code == http.StatusOK
	}

	return &CpnRepoImpl{
		bc:      bc,
		wechat:  wechat,
		options: request.NewOptions(request.WithHeaders(h), request.WithHttpClient(hc), request.WithStatusCodeFunc(isSuccess)),
	}
}

func (c *CpnRepoImpl) bodyErr(_ context.Context, reqErr error, result *core.APIResult) error {
	if result == nil {
		return reqErr
	}

	if result.Response.Body == nil {
		return reqErr
	}

	bodyBytes, err := io.ReadAll(result.Response.Body)
	if err != nil {
		return fmt.Errorf(fmt.Sprintf("读取微信body错误:%s", err.Error()))
	}

	if bodyBytes == nil {
		return err
	}

	var errBody *ErrBody
	if err = json.Unmarshal(bodyBytes, &errBody); err != nil {
		return fmt.Errorf("微信错误返回body内容解析错误,body[%s]err[%s]", string(bodyBytes), err.Error())
	}

	if len(errBody.Message) == 0 {
		errBody.Message = reqErr.Error()
	}

	return errBody.GetWechatError()
}

func (c *CpnRepoImpl) SendCoupon(ctx context.Context, req *bo.WechatSendCouponBo) (couponId string, err error) {
	if req.StockId == "JXJKCS000002" {
		return strconv.Itoa(time.Now().Nanosecond()), err
	}
	client, err := c.wechat.Get(req.StockSendMchId)
	if err != nil {
		return "", err
	}

	svc := cashcoupons.CouponApiService{Client: client}

	resp, result, err := svc.SendCoupon(ctx, cashcoupons.SendCouponRequest{
		OutRequestNo: core.String(req.OutRequestNo),
		// 微信为发券方商户分配的公众账号ID，接口传入的所有appid应该为公众号的appid（在mp.weixin.qq.com申请的），不能为APP的appid（在open.weixin.qq.com申请的）。
		Appid:             core.String(req.Appid),
		Openid:            core.String(req.Openid),
		StockId:           core.String(req.StockId),
		StockCreatorMchid: core.String(req.StockCreatorMchId),
	})
	if err != nil {
		return "", c.bodyErr(ctx, err, result)
	}

	return *resp.CouponId, nil
}

func (c *CpnRepoImpl) QueryCoupon(ctx context.Context, req *bo.WechatQueryCouponBo) (*cashcoupons.Coupon, error) {
	client, err := c.wechat.Get(req.StockSendMchId)
	if err != nil {
		return nil, err
	}

	svc := cashcoupons.CouponApiService{Client: client}

	resp, result, err := svc.QueryCoupon(ctx, cashcoupons.QueryCouponRequest{
		CouponId: core.String(req.CouponId),
		Appid:    core.String(req.Appid),
		Openid:   core.String(req.Openid),
	})
	if err != nil {
		return nil, c.bodyErr(ctx, err, result)
	}

	return resp, nil
}

func (c *CpnRepoImpl) QueryStock(ctx context.Context, sendMchId, stockCreatorMchId, stockId string) (*cashcoupons.Stock, error) {
	if stockCreatorMchId == "" {
		return nil, fmt.Errorf("商户号不能为空")
	}

	if stockId == "" {
		return nil, fmt.Errorf("批次号不能为空")
	}

	client, err := c.wechat.Get(sendMchId)
	if err != nil {
		return nil, err
	}

	req := cashcoupons.QueryStockRequest{
		StockId:           core.String(stockId),
		StockCreatorMchid: core.String(stockCreatorMchId),
	}

	svc := cashcoupons.StockApiService{Client: client}
	response, result, err := svc.QueryStock(ctx, req)
	if err != nil {
		return nil, c.bodyErr(ctx, err, result)
	}

	return response, nil
}

func (c *CpnRepoImpl) RegisterNotifyTag(ctx context.Context, stockId string) ([]byte, error) {
	// 注册“刚哥那边”回调中心tag,一个批次只能注册一次，消费时根据不同的tag消费(区分测试/正式注册tag处理)
	b := struct {
		TagName string `json:"tag_name"`
		StockId string `json:"stock_id"`
	}{
		TagName: c.bc.WechatNotifyMQ.Tag,
		StockId: stockId,
	}

	body, err := json.Marshal(b)
	if err != nil {
		return nil, err
	}

	_, respBody, err := request.POST(
		ctx,
		c.bc.WechatNotifyMQ.RegisterTagUrl,
		body,
		c.options,
	)
	if err != nil {
		return nil, err
	}

	return respBody, nil
}
