package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type CouponDo struct {
	Id            int                 `json:"id"`
	Code          string              `json:"code"`
	Link          string              `json:"link"`
	OrderId       int                 `json:"order_id"`
	MerchantId    string              `json:"merchant_id"`
	ProductId     int                 `json:"product_id"`
	ProductCode   string              `json:"product_code"`
	Status        valobj.CouponStatus `json:"status"`
	Total         int                 `json:"total"`
	Residue       int                 `json:"residue"`
	TransactionId string              `json:"transaction_id"`
	ExpireTime    *time.Time          `json:"expire_time"`
	CreateTime    *time.Time          `json:"create_time"`
	UpdateTime    *time.Time          `json:"update_time"`
	Product       *ProductDo
}
