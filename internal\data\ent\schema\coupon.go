// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Coupon struct {
	ent.Schema
}

func (Coupon) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("code").Unique(),
		field.String("link").Optional(),
		field.Int("order_id"),
		field.String("merchant_id"),
		field.Int("product_id"),
		field.String("product_code"),
		field.Int("status").Comment("0 默认 1 可用  2 已使用  3核销 4 作废"),
		field.Int("total").Optional().Comment("总次数"),
		field.Int("residue").Optional().Comment("剩余次数"),
		field.String("transaction_id").Optional().Comment("核销时的订单号(立减金为微信的订单号)"),
		field.Time("expire_time").Optional(),
		field.Time("create_time").Optional(),
		field.Time("update_time").Optional(),
	}
}
func (Coupon) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("coupon_log", CouponLog.Type),
		edge.From("product", Product.Type).Ref("coupon").Unique().Field("product_id").Required(),
	}
}

func (Coupon) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "coupon"}}
}
