package repository

import (
	"context"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/valobj"
)

type OrderRepo interface {
	CreateE(ctx context.Context, d *do.OrderDo) (*do.OrderDo, error)
	UpdateE(ctx context.Context, d *do.OrderDo) (*do.OrderDo, error)
	SaveUpstreamOrderNumber(ctx context.Context, id int, updateOrderId string) error
	UpdateStatus(ctx context.Context, id int, oldStatus, status valobj.OrderStatus) (*do.OrderDo, error)
	FindByOrderNo(ctx context.Context, orderNo string) (*do.OrderDo, error)
	Find(ctx context.Context, id int) (*do.OrderDo, error)
	FindAll(ctx context.Context, reqBo *bo.OrderListBo) ([]*do.OrderDo, error)
}
