package valobj

type WechatCpnStatus string

const (
	WechatCpnStatusSended  WechatCpnStatus = "SENDED"
	WechatCpnStatusUsed    WechatCpnStatus = "USED"
	WechatCpnStatusExpired WechatCpnStatus = "EXPIRED"
)

var WechatCpnStatusMap = map[WechatCpnStatus]string{
	WechatCpnStatusSended:  "可用",
	WechatCpnStatusUsed:    "已实扣",
	WechatCpnStatusExpired: "已过期",
}

func (s WechatCpnStatus) GetText() string {
	if t, ok := WechatCpnStatusMap[s]; ok {
		return t
	}
	return "未知类型"
}

func (s WechatCpnStatus) GetValue() string {
	return string(s)
}

func (s WechatCpnStatus) IsSended() bool {
	return s == WechatCpnStatusSended
}

func (s WechatCpnStatus) IsUsed() bool {
	return s == WechatCpnStatusUsed
}

func (s WechatCpnStatus) IsExpired() bool {
	return s == WechatCpnStatusExpired
}
