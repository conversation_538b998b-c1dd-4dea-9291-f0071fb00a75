package rpc

import (
	"net/http"

	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/api"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/notify"
)

type ZLTXRpc interface {
	// RechargeOrder 直充下单
	RechargeOrder(mrchId string, req *api.RechargeOrderReq) (*api.RechargeOrderResp, error)
	// RechargeQuery 直充查询
	RechargeQuery(mrchId, outTradeNo string) (*api.RechargeQueryResp, error)
	// CardOrder 卡密下单
	CardOrder(mrchId string, req *api.CardOrderReq) (*api.CardOrderResp, error)
	// CardQuery 卡密订单查询
	CardQuery(mrchId, outTradeNo string) (*api.CardQueryResp, error)
	// Product 产品查询
	Product(mrchId string) (*api.RechargeProductResp, error)
	// ParseOrderNotify 订单通知
	ParseOrderNotify(req *http.Request) (*notify.OrderReq, error)
}
