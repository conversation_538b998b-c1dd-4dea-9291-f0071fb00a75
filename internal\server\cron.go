package server

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ccb/internal/conf"
	"ccb/internal/server/crontab"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
)

// CronServer  定时任务
type CronServer struct {
	transport.Server
	cron           *cron.Cron
	log            *log.Helper
	cronJobManager *CronJobManager
	c              *conf.Bootstrap
	ccbCron        *crontab.ReconciliationCron
}

var CronServerWaitGroup = &sync.WaitGroup{}

func NewCronServer(
	log *log.Helper,
	c *conf.Bootstrap,
	ccbCron *crontab.ReconciliationCron,
) *CronServer {
	cronServer := &CronServer{
		log:            log,
		c:              c,
		cronJobManager: &CronJobManager{},
		ccbCron:        ccbCron,
	}
	cronServer.cron = cron.New()
	return cronServer
}

func (c *CronServer) Start(ctx context.Context) error {
	if c.c.Cron.CreateReconciliation != "" {
		var err error
		jobName := "创建对账单"
		err = c.StartJob(c.c.Cron.CreateReconciliation, c.ccbCron, jobName)
		if err != nil {
			fmt.Printf("定时任务启动失败：%s：%+v \n", jobName, err)
			return err
		}
	}
	c.cron.Start()
	return nil
}

// StartJob 启动job
func (c *CronServer) StartJob(spec string, job cron.Job, jobName string) error {
	_, err := c.cron.AddJob(spec, CronJobNew(job, jobName, c.log))
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("定时任务启动失败：%s,错误原因：%s", jobName, err))
	}
	fmt.Printf("定时任务启动成功：%s（%s）\n", jobName, spec)
	return nil
}

type CronJobManager struct {
	job     cron.Job
	jobName string
	log     *log.Helper
}

func CronJobNew(job cron.Job, jobName string, log *log.Helper) *CronJobManager {
	return &CronJobManager{job: job, jobName: jobName, log: log}
}

func (c *CronJobManager) Run() {
	defer func() {
		if r := recover(); r != nil {
			panicErr := errors.New("")
			c.log.Errorf("定时任务方法panic:%s:%+v,%+v", c.jobName, r, panicErr)
		}
	}()
	start := time.Now()
	fmt.Printf("开始执行定时任务：%s\n", c.jobName)
	CronServerWaitGroup.Add(1)
	defer CronServerWaitGroup.Done()

	c.job.Run()

	end := time.Now()
	costTime := end.Sub(start).Seconds()
	fmt.Printf("执行定时任务结束：%s，持续:%f秒\n", c.jobName, costTime)
}

func (c *CronServer) Stop(ctx context.Context) error {
	CronServerWaitGroup.Wait()
	fmt.Println("任务已经关闭!")
	return nil
}
