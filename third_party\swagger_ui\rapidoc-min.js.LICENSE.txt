/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017 <PERSON>
 * MIT license
 */

/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, <PERSON>.
 * Released under the MIT License.
 */

/*!
 * repeat-string <https://github.com/jonschlinkert/repeat-string>
 *
 * Copyright (c) 2014-2015, <PERSON>.
 * Licensed under the MIT License.
 */

/*! https://mths.be/punycode v1.3.2 by @mathias */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */

/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */

/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */

/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */

/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */

/**
* @preserve
* RapiDoc 9.3.5-beta - WebComponent to View OpenAPI docs
* License: MIT
* Repo   : https://github.com/rapi-doc/RapiDoc
* Author : Mrinmoy Majumdar
*/
