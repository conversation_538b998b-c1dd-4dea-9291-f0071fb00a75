syntax = "proto3";
package api.myerr;
option go_package = "ccb/api/myerr;myerr";
import "errors/errors.proto";

enum MyErr {
	// 设置缺省错误码，正常情况，所有业务错误都默认用此状态码
	option (errors.default_code) = 560;

	// 系统panic错误
	SYSTEM_PANIC = 0 [(errors.code) = 599];

	// 未登录，401不能滥用，客户端会拉起登录
	NOT_LOGIN = 1 [(errors.code) = 401];

	// 统一未找到数据的提示，无特殊业务处理的 reason
	DB_NOT_FOUND = 2;

	// 参数错误
	PARAM = 3 [(errors.code) = 400];

	// 不允许
	 NOT_ALLOW = 4;

	// 异常
	EXCEPTION = 5;

	// 未找到应用
	NOT_FOUND_APP = 6;

	// 解析app id失败
	PARSE_APP_ID = 7;

}