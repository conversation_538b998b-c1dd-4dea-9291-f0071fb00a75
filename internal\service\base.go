package service

import (
	"context"

	"ccb/internal/biz/do"
	"ccb/internal/pkg/helper"
	jwt "ccb/internal/pkg/jwt"
	"ccb/internal/server/base"
)

type Base struct {
	*base.BaseService
}

var JWTPkg *jwt.JWT

/*// GetMyClaims 获取登录信息
func (b *Base) GetMyClaims(ctx context.Context) (*jwt.MyClaims, error) {
	return JWTPkg.GetLoginClaim(ctx)
}*/

// GetLoginUserIdX 获取登录的用户 Id
func (b *Base) GetLoginUserIdX(ctx context.Context) int {
	return b.BaseService.GetLoginedUserIdX(ctx)
}

// GenToken 生成 token
func (b *Base) GenToken(userDo *do.AdminUserDo) string {
	return helper.MustString(JWTPkg.GenerateToken(userDo))
}
