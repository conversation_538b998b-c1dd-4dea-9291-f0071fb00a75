package valobj

type OrderStatus int

const (
	OrderStatusWaitRecharge OrderStatus = iota + 1
	OrderStatusRecharging
	OrderStatusCompleted
	OrderStatusRechargeFailed
	OrderStatusRefundIng
	OrderStatusRefundSuccess
	OrderStatusVerify
	OrderStatusExpired
)

var orderStatusMap = map[OrderStatus]string{
	OrderStatusWaitRecharge:   "待充值",
	OrderStatusRecharging:     "充值中",
	OrderStatusCompleted:      "充值成功",
	OrderStatusRechargeFailed: "充值失败",
	OrderStatusRefundIng:      "退款中",
	OrderStatusRefundSuccess:  "退款成功",
	OrderStatusVerify:         "已核销",
	OrderStatusExpired:        "已过期",
}

func (r OrderStatus) GetName() string {
	return orderStatusMap[r]
}

func (r OrderStatus) GetValue() int {
	return int(r)
}

type OrderType int

const (
	OrderTypeDirect OrderType = iota + 1
	OrderTypeCard
)

var orderTypeMap = map[OrderType]string{
	OrderTypeDirect: "直充",
	OrderTypeCard:   "优惠券",
}

func (r OrderType) GetName() string {
	return orderTypeMap[r]
}

func (r OrderType) GetValue() int {
	return int(r)
}

type CCBCouponOrderNotifyStatus int

const (
	CCBCouponOrderNotifyFail CCBCouponOrderNotifyStatus = iota + 2
	CCBCouponOrderNotifySuccess
)

var ccbCouponOrderNotifyStatusMap = map[CCBCouponOrderNotifyStatus]string{
	CCBCouponOrderNotifySuccess: "成功",
	CCBCouponOrderNotifyFail:    "失败",
}

func (r CCBCouponOrderNotifyStatus) GetName() string {
	return ccbCouponOrderNotifyStatusMap[r]
}

func (r CCBCouponOrderNotifyStatus) GetValue() int {
	return int(r)
}

type CCBCouponOperation string

const (
	CCBCouponOperationUnkown      CCBCouponOperation = ""
	CCBCouponOperationToUsed      CCBCouponOperation = "011019"
	CCBCouponOperationToUnUsed    CCBCouponOperation = "011020"
	CCBCouponOperationToReceived  CCBCouponOperation = "011036"
	CCBCouponOperationToExchanged CCBCouponOperation = "011037"
)

var ccbCouponOperationMap = map[CCBCouponOperation]string{
	CCBCouponOperationToUsed:      "未使用->已使用",
	CCBCouponOperationToUnUsed:    "已使用->未使用",
	CCBCouponOperationToReceived:  "未兑换->已领取",
	CCBCouponOperationToExchanged: "未兑换->已兑换",
}

func (r CCBCouponOperation) GetName() string {
	return ccbCouponOperationMap[r]
}

func (r CCBCouponOperation) GetValue() string {
	return string(r)
}

type CCBCouponUseType string

const (
	CCBCouponUseTypeKey         CCBCouponUseType = "1"
	CCBCouponUseTypeDynamicCode CCBCouponUseType = "4"
	CCBCouponUseTypeStaticCode  CCBCouponUseType = "5"
	CCBCouponUseTypeRecharge    CCBCouponUseType = "6"
)

var ccbCouponUseTypenMap = map[CCBCouponUseType]string{
	CCBCouponUseTypeKey:         "兑换码",
	CCBCouponUseTypeDynamicCode: "动态核销码",
	CCBCouponUseTypeStaticCode:  "静态核销码",
	CCBCouponUseTypeRecharge:    "直接发放",
}

func (r CCBCouponUseType) GetName() string {
	return ccbCouponUseTypenMap[r]
}

func (r CCBCouponUseType) GetValue() string {
	return string(r)
}

type CCBOrderRespStatus string

const (
	CCBOrderRespStatusRechargeing CCBOrderRespStatus = "1" // 处理中
	CCBOrderRespStatusFail        CCBOrderRespStatus = "2" // 处理失败
	CCBOrderRespStatusSuccess     CCBOrderRespStatus = "3" // 处理成功
	CCBOrderRespStatusUnkown      CCBOrderRespStatus = "4" // 不确定
)
