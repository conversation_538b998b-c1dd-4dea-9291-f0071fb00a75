package crypto

import (
	"crypto/des"
	"encoding/base64"
	"errors"
)

// Create3DES<PERSON>ey creates a 3DES key from a base64 encoded string
func Create3DESKey(tpDes<PERSON>ey string) (des.Key, error) {
	// Decode the base64 encoded key
	keyBytes, err := base64.StdEncoding.DecodeString(tpDesKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 key: %v", err)
	}

	// Ensure we have exactly 24 bytes for 3DES
	if len(keyBytes) != 24 {
		return nil, fmt.Errorf("invalid key size: expected 24 bytes, got %d", len(keyBytes))
	}

	// Return the key as a des.Key type
	return keyBytes, nil
}

func TripleDESDecrypt(key, encryptData string) ([]byte, error) {
	// Base64解码
	decodeBase64, err := base64.StdEncoding.DecodeString(encryptData)
	if err != nil {
		return nil, err
	}
	// 使用DES解密
	desKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return nil, err
	}
	block, err := des.NewTripleDESCipher(desKey)
	if err != nil {
		return nil, err
	}
	if len(decodeBase64)%block.BlockSize() != 0 {
		return nil, errors.New("cipher text length not aligned with block size")
	}
	cipherText := make([]byte, len(decodeBase64))
	block.Decrypt(cipherText, decodeBase64)
	return PKCS5UnPadding(cipherText, block.BlockSize())
	return cipherText, nil
}

func PKCS5UnPadding(plainText []byte, blockSize int) ([]byte, error) {
	length := len(plainText)
	number := int(plainText[length-1])
	if number >= length || number > blockSize {
		return nil, errors.New("cipher text length not aligned with block size")
	}
	return plainText[:length-number], nil
}
