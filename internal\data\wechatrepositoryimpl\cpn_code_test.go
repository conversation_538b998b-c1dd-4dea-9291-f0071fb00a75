package wechatrepositoryimpl

import (
	"errors"
	errors2 "github.com/go-kratos/kratos/v2/errors"
	"gorm.io/gorm"
	"testing"
)

func TestGetErrorByDescription(t *testing.T) {
	e := &ErrBody{
		Code:    "INVALID_REQUEST",
		Message: "活动已结束或未激活",
	}
	t.Log(e.GetWechatError())

	err := gorm.ErrRecordNotFound

	t.Log(errors.Is(err, gorm.ErrRecordNotFound))
}

func TestErr(t *testing.T) {
	e := &ErrBody{
		Code:    "INVALID_REQUEST",
		Message: "活动已结束或未激活",
	}

	se := errors2.FromError(e.GetWechatError())

	t.Log(se.Reason)
	t.Log(se.Message)

	e2 := errors.New("活动已结束或未激活")
	se2 := errors2.FromError(e2)

	t.Log(se2.Reason)
	t.Log(len(se2.Reason))
	t.Log(se2.Message)
}
