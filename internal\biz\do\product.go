package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type ProductDo struct {
	ID           int
	Code         string
	Name         string
	Desc         string
	Status       valobj.IsEnable
	Type         valobj.ProductType
	Attr         string
	SendMchId    string
	CreatorMchId string
	FacePrice    valobj.Money
	CostPrice    valobj.Money
	CreatedTime  time.Time
	UpdatedTime  time.Time
}



type CCBProductListResp struct {
	Code      string         `json:"code"`
	Msg       string         `json:"msg"`
	PageSize  int            `json:"pageSize"`
	PageNo    int            `json:"pageNo"`
	Total     int            `json:"total"`
	TotalPage int            `json:"totalPage"`
	Products  []CCBProductDo `json:"products"`
}

type CCBProductDo struct {
	ProductId   string            `json:"productId"`
	ProductName string            `json:"productName"`
	Desc        string            `json:"desc,omitempty"`
	MrchId      string            `json:"mrchId"`           // 产品供应商，默认与渠道Id一致
	FVal        string            `json:"fVal,omitempty"`   // 面额，带两位小数金额
	CstPrc      string            `json:"cstPrc,omitempty"` // 成本价，带两位小数金额
	Attr        string            `json:"attr"`
	UdcrgTm     string            `json:"udcrgTm,omitempty"` // 产品下架（失效）时间，yyyyMMddhhmmss
	Expire      *CCBProductExpire `json:"expire,omitempty"`
	Limit       string            `json:"limit,omitempty"` // 领取数量限制：每日发放上限|每人领取上限|每人每日限制
}

type CCBProductExpire struct {
	/**
	有效期类型
	1 – 固定有效期，比如固定在20230701-20240731才能使用
	2 – 相对有效期A，领取后N天内有效（精准到时分秒）
	3 – 相对有效期B，领取后从当天算起第N+1天零点失效
	4 – 相对有效期C，领取后从明天算起第N+1天零点失效
	9 – 无有效期（由建行权益中台主动作废）
	*/
	ExpireType string `json:"expireType,omitempty"`
	/**
	有效期时间
	固定有效期，起始有效期|截止有效期(yyyyMMddHHmmss)，示例20230701000000|20230731235959
	相对有效期，相对领取权益后的有效天数，直接传入数值，如1、3、7
	*/
	ExpireTm string `json:"expireTm,omitempty"`
}

type CCBProductStockDo struct {
	ProductId  string     `json:"productId"`
	IssuNum    string     `json:"issuNum,omitempty"`    // 业务约定的签约总发行量
	PrpIvntNum string     `json:"prpIvntNum,omitempty"` // 累计备货数量
	AvlIvntNum string     `json:"avlIvntNum"`           // 当前可用的库存余量
	UsedNum    string     `json:"usedNum,omitempty"`    // 累计已核销数量
	UdtTm      *time.Time `json:"udtTm"`                // 库存信息刷新的时间
}

type ProductStockDo struct {
	Id          int                `json:"id"`
	ProductId   string             `json:"product_id"`
	Name        string             `json:"name"`
	Status      int                `json:"status"`
	MerchantId  string             `json:"merchant_id"`
	ProductType valobj.ProductType `json:"product_type"`
	Total       int                `json:"total"`
	Residue     int                `json:"residue"`
	UsedNum     int                `json:"used_num"`
	Attr        string             `json:"attr"`
	CreateTime  *time.Time         `json:"create_time"`
	UpdateTime  *time.Time         `json:"update_time"`
}
