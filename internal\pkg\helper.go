package pkg

import (
	"math/rand"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// GoTry 开启协程记录异常日志
func GoTry(hLog *log.Helper, name string, f func() error) {
	defer func() {
		if err := recover(); err != nil {
			hLog.Error(name, "：协程执行panic：", err)
		}
	}()
	err := f()
	if err != nil {
		hLog.Error(name, "：协程执行err：", err)
	}
}

func RandString(letterLength, numberLength int) string {
	// 设置随机数种子
	rand.Seed(time.Now().UnixNano())

	// 生成字母字符串
	var letters strings.Builder
	for i := 0; i < letterLength-1; i++ {
		letter := byte(rand.Intn(26) + 65) // 生成随机字母 ASCII 码值范围是 65-90
		letters.WriteByte(letter)
	}

	// 生成数字字符串
	var numbers strings.Builder
	for i := 0; i < numberLength; i++ {
		number := byte(rand.Intn(10) + 48) // 生成随机数字 ASCII 码值范围是 48-57
		numbers.WriteByte(number)
	}

	// 返回组合的字符串
	return strings.ToUpper("CCB"+letters.String()) + numbers.String()
}
