package rpc

import (
	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
)

type CCBRpc interface {
	CouponOrderNotify(mrhId string, req *bo.CCBCouponOrderNotifyReq) (*do.CodeMsg, error)
	CouponStatusNotify(mrhId string, reqBo *bo.CCBCouponStatusNotifyReq) (*do.CodeMsg, error)
	RechargeNotify(mrhId string, reqBo *bo.CCBRechargeNotifyReq) (*do.CodeMsg, error)

	VerifySignature(header map[string]string, body []byte) (err error)
	EncryptData(header map[string]string, body []byte) (data []byte, err error)
	DecryptData(header map[string]string, body []byte) (data []byte, err error)
	EncryptCpn(channelId string, body []byte) (data []byte, err error)
	DecryptCpn(channelId string, body []byte) (data []byte, err error)

	ResponseHeader(header map[string]string, data []byte) map[string]string
	SendReconciliation(mrhId, batchNum string, body []byte) (*do.CodeMsg, error)
}
