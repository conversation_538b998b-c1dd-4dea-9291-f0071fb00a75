package wechatrepository

import (
	"context"

	"ccb/internal/biz/bo"

	"github.com/wechatpay-apiv3/wechatpay-go/services/cashcoupons"
)

type WechatCpnRepo interface {
	SendCoupon(ctx context.Context, req *bo.WechatSendCouponBo) (couponId string, err error)
	QueryCoupon(ctx context.Context, req *bo.WechatQueryCouponBo) (*cashcoupons.Coupon, error)
	QueryStock(ctx context.Context, sendMchId, stockCreatorMchId, stockId string) (*cashcoupons.Stock, error)
	RegisterNotifyTag(ctx context.Context, stockId string) ([]byte, error)
}
