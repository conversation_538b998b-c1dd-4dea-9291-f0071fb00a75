// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type CouponLog struct {
	ent.Schema
}

func (CouponLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("coupon_id"),
		field.Int("before_status"),
		field.Int("status"),
		field.String("date_status"),
		field.String("request_data").Optional(),
		field.String("transaction_id").Optional().Comment("核销时的订单号(立减金为微信的订单号)"),
		field.Time("create_time").Optional(),
	}
}
func (CouponLog) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("coupon", Coupon.Type).Ref("coupon_log").Unique().Field("coupon_id").Required(),
	}
}
func (CouponLog) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "coupon_log"}}
}
