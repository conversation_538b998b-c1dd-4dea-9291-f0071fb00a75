package do

import "ccb/internal/biz/valobj"

type AdminUserDo struct {
	ID         int
	Account    string
	Username   string
	Password   string
	Status     valobj.AdminUserStatus
	RoleType   valobj.AdminUserRoleType // 预留字段
	CreateTime int
	UpdateTime int
}

type UnifiedLoginDo struct {
	Authorization string   `json:"authorization"`
	UserInfo      UserInfo `json:"user_info"`
}

type UserInfo struct {
	Id       int    `json:"id"`
	UserName string `json:"username"`
	Account  int    `json:"account"`
}
