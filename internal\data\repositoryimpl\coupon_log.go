package repositoryimpl

import (
	"context"
	"errors"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/data"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/coupon"
	"ccb/internal/data/ent/couponlog"
	"ccb/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

type CouponLogRepoImpl struct {
	Base[ent.CouponLog, do.CouponLogDo, ent.CouponLogQuery]
	data   *data.Data
	ccbrpc rpc.CCBRpc
}

func NewCouponLogRepoImpl(data *data.Data, ccbrpc rpc.CCBRpc) repository.CouponLogRepo {
	return &CouponLogRepoImpl{data: data, ccbrpc: ccbrpc}
}

// ToEntity 转换成实体
func (r *CouponLogRepoImpl) ToEntity(po *ent.CouponLog) *do.CouponLogDo {
	if po == nil {
		return nil
	}
	dc := r.Base.ToEntity(po)
	dc.CreateTime = &po.CreateTime
	return dc
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (r *CouponLogRepoImpl) ToEntities(pos []*ent.CouponLog) []*do.CouponLogDo {
	if pos == nil {
		return nil
	}
	entities := make([]*do.CouponLogDo, len(pos))
	for k, p := range pos {
		entities[k] = r.ToEntity(p)
	}
	return entities
}

func (r *CouponLogRepoImpl) CreateE(ctx context.Context, d *do.CouponLogDo) (*do.CouponLogDo, error) {
	row, err := r.data.GetDb(ctx).CouponLog.Create().
		SetCouponID(d.CouponId).
		SetRequestData(d.RequestData).
		SetBeforeStatus(d.BeforeStatus.GetValue()).
		SetStatus(d.Status.GetValue()).
		SetCreateTime(time.Now()).
		SetDateStatus(d.DateStatus).
		SetTransactionID(d.TransactionId).
		Save(ctx)
	return r.ToEntity(row), err
}

func (r *CouponLogRepoImpl) FindLastLog(ctx context.Context, req *bo.CouponLogBo) (*do.CouponLogDo, error) {
	where := r.setQuery(ctx, req)
	query := r.data.GetDb(ctx).CouponLog.Query().Where(where...)
	row, err := query.Order(ent.Desc(couponlog.FieldID)).First(ctx)
	return r.ToEntity(row), err
}

func (r *CouponLogRepoImpl) FindPageList(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, *bo.RespPageBo, error) {
	where := r.setListQuery(reqBo)
	if len(where) == 0 {
		return nil, nil, errors.New("条件不能为空")
	}
	respPage := new(bo.RespPageBo)
	query := r.data.GetDb(ctx).CouponLog.Query().Where(where...)
	if reqBo.ReqPageBo != nil {
		r.SetPageByBo(query, reqBo.ReqPageBo)
		respPage = r.QueryRespPage(ctx, query, reqBo.ReqPageBo)
	}
	entities, err := query.Order(ent.Asc(couponlog.FieldCreateTime)).All(ctx)
	if err != nil {
		return nil, nil, err
	}
	return r.ToEntities(entities), respPage, nil
}

func (r *CouponLogRepoImpl) FindAll(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, error) {
	where := r.setListQuery(reqBo)
	if len(where) == 0 {
		return nil, errors.New("条件不能为空")
	}
	query := r.data.GetDb(ctx).CouponLog.Query().Where(where...)
	entities, err := query.Order(ent.Asc(couponlog.FieldCreateTime)).All(ctx)
	if err != nil {
		return nil, err
	}
	return r.ToEntities(entities), nil
}

func (r *CouponLogRepoImpl) DayRows(ctx context.Context, reqBo *bo.CouponLogListBo) ([]*do.CouponLogDo, error) {
	rows, err := r.data.GetDb(ctx).QueryContext(ctx, `SELECT cl.*
FROM coupon_log cl
JOIN (
    SELECT coupon_id, MAX(create_time) AS max_create_time
    FROM coupon_log
    WHERE create_time >= ? AND create_time < ?  AND before_status <> status
    GROUP BY coupon_id 
) t ON cl.coupon_id = t.coupon_id
WHERE cl.create_time = t.max_create_time;`, reqBo.BeginDate, reqBo.EndDate)
	if err != nil {
		return nil, err
	}
	ret := make([]*do.CouponLogDo, 0)
	err = sql.ScanSlice(rows, &ret)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (r *CouponLogRepoImpl) setListQuery(reqBo *bo.CouponLogListBo) []predicate.CouponLog {
	where := make([]predicate.CouponLog, 0)
	if reqBo.Code != "" || reqBo.MerchantID != "" {
		predicateCoupon := make([]predicate.Coupon, 0)
		if reqBo.Code != "" {
			if reqBo.MerchantID != "" {
				if val, err := r.ccbrpc.EncryptCpn(reqBo.MerchantID, []byte(reqBo.Code)); err == nil && len(val) > 0 {
					reqBo.Code = string(val)
				}
			}
			predicateCoupon = append(predicateCoupon, coupon.Code(reqBo.Code))
		}
		if reqBo.MerchantID != "" {
			predicateCoupon = append(predicateCoupon, coupon.MerchantID(reqBo.MerchantID))
		}
		where = append(where, couponlog.HasCouponWith(predicateCoupon...))
	}
	if reqBo.BeginDate != nil {
		where = append(where, couponlog.CreateTimeGTE(*reqBo.BeginDate))
	}
	if reqBo.EndDate != nil {
		where = append(where, couponlog.CreateTimeLT(*reqBo.EndDate))
	}
	if len(reqBo.Status) > 0 {
		where = append(where, couponlog.StatusIn(reqBo.Status...))
	}
	return where
}

func (r *CouponLogRepoImpl) setQuery(ctx context.Context, reqBo *bo.CouponLogBo) []predicate.CouponLog {
	where := make([]predicate.CouponLog, 0)
	if reqBo.Id > 0 {
		where = append(where, couponlog.ID(reqBo.Id))
	}
	if reqBo.CouponId > 0 {
		where = append(where, couponlog.CouponIDEQ(reqBo.CouponId))
	}
	if reqBo.Code != "" || reqBo.MerchantID != "" {
		predicateCoupon := make([]predicate.Coupon, 0)
		if reqBo.Code != "" {
			if reqBo.MerchantID != "" {
				if val, err := r.ccbrpc.EncryptCpn(reqBo.MerchantID, []byte(reqBo.Code)); err == nil && len(val) > 0 {
					reqBo.Code = string(val)
				}
			}
			predicateCoupon = append(predicateCoupon, coupon.Code(reqBo.Code))
		}
		if reqBo.MerchantID != "" {
			predicateCoupon = append(predicateCoupon, coupon.MerchantID(reqBo.MerchantID))
		}
		where = append(where, couponlog.HasCouponWith(predicateCoupon...))
	}
	if reqBo.BeginDate != nil {
		where = append(where, couponlog.CreateTimeGTE(*reqBo.BeginDate))
	}
	if reqBo.EndDate != nil {
		where = append(where, couponlog.CreateTimeLT(*reqBo.EndDate))
	}
	if reqBo.Status > 0 {
		where = append(where, couponlog.Status(reqBo.Status))
	}
	return where
}
