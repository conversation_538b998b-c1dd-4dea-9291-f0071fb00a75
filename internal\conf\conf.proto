syntax = "proto3";
package kratos.api;

option go_package = "xa/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Logs logs = 3;
  Alarm alarm = 4;
  Jwt jwt = 5;
  Business business = 6;
  SendSms sendSms = 7;
  Rpc rpc = 8;
  Cron cron = 9;
  EventRocketMQ eventRocketMQ = 10;
  map<string, Channel> ccbMap = 14;
  map<string, Zltx> zltxMap = 15;
  map<string, Market> marketMap = 16;
  map<string, YmtConfig> ymtMap = 17;
  repeated Wechat wechat = 20;
  WechatNotifyMQ wechatNotifyMQ = 21;
  map<string, ProxyConfig> ccbProxyMap = 22;
}

message Cron {
  // 生成对账单
  string createReconciliation = 1;
}

message EventMap {
  string group = 1;
  string topic = 2;
  int32 perCoroutineCnt = 3;
  bool isOpenConsumer = 4;
  repeated string tags = 5;
}

message EventRocketMQ {
  string addr = 1;
  string accessKey = 2;
  string secretKey = 3;
  string secretToken = 4;
  map<string, EventMap> eventMap = 5;
}

message Rpc {
  string attachmentDomain = 1;
}

message Business {
  string passLoginCode = 1;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
    bool isOpenSwagger = 4;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
    string token = 4;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message SendSms {
  message Template {
    string templateCode = 1;
    string signName = 2;
  }
  string accessKeyId = 1;
  string accessKeySecret = 2;
  bool isOpen = 3;
  int64 checkSmsCodeMaxNum = 4;
  Template sendCpnSms = 5;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    int32 maxIdle = 3;
    int32 maxOpen = 4;
    google.protobuf.Duration maxLifetime = 5;
    bool isDebug = 6;
    bool useEntcache = 7;
    google.protobuf.Duration entcacheTTL = 8;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration readTimeout = 3;
    google.protobuf.Duration writeTimeout = 4;
    string password = 5;
    int32 poolSize = 6;
    int32 minIdleConns = 7;
    google.protobuf.Duration connMaxIdleTime = 8;
  }

  message RocketMq {
    repeated string nameServers = 1;
    string productGroup = 2;
    int32 retry = 3;
  }

  Database database = 1;
  Redis redis = 2;
  RocketMq rocketMq = 3;
}

message Logs {
  string business = 1;
  string access = 2;
}

message Alarm {
  string dingToken = 1;
}

message Jwt {
  string jwtKey = 1;
  google.protobuf.Duration timeout = 2;
  string privateKey = 4;
  string publicKey = 5;
}

message Channel {
  string merchantId = 1;
  string platformId = 2;
  string upstreamMerchantId = 3;
  string marketMerchantId = 4;
  string aesKey = 5;
  string publicKey = 6;
  string privateKey = 7;
  string url = 8;
  string ourAppId = 9;
  string ymtMerchantId = 10;
}

message Zltx {
  string merchantId = 1;
  string secretKey = 2;
  bool isProd = 3;
  string notifyUrl = 4;
}

message Market {
  string merchantId = 1;
  string appId = 2;
  string platformId = 3;
  string notifyUrl = 4;
  string merchantPublicKey = 5;
  string publicKey = 6;
  string privateKey = 7;
}

message Wechat {
  string mchID = 1;
  string name = 4;
  string mchCertificateSerialNumber = 2;
  string wechatPayPublicKeyID = 3;
}

message WechatNotifyMQ {
  string accessKeyId = 1;
  string accessKeySecret = 2;
  string endPoint = 3;
  string regionId = 4;
  string instanceId = 5;
  string topic = 6;
  string tag = 7;
  string groupId = 8;
  bool isOpenConsumer = 11;
  string registerTagUrl = 10;
}

message YmtConfig {
  string host = 1; // 地址
  string appId = 2; // 应用ID
  string publicKey = 3; // 公钥
  string privateKey = 4; // 私钥
  string key = 5; // 业务参数key
}

message ProxyConfig {
  string desKey = 1;
  string publicKey = 2; // 公钥
}