package bo

import "ccb/internal/biz/valobj"

type MarketReceiveKeyReq struct {
	AppId       string                   `json:"app_id"`
	MemId       string                   `json:"mem_id"`
	MobileNo    string                   `json:"mobile_no"`
	PosId       string                   `json:"pos_id"`
	ReqSerialNo string                   `json:"req_serial_no"`
	SendMsg     valobj.MarketSendMsgType `json:"send_msg"`
	Timestamp   string                   `json:"timestamp"`
	VoucherId   string                   `json:"voucher_id"`
	VoucherNum  string                   `json:"voucher_num"`
}

type MarketCancelKeyReq struct {
	AppId       string `json:"app_id"`
	MemId       string `json:"mem_id"`
	MobileNo    string `json:"mobile_no"`
	Timestamp   string `json:"timestamp"`
	VoucherCode string `json:"voucher_code"`
	VoucherId   string `json:"voucher_id"`
}

type MarketQueryKeyReq struct {
	AppId       string `json:"app_id"`
	MemId       string `json:"mem_id"`
	MobileNo    string `json:"mobile_no"`
	Timestamp   string `json:"timestamp"`
	VoucherCode string `json:"voucher_code"`
	VoucherId   string `json:"voucher_id"`
}

type MarketCouponReceiveNotifyReq struct {
	TradeNo      string `json:"tradeNo"`
	VoucherId    string `json:"voucherId"`
	VoucherCode  string `json:"voucherCode"`
	CnclSt       string `json:"cnclSt"`
	RedeemResult string `json:"redeemResult"`
	MrchntNo     string `json:"mrchntNo"`
	Sign         string `json:"sign"`
}

type MarketCouponVerifyNotifyReq struct {
	Event      string `json:"event"`
	CodeStatus int    `json:"codeStatus"`
	MerchantId int    `json:"merchantId"`
	CardCode   string `json:"cardCode"`
	Url        string `json:"url"`
	PlanId     int    `json:"planId"`
	KeyBatchId int    `json:"keyBatchId"`
	Account    string `json:"account"`
	UsageTime  int    `json:"usageTime"`
	CouponId   string `json:"couponId"`
	StockId    string `json:"stockId"`
	TradeNo    string `json:"tradeNo"`
	Channel    int    `json:"channel"`
	Sign       string `json:"sign"`
}
