package repositoryimpl

import (
	"ccb/internal/biz/bo"
	"context"
	"time"

	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/valobj"
	"ccb/internal/data"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/order"

	"github.com/pkg/errors"
)

type OrderRepoImpl struct {
	Base[ent.Order, do.OrderDo, ent.OrderQuery]
	data *data.Data
}

func NewOrderRepoImpl(data *data.Data) repository.OrderRepo {
	return &OrderRepoImpl{data: data}
}

// ToEntity 转换成实体
func (o *OrderRepoImpl) ToEntity(po *ent.Order) *do.OrderDo {
	if po == nil {
		return nil
	}
	dc := &do.OrderDo{
		Id:                  po.ID,
		OrderNumber:         po.OrderNumber,
		UpstreamOrderNumber: po.UpstreamOrderNumber,
		UserId:              po.UserID,
		Type:                valobj.OrderType(po.Type),
		MerchantId:          po.MerchantID,
		UpstreamMerchantID:  po.UpstreamMerchantID,
		ProductId:           po.ProductID,
		ProductCode:         po.ProductCode,
		Number:              po.Number,
		TotalFee:            valobj.Money(po.TotalFee),
		Price:               valobj.Money(po.Price),
		Status:              valobj.OrderStatus(po.Status),
		ActivityId:          po.ActivityID,
		PayInfo:             po.PayInfo,
		ExtraData:           po.ExtraData,
		ExpiredTime:         &po.ExpiredTime,
		CreateTime:          &po.CreateTime,
		UpdateTime:          &po.CreateTime,
		OurAppId:            po.OurAppID,
		SendMchId:           po.SendMchID,
		CreatorMchId:        po.CreatorMchID,
		BatchNo:             po.BatchNo,
	}
	return dc
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (o *OrderRepoImpl) ToEntities(pos []*ent.Order) []*do.OrderDo {
	if pos == nil {
		return nil
	}
	entities := make([]*do.OrderDo, len(pos))
	for k, p := range pos {
		entities[k] = o.ToEntity(p)
	}
	return entities
}

// CreateE 创建订单
func (o *OrderRepoImpl) CreateE(ctx context.Context, d *do.OrderDo) (*do.OrderDo, error) {
	query := o.data.GetDb(ctx).Order.Create().
		SetOrderNumber(d.OrderNumber).
		SetUserID(d.UserId).
		SetType(d.Type.GetValue()).
		SetNumber(d.Number).
		SetMerchantID(d.MerchantId).
		SetUpstreamMerchantID(d.UpstreamMerchantID).
		SetPrice(d.Price.GetValue()).
		SetPayInfo(d.PayInfo).
		SetTotalFee(d.TotalFee.GetValue()).
		SetProductID(d.ProductId).
		SetProductCode(d.ProductCode).
		SetStatus(d.Status.GetValue()).
		SetActivityID(d.ActivityId).
		SetOurAppID(d.OurAppId).
		SetSendMchID(d.SendMchId).
		SetCreatorMchID(d.CreatorMchId).
		SetBatchNo(d.BatchNo).
		SetCreateTime(time.Now())
	if d.ExpiredTime != nil {
		query = query.SetExpiredTime(*d.ExpiredTime)
	}
	row, err := query.Save(ctx)
	return o.ToEntity(row), err
}

func (o *OrderRepoImpl) UpdateE(ctx context.Context, d *do.OrderDo) (*do.OrderDo, error) {
	query := o.data.GetDb(ctx).Order.UpdateOneID(d.Id).
		SetUpstreamOrderNumber(d.UpstreamOrderNumber).
		SetPrice(d.Price.GetValue()).
		SetTotalFee(d.TotalFee.GetValue()).
		SetPayInfo(d.PayInfo).
		SetTotalFee(d.TotalFee.GetValue()).
		SetStatus(d.Status.GetValue()).
		SetOurAppID(d.OurAppId).
		SetSendMchID(d.SendMchId).
		SetCreatorMchID(d.CreatorMchId).
		SetBatchNo(d.BatchNo).
		SetUpdateTime(time.Now())
	if d.ExpiredTime != nil {
		if d.ExpiredTime.Unix() > 0 {
			query = query.SetExpiredTime(*d.ExpiredTime)
		}
	}
	row, err := query.Save(ctx)
	return o.ToEntity(row), err
}

func (o *OrderRepoImpl) SaveUpstreamOrderNumber(ctx context.Context, id int, updateOrderId string) error {
	ctn, err := o.data.GetDb(ctx).Order.Update().Where(order.ID(id)).
		SetUpstreamOrderNumber(updateOrderId).
		SetUpdateTime(time.Now()).
		Save(ctx)
	if ctn == 0 {
		return errors.Errorf("SaveUpstreamOrderNumber 更新条数为0 :%d", id)
	}
	return err
}

func (o *OrderRepoImpl) UpdateStatus(ctx context.Context, id int, oldStatus, status valobj.OrderStatus) (*do.OrderDo, error) {
	row, err := o.data.GetDb(ctx).Order.UpdateOneID(id).
		Where(order.StatusEQ(oldStatus.GetValue())).
		SetStatus(status.GetValue()).
		SetUpdateTime(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return o.ToEntity(row), nil
}

func (o *OrderRepoImpl) FindByOrderNo(ctx context.Context, orderNo string) (*do.OrderDo, error) {
	row, err := o.data.GetDb(ctx).Order.Query().
		Where(order.OrderNumberEQ(orderNo)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return o.ToEntity(row), nil
}

func (o *OrderRepoImpl) Find(ctx context.Context, id int) (*do.OrderDo, error) {
	row, err := o.data.GetDb(ctx).Order.Query().
		Where(order.ID(id)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return o.ToEntity(row), nil
}

func (o *OrderRepoImpl) FindAll(ctx context.Context, reqBo *bo.OrderListBo) ([]*do.OrderDo, error) {
	q := o.data.GetDb(ctx).Order.Query()
	if reqBo.MerchantId != "" {
		q = q.Where(order.MerchantIDEQ(reqBo.MerchantId))
	}
	if len(reqBo.ProductIds) > 0 {
		q = q.Where(order.ProductIDIn(reqBo.ProductIds...))
	}
	if reqBo.StartTime != nil {
		q = q.Where(order.CreateTimeGTE(*reqBo.StartTime))
	}
	if reqBo.EndTime != nil {
		q = q.Where(order.CreateTimeLTE(*reqBo.EndTime))
	}
	rows, err := q.All(ctx)
	if err != nil {
		return nil, err
	}
	return o.ToEntities(rows), err
}
