package repositoryimpl

import (
	"context"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/valobj"
	"ccb/internal/data"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/product"
)

var productRepoImpl = ProductRepoImpl{}

type ProductRepoImpl struct {
	Base[ent.Product, do.ProductDo, ent.ProductQuery]
	data *data.Data
}

// NewProductRepoImpl 创建 ProductRepo的实现者
func NewProductRepoImpl(data *data.Data) repository.ProductRepo {
	return &ProductRepoImpl{data: data}
}

// ToEntity 转换成实体
func (p *ProductRepoImpl) ToEntity(po *ent.Product) *do.ProductDo {
	if po == nil {
		return nil
	}
	return &do.ProductDo{
		ID:           po.ID,
		Code:         po.Code,
		Name:         po.Name,
		Desc:         po.Desc,
		Status:       valobj.IsEnable(po.Status),
		Type:         valobj.ProductType(po.Type),
		Attr:         po.Attr,
		FacePrice:    valobj.Money(po.FacePrice),
		CostPrice:    valobj.Money(po.CostPrice),
		SendMchId:    po.SendMchID,
		CreatorMchId: po.CreatorMchID,
		CreatedTime:  po.CreatedTime,
		UpdatedTime:  po.UpdatedTime,
	}
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (p *ProductRepoImpl) ToEntities(pos []*ent.Product) []*do.ProductDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.ProductDo, len(pos))
	for k, po := range pos {
		entities[k] = p.ToEntity(po)
	}
	return entities
}

// GetE 通过 id 获取一条数据
func (p *ProductRepoImpl) GetE(ctx context.Context, id int) (*do.ProductDo, error) {
	row, err := p.data.GetDb(ctx).Product.Query().Where(product.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return p.ToEntity(row), nil
}

// FindE 通过多个 id 获取多条数据
func (p *ProductRepoImpl) FindE(ctx context.Context, ids ...int) ([]*do.ProductDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := p.data.GetDb(ctx).Product.Query().Where(product.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return p.ToEntities(rows), nil
}

// CreateE 创建数据
func (p *ProductRepoImpl) CreateE(ctx context.Context, creatData *do.ProductDo) (*do.ProductDo, error) {
	row, err := p.data.GetDb(ctx).Product.Create().
		SetCode(creatData.Code).
		SetName(creatData.Name).
		SetDesc(creatData.Desc).
		SetStatus(creatData.Status.GetValue()).
		SetType(creatData.Type.GetValue()).
		SetAttr(creatData.Attr).
		SetFacePrice(creatData.FacePrice.GetValue()).
		SetCostPrice(creatData.CostPrice.GetValue()).
		SetCreatedTime(creatData.CreatedTime).
		SetUpdatedTime(creatData.UpdatedTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return p.ToEntity(row), nil
}

// CreateBulkE 批量创建数据
func (p *ProductRepoImpl) CreateBulkE(ctx context.Context, dos []*do.ProductDo) ([]*do.ProductDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent.ProductCreate, len(dos))
	for i, item := range dos {
		values[i] = p.data.GetDb(ctx).Product.Create().
			SetCode(item.Code).
			SetName(item.Name).
			SetDesc(item.Desc).
			SetStatus(item.Status.GetValue()).
			SetType(item.Type.GetValue()).
			SetAttr(item.Attr).
			SetFacePrice(item.FacePrice.GetValue()).
			SetCostPrice(item.CostPrice.GetValue())
	}
	rows, err := p.data.GetDb(ctx).Product.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return p.ToEntities(rows), nil
}

// UpdateE 更新数据
func (p *ProductRepoImpl) UpdateE(ctx context.Context, updateData *do.ProductDo) (int, error) {
	cnt, err := p.data.GetDb(ctx).Product.Update().Where(product.ID(updateData.ID)).
		SetCode(updateData.Code).
		SetName(updateData.Name).
		SetDesc(updateData.Desc).
		SetStatus(updateData.Status.GetValue()).
		SetType(updateData.Type.GetValue()).
		SetAttr(updateData.Attr).
		SetFacePrice(updateData.FacePrice.GetValue()).
		SetCostPrice(updateData.CostPrice.GetValue()).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

// SearchListE 搜索列表
func (p *ProductRepoImpl) SearchListE(ctx context.Context, reqBo *bo.ReqPageBo) (dos []*do.ProductDo, respPage *bo.RespPageBo, err error) {
	q := p.data.GetDb(ctx).Product.Query()
	p.SetPageByBo(q, reqBo)
	if reqBo != nil {
		respPage = p.QueryRespPage(ctx, q, reqBo)
	}
	pos, err := q.All(ctx)
	if err != nil {
		return nil, nil, err
	}
	dos = p.ToEntities(pos)
	return
}
