package rpcimpl

import (
	"context"
	"errors"
	"net/http"
	"time"

	"ccb/internal/biz/rpc"
	"ccb/internal/conf"

	openapi_go_sdk "gitee.com/chengdu_blue_brothers/openapi-go-sdk"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/api"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/notify"
	"github.com/go-kratos/kratos/v2/log"
)

type ZLTXRpcImpl struct {
	conf      *conf.Bootstrap
	logHelper *log.Helper
	clients   map[string]*api.Client
	notifies  map[string]*notify.Notify
}

func NewZLTXRpcImpl(conf *conf.Bootstrap, logHelper *log.Helper) rpc.ZLTXRpc {
	zltxRpc := &ZLTXRpcImpl{conf: conf, logHelper: logHelper, clients: make(map[string]*api.Client), notifies: make(map[string]*notify.Notify)}
	for s := range conf.ZltxMap {
		client, err := api.NewClient(conf.ZltxMap[s].MerchantId, conf.ZltxMap[s].<PERSON>Key, conf.ZltxMap[s].IsProd, 30*time.Second)
		if err == nil {
			zltxRpc.clients[s] = client
			zltxRpc.notifies[s] = notify.NewNotify(conf.ZltxMap[s].MerchantId, conf.ZltxMap[s].SecretKey)
		}
	}
	return zltxRpc
}

// RechargeOrder 直充下单
func (z *ZLTXRpcImpl) RechargeOrder(mrchId string, req *api.RechargeOrderReq) (*api.RechargeOrderResp, error) {
	if _, ok := z.clients[mrchId]; !ok {
		return nil, errors.New("mrch id is not exist")
	}
	return z.clients[mrchId].RechargeOrder(context.Background(), req)
}

// RechargeQuery 直充订单查询
func (z *ZLTXRpcImpl) RechargeQuery(mrchId, outTradeNo string) (*api.RechargeQueryResp, error) {
	if _, ok := z.clients[mrchId]; !ok {
		return nil, errors.New("mrch id is not exist")
	}
	return z.clients[mrchId].RechargeQuery(context.Background(), outTradeNo)
}

// CardOrder 卡密下单
func (z *ZLTXRpcImpl) CardOrder(mrchId string, req *api.CardOrderReq) (*api.CardOrderResp, error) {
	if _, ok := z.clients[mrchId]; !ok {
		return nil, errors.New("mrch id is not exist")
	}
	return z.clients[mrchId].CardOrder(context.Background(), req)
}

// CardQuery 卡密订单查询
func (z *ZLTXRpcImpl) CardQuery(mrchId, outTradeNo string) (*api.CardQueryResp, error) {
	if _, ok := z.clients[mrchId]; !ok {
		return nil, errors.New("mrch id is not exist")
	}
	return z.clients[mrchId].CardQuery(context.Background(), outTradeNo)
}

// Product 商品列表接口
func (z *ZLTXRpcImpl) Product(mrchId string) (*api.RechargeProductResp, error) {
	if _, ok := z.clients[mrchId]; !ok {
		return nil, errors.New("mrch id is not exist")
	}
	return z.clients[mrchId].RechargeProduct(context.Background())
}

// ParseOrderNotify 订单结果回调
func (z *ZLTXRpcImpl) ParseOrderNotify(req *http.Request) (*notify.OrderReq, error) {
	err := req.ParseForm()
	if err != nil {
		return nil, err
	}
	key := req.PostFormValue("merchantId")
	if key == "" {
		return nil, openapi_go_sdk.ErrMerchantIdEmpty
	}
	if _, ok := z.notifies[key]; ok {
		return z.notifies[key].ParseAndVerify(req)
	}
	return nil, errors.New("merchantId错误")
}
