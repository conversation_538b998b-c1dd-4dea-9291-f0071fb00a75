syntax = "proto3";
package api.err;

import "errors/errors.proto";
option go_package = "ccb/api/myerr;myerr";

enum WechatErr{
  option (errors.default_code) = 1;
  WechatFAIL = 0 [(errors.code) = 500];

  WechatUserIllegal = 1 [(errors.code) = 500];
  WechatAppIDMchIDMismatch = 2 [(errors.code) = 500];
  WechatOpenIDAppIDMismatch = 3 [(errors.code) = 500];
  WechatInvalidMerchantID = 4 [(errors.code) = 500];
  WechatHighFrequency = 5 [(errors.code) = 500];
  WechatActivityInactive = 6 [(errors.code) = 500];
  WechatBatchInfoError = 7 [(errors.code) = 500];
  WechatAppIDRequired = 8 [(errors.code) = 500];
  WechatOpenIDRequired = 9 [(errors.code) = 500];
  WechatBatchIDRequired = 10 [(errors.code) = 500];
  WechatMerchantIDRequired = 11 [(errors.code) = 500];
  WechatInvalidBatchStatus = 12 [(errors.code) = 500];
  WechatMchNotExists = 13 [(errors.code) = 500];
  WechatBatchBudgetInsufficient = 14 [(errors.code) = 500];
  WechatDailyLimitExceeded = 15 [(errors.code) = 500];
  WechatAccountBalanceInsufficient = 16 [(errors.code) = 500];
  WechatBatchBudgetDepleted = 17 [(errors.code) = 500];
  WechatMerchantNoPermission = 18 [(errors.code) = 500];
  WechatCrossMerchantNotSupported = 19 [(errors.code) = 500];
  WechatUserReceiveLimit = 20 [(errors.code) = 500];
  WechatAPIChannelNotSupported = 21 [(errors.code) = 500];
  WechatSpecifiedDenominationNotSupported = 22 [(errors.code) = 500];
  WechatOnlyAdvertisingBatch = 23 [(errors.code) = 500];
  WechatUserMaxCoupons = 24 [(errors.code) = 500];
  WechatNaturalPersonRuleBlocked = 25 [(errors.code) = 500];
  WechatResourceNotExists = 26 [(errors.code) = 500];
  WechatFrequencyLimited = 27 [(errors.code) = 500];
}
