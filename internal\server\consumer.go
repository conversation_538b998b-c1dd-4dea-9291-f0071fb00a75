package server

import (
	"ccb/internal/conf"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/event"
	"github.com/go-kratos/kratos/v2/log"
)

// NewConsumerServer 工厂方法
func NewConsumerServer(
	hLog *log.Helper,
	c *conf.Bootstrap,
) *event.ConsumerManager {
	manager := event.NewConsumerManager(hLog)
	eventMap := c.GetEventRocketMQ().GetEventMap()
	if len(eventMap) == 0 {
		return manager
	}

	event.SetRecoverLogger(hLog)

	return manager
}
