package main

import (
	"crypto/des"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"testing"

	"ccb/internal/pkg/crypto"

	des2 "github.com/wumansgy/goEncrypt/des"
)

// 密钥 24字节
const secretKey = "XK2WMSsDVWNcahkCQQDdAJOR5ziy9Cdv"

// 三重DES解密
func TripleDesDecrypt(decryptString string) (string, error) {
	decryptBytes, err := base64.StdEncoding.DecodeString(decryptString)
	if err != nil {
		return "", err
	}
	desKey, err := base64.StdEncoding.DecodeString(secretKey)
	if err != nil {
		return "", err
	}
	cryptText, err := des2.TripleDesDecrypt(decryptBytes, desKey, nil)
	if err != nil {
		return "", err
	}

	return string(cryptText), nil
}

func TestDES(t *testing.T) {
	// RSA公钥
	/*	pubKey := `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDUS/Ukcdn6hq5QAea+Ja1nMvDSB9Vy/DhzQKYs
		ZaYdtSZljTBZ6U3GKpr23zLoJq/X6lJMVVJW16dX9jfwhc/zSo1o+buUJlCYCX308F+ol/zPSzxo
		k1J1ruwqqnkDlHMyX251uDjDCpqvJCL8eqxrwKE2YdVkgLo5iJoQVFMDpQIDAQAB`*/

	// DES公钥
	tpDesKey := "XK2WMSsDVWNcahkCQQDdAJOR5ziy9Cdv"

	// 收到的加密参数
	encryptData := "bZNbU+Ya55XKf1NW/9pWKJqgMmXcs08Qavu9y8+2FRQaTie8Ijtyx8/9AMWIvHP4tbp8o0AKxUXTAo5O091fcBwkyGFJvgboJmzN03DwrqINezuIhVZsevWp+5J6I8YlFdIJ8lIYf8yf9Mia2fwdA4+dVP5USdPXFoQ8dT1ExByOdSUYD52KLM7cP+jChtXAIQbRrRu3OFvUgQNYjSDUwl9kdK3ZVLMXncwAEVyFqmu4ffdO+yJ5T5QB/C+FvlmX3fl8Kocjw6/SqrNaSPJZU6LBj6KtFNxZFtXM/QO+NGdP4iqoZZe1taJDTA6G6tdxXxM/ZeW9C4noI4RzZfEr8H5rTWcytgE7flYNhK6Xud6FV4amIZ+5oDCYJSFZ4nX/tnSh0sVbSQc="

	fmt.Println(TripleDesDecrypt(encryptData))
	xxx, _ := crypto.TripleDESDecrypt(tpDesKey, encryptData)
	fmt.Println(string(xxx))
	sp := make(map[string]any)
	err := json.Unmarshal(xxx, &sp)
	fmt.Println(sp, err)
	return

	// Base64解码
	decodeBase64, err := base64.StdEncoding.DecodeString(encryptData)
	if err != nil {
		log.Fatalf("Base64 decode error: %v", err)
	}

	// 使用DES解密
	desKey, err := base64.StdEncoding.DecodeString(tpDesKey)
	if err != nil {
		log.Fatalf("DES key decode error: %v", err)
	}

	block, err := des.NewTripleDESCipher(desKey)
	if err != nil {
		log.Fatalf("DES cipher error: %v", err)
	}

	if len(decodeBase64)%block.BlockSize() != 0 {
		log.Fatalf("Cipher text length not aligned with block size")
	}

	cipherText := make([]byte, len(decodeBase64))
	block.Decrypt(cipherText, decodeBase64)

	// 输出解密后的内容
	fmt.Println("encryptData解密后:", string(cipherText))
}
