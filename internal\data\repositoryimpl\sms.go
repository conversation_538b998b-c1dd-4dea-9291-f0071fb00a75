package repositoryimpl

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"time"

	"ccb/api/myerr"
	"ccb/internal/biz/repository"
	"ccb/internal/conf"
	"ccb/internal/data"
	"ccb/internal/pkg"
	"ccb/internal/pkg/sms"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

const (
	expire             = time.Minute * 15
	checkSmsCodeMaxNum = 30
)

type smsRepositoryImpl struct {
	data *data.Data

	sendSms            *conf.SendSms
	checkSmsCodeMaxNum int64
}

func NewSmsRepository(data *data.Data, c *conf.Bootstrap) repository.Sms {
	num := int64(checkSmsCodeMaxNum)
	if c.GetSendSms().GetCheckSmsCodeMaxNum() > 0 {
		num = c.GetSendSms().GetCheckSmsCodeMaxNum()
	}
	return &smsRepositoryImpl{
		data:               data,
		sendSms:            c.GetSendSms(),
		checkSmsCodeMaxNum: num,
	}
}

func (s *smsRepositoryImpl) getSignKey() string {
	return fmt.Sprintf("%x", md5.Sum([]byte(pkg.RandString(10, 6))))
}

func (s *smsRepositoryImpl) sendCode(ctx context.Context, req *sms.Message) (time.Time, error) {
	log.Debugw("SendSms", req.Template.GetTemplateCode(), "phone", req.Phone)
	return s.data.SmsCli.SendCode(ctx, req)
}

func (s *smsRepositoryImpl) SendCpnSms(ctx context.Context, phone string, param map[string]any) error {
	codeTime, err := s.sendCode(ctx, &sms.Message{
		Template: s.sendSms.GetSendCpnSms(),
		Phone:    phone,
		Info:     param,
	})
	if err != nil {
		return err
	}
	log.Debugw("SendCpnSms", codeTime)
	return nil
}

func (s *smsRepositoryImpl) VerifySmsCode(ctx context.Context, signKey, smsCode string) error {
	signKeyCheckKey := s.buildSignKeyCheckCountKey(signKey)
	n, err := s.data.Rdb.Exists(ctx, signKeyCheckKey).Result()
	if err != nil {
		return err
	}
	if n > 0 {
		count, err := s.data.Rdb.Get(ctx, signKeyCheckKey).Int64()
		if err != nil {
			return err
		}
		if count >= s.checkSmsCodeMaxNum {
			// 删除验证码缓存
			s.data.Rdb.Del(ctx, s.buildSmsCodeCacheKey(signKey))
			return myerr.ErrorNotAllow("短信验证码错误, 请重新获取")
		}
		s.data.Rdb.Incr(ctx, signKeyCheckKey)
	} else {
		if err := s.data.Rdb.Set(ctx, signKeyCheckKey, 1, expire).Err(); err != nil {
			return err
		}
	}
	codeInfo, err := s.data.Rdb.Get(ctx, s.buildSmsCodeCacheKey(signKey)).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return myerr.ErrorNotAllow("短信验证码已过期")
		}
		return err
	}
	if codeInfo != smsCode {
		return myerr.ErrorNotAllow("短信验证码错误")
	}
	return nil
}

// cacheSmsCode 缓存短信验证码
func (s *smsRepositoryImpl) cacheSmsCode(ctx context.Context, signKey, code string) error {
	return s.data.Rdb.Set(ctx, s.buildSmsCodeCacheKey(signKey), code, expire).Err()
}

// buildSmsCodeCacheKey 构建短信验证码缓存key
func (s *smsRepositoryImpl) buildSmsCodeCacheKey(signKey string) string {
	return "ccb:sms_code:" + signKey
}

// buildSignKeyCheckCountKey 构建短信验证码校验次数缓存key
func (s *smsRepositoryImpl) buildSignKeyCheckCountKey(signKey string) string {
	return "ccb:sms_code_check_count:" + signKey
}
