package repository

import (
	"context"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/valobj"
)

type CouponRepo interface {
	CreateE(ctx context.Context, d *do.CouponDo) (*do.CouponDo, error)
	UpdateE(ctx context.Context, d *do.CouponDo) (*do.CouponDo, error)
	UpdateStatus(ctx context.Context, id, residue int, oldStatus, status valobj.CouponStatus, transactionID string) (*do.CouponDo, error)
	FindByCode(ctx context.Context, channelId, code string) (*do.CouponDo, error)
	FindByOrderId(ctx context.Context, orderId int) ([]*do.CouponDo, error)
	FindPageList(ctx context.Context, reqBo *bo.CouponListBo) ([]*do.CouponDo, *bo.RespPageBo, error)
}
