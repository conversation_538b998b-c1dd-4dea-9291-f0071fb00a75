package repository

import (
	"context"

	"ccb/internal/biz/valobj"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
)

type ReconciliationRepo interface {
	CreateE(ctx context.Context, d *do.ReconciliationDo) (*do.ReconciliationDo, error)
	UpdateStatus(ctx context.Context, id int, status valobj.ReconciliationStatus) (*do.ReconciliationDo, error)
	UpdateStatusByNumber(ctx context.Context, number, msg string, status valobj.ReconciliationStatus) error
	FindByNumber(ctx context.Context, number string) (*do.ReconciliationDo, error)
	SearchList(ctx context.Context, reqBo *bo.ReconciliationListBo) ([]*do.ReconciliationDo, error)
}
