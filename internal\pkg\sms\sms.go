package sms

import (
	"context"
	"encoding/json"
	"time"

	"ccb/api/myerr"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/go-kratos/kratos/v2/log"
)

type Sms struct {
	log                          *log.Helper
	isOpen                       bool
	accessKeyId, accessKeySecret string
}

type AccessConfig interface {
	GetAccessKeyId() string
	GetAccessKeySecret() string
	GetIsOpen() bool
}

type Template interface {
	GetSignName() string
	GetTemplateCode() string
}

type Message struct {
	Template Template
	Phone    string
	Info     map[string]any
}

func NewSms(c AccessConfig, log *log.Helper) *Sms {
	return &Sms{
		log:             log,
		accessKeyId:     c.GetAccessKeyId(),
		accessKeySecret: c.GetAccessKeySecret(),
		isOpen:          c.GetIsOpen(),
	}
}

// SendCode 发送短信
func (s *Sms) SendCode(_ context.Context, msg *Message) (time.Time, error) {
	log.Debugw("[SendCode]", msg)
	if !s.isOpen {
		return time.Now(), nil
	}
	smsMapByte, _ := json.Marshal(msg.Info)
	request := &dysmsapi.SendSmsRequest{
		SignName:      tea.String(msg.Template.GetSignName()),
		TemplateCode:  tea.String(msg.Template.GetTemplateCode()),
		PhoneNumbers:  tea.String(msg.Phone),
		TemplateParam: tea.String(string(smsMapByte)),
	}
	return time.Now(), s.send(request)
}

// send 请求 api 进行发送
func (s *Sms) send(request *dysmsapi.SendSmsRequest) error {
	if *request.TemplateCode == "" {
		return myerr.ErrorNotAllow("未配置短信模板")
	}
	if *request.SignName == "" {
		return myerr.ErrorNotAllow("未配置短信签名")
	}

	if !s.isOpen {
		return nil
	}

	client, err := s.createClient(s.accessKeyId, s.accessKeySecret)
	if err != nil {
		return err
	}

	runtime := &util.RuntimeOptions{
		Autoretry:   tea.Bool(true),
		MaxAttempts: tea.Int(3),
		IgnoreSSL:   tea.Bool(true),
	}
	res, err := client.SendSmsWithOptions(request, runtime)
	if err != nil {
		return err
	}
	s.log.Info("send sms success", "res", res)
	if *res.StatusCode != 200 || *res.Body.Code != "OK" {
		// 此SDK有些错误，err 返回的 是 nil，但其实是失败
		return myerr.ErrorException("短信接口异常 %s", *res.Body.Message)
	}
	return nil
}

// CreateClient 发送 client
func (s *Sms) createClient(accessKeyId string, accessKeySecret string) (client *dysmsapi.Client, err error) {
	if accessKeySecret == "" || accessKeyId == "" {
		err = myerr.ErrorNotAllow("未配置短信发送凭据信息")
		return
	}
	config := &openapi.Config{
		AccessKeyId:     &accessKeyId,
		AccessKeySecret: &accessKeySecret,
	}
	// 访问的域名
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	client, err = dysmsapi.NewClient(config)
	return
}
