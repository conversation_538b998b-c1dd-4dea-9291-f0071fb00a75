package valobj

type AdminUserStatus int

const (
	AdminUserStatusUnknown AdminUserStatus = iota // 未知
	AdminUserStatusEnable                         // 生效中
	AdminUserStatusDisable                        // 禁用
)

// AdminUserStatusMap 充值产品 code 映射
var AdminUserStatusMap = map[AdminUserStatus]string{
	AdminUserStatusUnknown: "未知",
	AdminUserStatusEnable:  "生效中",
	AdminUserStatusDisable: "已禁用",
}

func (r AdminUserStatus) GetName() string {
	return AdminUserStatusMap[r]
}

func (r AdminUserStatus) GetValue() int {
	return int(r)
}
