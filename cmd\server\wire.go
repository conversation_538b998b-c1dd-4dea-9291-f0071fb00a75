//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"ccb/internal/biz"
	"ccb/internal/conf"
	"ccb/internal/data"
	"ccb/internal/data/repositoryimpl"
	"ccb/internal/data/rpcimpl"
	"ccb/internal/data/wechatrepositoryimpl"
	"ccb/internal/pkg"
	"ccb/internal/pkg/jwt"
	log2 "ccb/internal/pkg/log"
	"ccb/internal/server"
	"ccb/internal/server/crontab"
	"ccb/internal/service/consumer"
	"ccb/internal/service/front"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Bootstrap, log.Logger, *log2.AccessLogger) (*kratos.App, func(), error) {
	panic(wire.Build(
		server.ProviderSetServer,
		front.ProviderSetServiceFront,
		biz.ProviderSetBiz,
		data.ProviderSetData,
		// eventimpl.ProviderEvent,
		crontab.ProviderSetServer,
		repositoryimpl.ProviderRepositoryImpl,
		rpcimpl.ProviderSetRpcImpl,
		pkg.ProviderSetPkg,
		jwt.ProviderSetPkgJwt,
		consumer.ProviderSetConsumerService,
		wechatrepositoryimpl.ProviderWechatReposImplSet,
		newApp,
	))
}
