package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type StockDo struct {
	ID          int
	ProductID   int
	ProductCode string
	Code        string
	Status      valobj.IsEnable
	MerchantID  string
	ActivityID  string
	BatchNo     string // 借记卡(01)批次
	BatchNo2    string // 信用卡(02)批次
	Total       int
	Residue     int
	UsedNum     int
	AccountType valobj.AccountType
	Key         string
	CreatedTime time.Time
	UpdatedTime time.Time
	SendType    valobj.StockSendType
	Product     *ProductDo
}
