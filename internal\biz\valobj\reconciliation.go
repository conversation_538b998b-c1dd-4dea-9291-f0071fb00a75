package valobj

type ReconciliationStatus int

const (
	ReconciliationStatusDefault ReconciliationStatus = iota
	ReconciliationStatusWaitSend
	ReconciliationStatusSended
	ReconciliationStatusCompleted
	ReconciliationStatusFailed
)

var ReconciliationStatusMap = map[ReconciliationStatus]string{
	ReconciliationStatusDefault:   "默认",
	ReconciliationStatusWaitSend:  "待推送",
	ReconciliationStatusSended:    "已推送",
	ReconciliationStatusCompleted: "已处理",
	ReconciliationStatusFailed:    "处理失败",
}

func (r ReconciliationStatus) GetName() string {
	return ReconciliationStatusMap[r]
}

func (r ReconciliationStatus) GetValue() int {
	return int(r)
}
