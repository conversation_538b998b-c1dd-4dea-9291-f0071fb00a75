package main

import (
	"os"

	"ccb/internal/initialize"
	"ccb/internal/pkg/jwt"
	log2 "ccb/internal/pkg/log"
	"ccb/internal/server"
	"ccb/internal/service"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/event"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = "ccb"
	// Version is the version of the compiled software.
	Version string

	id, _ = os.Hostname()
)

func main() {
	bc := initialize.LoadConfig()
	businessLogger := log2.NewBusinessLogger(bc.Logs, id, Name, Version)
	accessLogger := log2.NewAccessLogger(bc.Logs, id, Name, Version)

	app, cleanup, err := wireApp(bc, businessLogger, accessLogger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err = app.Run(); err != nil {
		panic(err)
	}
}

func newApp(
	logger log.Logger,
	hLog *log.Helper,
	httpServer *http.Server,
	consumerServer *event.ConsumerManager,
	logServer *server.MonitorLogServer,
	cronService *server.CronServer,
	_ *server.Router,
	pkgJWT *jwt.JWT,
	wechatConsumer *server.WechatNotifyConsumer,
) *kratos.App {
	coroutineServer := coroutine.NewServer(hLog)
	serverOption := kratos.Server(
		httpServer, consumerServer, logServer, cronService, coroutineServer, wechatConsumer,
	)

	if initialize.IsCommandMode() {
		// 命令行模式，不启动 grpc, consumer
		serverOption = kratos.Server(
			logServer,
		)
	}

	// 特殊处理：为了使其它非依赖管理的地址使用，如service.base
	service.JWTPkg = pkgJWT

	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		serverOption,
	)
}
