package crontab

import (
	"context"

	"ccb/internal/biz"
	"ccb/internal/biz/bo"
	"ccb/internal/conf"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"
	"github.com/go-kratos/kratos/v2/log"
)

type ReconciliationCron struct {
	c      *conf.Bootstrap
	log    *log.Helper
	ccbBiz *biz.CCBBiz
}

func NewReconciliationCron(c *conf.Bootstrap, log *log.Helper, ccbBiz *biz.CCBBiz) *ReconciliationCron {
	return &ReconciliationCron{c: c, log: log, ccbBiz: ccbBiz}
}

func (e *ReconciliationCron) Run() {
	coroutine.Run("reconciliation-create", func() {
		_ = e.ccbBiz.CreateReconciliation(context.Background(), 1)
		_ = e.ccbBiz.SendReconciliation(context.Background(), &bo.PushReconciliationReq{})
	})
}
