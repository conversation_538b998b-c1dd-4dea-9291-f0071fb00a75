package valobj

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/shopspring/decimal"
)

// Money 单位元*10000
type Money int

const MoneyDivisor float64 = 10000

func NewMoneyFromYuan(money float64) Money {
	return Money(decimal.NewFromFloat(money).Mul(decimal.NewFromFloat(MoneyDivisor)).IntPart())
}

// Add 增加
func (m Money) Add(i Money) Money {
	return m + i
}

// AddByYuan 增加元
func (m Money) AddByYuan(money float64) Money {
	return m.Add(NewMoneyFromYuan(money))
}

// String 格式化字符串
func (m Money) String() string {
	return fmt.Sprintf("¥%.4f", float64(m)/MoneyDivisor)
}

func (m Money) FormatYuan() string {
	moneyYuan := float64(m) / MoneyDivisor
	result := strconv.FormatFloat(moneyYuan, 'f', 2, 64)
	return strings.TrimSuffix(strings.TrimSuffix(result, "."), "0")
}

func (m Money) FormatYuanWithPrec(n int) string {
	moneyYuan := float64(m) / MoneyDivisor
	result := strconv.FormatFloat(moneyYuan, 'f', n, 64)
	return strings.TrimSuffix(strings.TrimSuffix(result, "."), "0")
}

func (m Money) GetYuan() float64 {
	return float64(m) / MoneyDivisor
}

func (m Money) Div(num int) Money {
	return Money(math.Round(float64(m) / float64(num)))
}

func (m Money) Mul(num int) Money {
	return Money(math.Round(float64(m) * float64(num)))
}

func (m Money) GetValue() int {
	return int(decimal.NewFromInt(int64(m)).IntPart())
}
