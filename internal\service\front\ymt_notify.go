package front

import (
	"io"

	"ccb/internal/biz"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type YmtNotifyService struct {
	logger *log.Helper
	c      *conf.Bootstrap
	ymtBiz *biz.YmtBiz
}

func NewYmtNotifyService(logger *log.Helper, c *conf.Bootstrap, ymtBiz *biz.YmtBiz) *YmtNotifyService {
	return &YmtNotifyService{logger: logger, c: c, ymtBiz: ymtBiz}
}

// OrderNotify 订单核销回调
func (srv *YmtNotifyService) OrderNotify(ctx http.Context) error {
	body, _ := io.ReadAll(ctx.Request().Body)
	defer ctx.Request().Body.Close()
	srv.logger.Infof("收到易码通回调 notify body: %s", string(body))

	err := srv.ymtBiz.OrderNotify(body)
	if err != nil {
		return ctx.String(200, err.Error())
	}
	return ctx.String(200, "ok")
}

// OrderNotifyV2 订单核销回调
func (srv *YmtNotifyService) OrderNotifyV2(ctx http.Context) error {
	body, _ := io.ReadAll(ctx.Request().Body)
	defer ctx.Request().Body.Close()
	srv.logger.Infof("收到易码通回调v2 notify body: %s", string(body))
	err := srv.ymtBiz.OrderNotifyV2(body, ctx.Vars().Get("name"))
	if err != nil {
		return ctx.String(200, err.Error())
	}
	return ctx.String(200, "ok")
}
