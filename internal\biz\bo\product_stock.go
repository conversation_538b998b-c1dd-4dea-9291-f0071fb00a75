package bo

type ProductStockReq struct {
	ProductId string `json:"productId"`
	DccpAvyId string `json:"dccpAvyId"`
}

type ProductStockBo struct {
	ProductId   int
	ProductCode string
	MerchantId  string
	Status      int
	ActivityId  string
	BatchNo     string
}

type ProductStockListBo struct {
	Page        *ReqPageBo
	ProductId   int
	ProductCode string
	MerchantId  string
	ProductType int
	Status      int
	Statuses    []int
}
