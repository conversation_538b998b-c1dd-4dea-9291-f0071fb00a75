package do

import "ccb/internal/biz/valobj"

type CCBResponse struct {
	Header map[string]string
	Body   interface{}
}

type CodeMsg struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

type CouponStatusResp struct {
	Code         string `json:"code"`
	Msg          string `json:"msg"`
	CouponCode   string `json:"couponCode"`
	CouponStatus string `json:"couponStatus"`
	StatusDesc   string `json:"statusDesc,omitempty"`
	UseOrderId   string `json:"useOrderId,omitempty"`
	OrderAmt     string `json:"orderAmt,omitempty"`
	PayAmt       string `json:"payAmt,omitempty"`
	PrftAmt      string `json:"prftAmt,omitempty"`
	StoreId1     string `json:"storeId_1,omitempty"`
	StoreId2     string `json:"storeId_2,omitempty"`
	UseTm        string `json:"useTm,omitempty"`
	AcmUsedAmt   string `json:"acmUsedAmt,omitempty"`
	UnUsedAmt    string `json:"unUsedAmt,omitempty"`
	ExpireTm     string `json:"expireTm,omitempty"`
}

type CouponReceiveResp struct {
	Code       string      `json:"code"`
	Msg        string      `json:"msg"`
	Status     string      `json:"status"`
	OrderId    string      `json:"orderId"`
	AsynFlag   string      `json:"asynFlag"`
	UseType    string      `json:"useType"`
	RespFlag   string      `json:"respFlag,omitempty"`
	CompleteTm string      `json:"completeTm,omitempty"`
	Coupons    []CCBCoupon `json:"coupons,omitempty"`
}

type CCBCoupon struct {
	CouponCode string `json:"couponCode"`
	Password   string `json:"password,omitempty"`
	Link       string `json:"link,omitempty"`
}

type CCBUserRechargeResp struct {
	Code          string `json:"code"`
	Msg           string `json:"msg"`
	Status        string `json:"status"`
	OrderId       string `json:"orderId"`
	AsynFlag      string `json:"asynFlag"`
	CompleteTm    string `json:"completeTm"`
	PltfrmOrderId string `json:"pltfrmOrderId,omitempty"`
}

type MarketSendKeyResp struct {
	ErrCode string `json:"errCode"` // 00-成功 其他：失败
	Msg     string `json:"msg"`
	Data    struct {
		VoucherId    string `json:"voucher_id"`
		VoucherCode  string `json:"voucher_code"`
		ShortUrl     string `json:"short_url"`
		VoucherSdate string `json:"voucher_sdate"`
		VoucherEdate string `json:"voucher_edate"`
		CodeType     string `json:"code_type"` // 00- 代金券 01- 满减券
	} `json:"data"`
}
type MarketCancelKeyResp struct {
	ErrCode string `json:"errCode"` // 00-成功 其他：失败
	Msg     string `json:"msg"`
	Data    struct {
		VoucherId   string `json:"voucher_id"`
		VoucherCode string `json:"voucher_code"`
		VoucherDate string `json:"voucher_date"`
		Status      string `json:"status"` // 5-已作废
	} `json:"data"`
}

type MarketQueryKeyResp struct {
	ErrCode string `json:"errCode"` // 00-成功 其他：失败
	Msg     string `json:"msg"`
	Data    struct {
		VoucherId     string                     `json:"voucher_id"`
		VoucherCode   string                     `json:"voucher_code"`
		VoucherStatus valobj.MarketVoucherStatus `json:"voucher_status"` // 2-已发放 4-已核销 5-已作废
	} `json:"data"`
}

type MarketNotifyResp struct {
	Code    string `json:"code"` // 00-成功 其他：失败
	Msg     string `json:"msg"`
	TradeNo string `json:"tradeNo"`
}
