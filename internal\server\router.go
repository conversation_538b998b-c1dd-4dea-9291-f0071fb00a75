package server

import (
	"context"
	http2 "net/http"

	"ccb/internal/conf"
	"ccb/internal/service/front"

	"github.com/go-kratos/kratos/v2/transport/http"
)

type Router struct{}

func NewRouter(
	c *conf.Bootstrap,
	srv *http.Server,
	ccbSrv *front.CCBNotifyService,
	zltxSrv *front.ZltxNotifyService,
	marketSrv *front.MarketNotifyService,
	ymtSrv *front.YmtNotifyService,
) *Router {
	srv.Handle("/ping", http2.HandlerFunc(func(w http2.ResponseWriter, r *http2.Request) {
		_, _ = w.Write([]byte("pong"))
		return
	}))
	customeRouter := srv.Route("")
	{
		customeRouter.GET("/getReconciliationInfo", wrapMiddleware(ccbSrv.GetReconciliationInfo))
		customeRouter.GET("/proxy/{mchId}", wrapMiddleware(ccbSrv.Proxy))
	}
	apiRouter := srv.Route("/api")
	{
		apiRouter.POST("/product/list", wrapMiddleware(ccbSrv.ProductList))
		apiRouter.POST("/product/stock", wrapMiddleware(ccbSrv.ProductStock))
		apiRouter.POST("/coupon/receive", wrapMiddleware(ccbSrv.CouponReceive))
		apiRouter.POST("/coupon/dynamic_verify_code", wrapMiddleware(ccbSrv.DynamicVerifyCode))
		apiRouter.POST("/coupon/status", wrapMiddleware(ccbSrv.CouponStatus))
		apiRouter.POST("/coupon/cancel", wrapMiddleware(ccbSrv.CouponCancel))
		apiRouter.POST("/reconciliation/notidy", wrapMiddleware(ccbSrv.ReconciliationNotify))
		apiRouter.POST("/user/recharge", wrapMiddleware(ccbSrv.UserRecharge))
		apiRouter.POST("/wechatCpn/register", wrapMiddleware(ccbSrv.WechatCpnRegisterNotifyTag))

		apiRouter.POST("/zltx/order_notify", wrapMiddleware(zltxSrv.OrderNotify))
		apiRouter.POST("/zltx/coupon_notify", wrapMiddleware(zltxSrv.CouponStatusNotify))
		apiRouter.POST("/zltx/products", wrapMiddleware(zltxSrv.Products))

		apiRouter.POST("/market/coupon_notify", wrapMiddleware(marketSrv.CouponNotify))
		apiRouter.POST("/market/coupon_notify_ignore", wrapMiddleware(marketSrv.CouponIgnore))

		apiRouter.POST("/ymt/notify", wrapMiddleware(ymtSrv.OrderNotify))
		apiRouter.POST("/ymt/notify/{name}", wrapMiddleware(ymtSrv.OrderNotifyV2))
	}

	// 注册 openapi
	if c.Server.Http.GetIsOpenSwagger() {
		srv.HandlePrefix("/doc/", http2.StripPrefix("/doc/", http2.FileServer(http2.Dir("./third_party/swagger_ui"))))
	}

	return &Router{}
}

func wrapMiddleware(fn func(http.Context) error) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		h := ctx.Middleware(func(innerCtx context.Context, req interface{}) (interface{}, error) {
			return nil, fn(ctx)
		})
		_, err := h(ctx, nil)
		if err != nil {
			return err
		}
		return ctx.Result(200, nil)
	}
}
