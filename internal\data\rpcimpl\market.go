package rpcimpl

import (
	"bytes"
	"crypto/rsa"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"sort"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/rpc"
	"ccb/internal/conf"
	"ccb/internal/pkg/crypto"

	"github.com/go-kratos/kratos/v2/log"
)

type MarketRpcImpl struct {
	conf          *conf.Bootstrap
	logHelper     *log.Helper
	marketPubkeys map[string]*rsa.PublicKey
	mrchPubKeys   map[string]*rsa.PublicKey
	mrchPriKeys   map[string]*rsa.PrivateKey
}

func NewMarketRpcImpl(conf *conf.Bootstrap, logHelper *log.Helper) rpc.MarketRpc {
	impl := &MarketRpcImpl{
		conf:          conf,
		logHelper:     logHelper,
		marketPubkeys: make(map[string]*rsa.PublicKey),
		mrchPubKeys:   make(map[string]*rsa.PublicKey),
		mrchPriKeys:   make(map[string]*rsa.PrivateKey),
	}
	for s := range conf.MarketMap {
		if conf.MarketMap[s].MerchantPublicKey != "" {
			impl.marketPubkeys[s], _ = crypto.PublicKey(conf.MarketMap[s].MerchantPublicKey)
		}
		if conf.MarketMap[s].PublicKey != "" {
			impl.mrchPubKeys[s], _ = crypto.PublicKey(conf.MarketMap[s].PublicKey)
		}
		if conf.MarketMap[s].PrivateKey != "" {
			impl.mrchPriKeys[s], _ = crypto.PrivateKey(conf.MarketMap[s].PrivateKey)
		}
	}
	return impl
}

// VerifySign 验签
func (impl *MarketRpcImpl) VerifySign(data map[string]interface{}) (err error) {
	if _, ok := data["sign"]; !ok {
		return errors.New("sign is null")
	}
	signStr := impl.getSignString(data)
	return crypto.Sha256WithRsaVerify(signStr, data["sign"].(string), impl.marketPubkeys["jianxingjinke"])
}

// ReceiveKey 领取优惠券
func (impl *MarketRpcImpl) ReceiveKey(req *bo.MarketReceiveKeyReq) (*do.MarketSendKeyResp, error) {
	reqStr, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	err = json.Unmarshal(reqStr, &data)
	if err != nil {
		return nil, err
	}
	data["req_code"] = "voucher.create"
	resp, err := impl.post("/openApi/v1/market/key/send", data)
	if err != nil {
		return nil, err
	}
	var respBo do.MarketSendKeyResp
	err = json.Unmarshal(resp, &respBo)
	if err != nil {
		return nil, errors.New(err.Error() + ",返回数据:" + string(resp))
	}
	return &respBo, nil
}

// CancelKey 作废优惠券
func (impl *MarketRpcImpl) CancelKey(req *bo.MarketCancelKeyReq) (*do.MarketCancelKeyResp, error) {
	reqStr, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	err = json.Unmarshal(reqStr, &data)
	if err != nil {
		return nil, err
	}
	data["req_code"] = "voucher.invalid"
	resp, err := impl.post("/openApi/v1/market/key/discard", data)
	if err != nil {
		return nil, err
	}
	var respBo do.MarketCancelKeyResp
	err = json.Unmarshal(resp, &respBo)
	if err != nil {
		return nil, errors.New(err.Error() + ",返回数据:" + string(resp))
	}
	return &respBo, nil
}

func (impl *MarketRpcImpl) QueryKey(req *bo.MarketQueryKeyReq) (*do.MarketQueryKeyResp, error) {
	reqStr, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	err = json.Unmarshal(reqStr, &data)
	if err != nil {
		return nil, err
	}
	data["req_code"] = "voucher.query"
	resp, err := impl.post("/openApi/v1/market/key/query", data)
	if err != nil {
		return nil, err
	}
	var respBo do.MarketQueryKeyResp
	err = json.Unmarshal(resp, &respBo)
	if err != nil {
		return nil, errors.New(err.Error() + ",返回数据:" + string(resp))
	}
	return &respBo, nil
}

func (impl *MarketRpcImpl) Decode(code string) (string, error) {
	ret, err := crypto.NewAESCipher([]byte("59d153c65552003826d16f74ac62fb41"), crypto.ECBMode, crypto.Pkcs7, crypto.PrintBase64).Decrypt(code)
	return string(ret), err
}

func (impl *MarketRpcImpl) getSignString(data map[string]interface{}) string {
	keys := make([]string, 0, len(data))
	for key := range data {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	signString := ""
	separator := ""
	for _, key := range keys {
		value := data[key]
		if key == "sign" || value == nil {
			continue
		}
		signString += fmt.Sprintf("%s%s=%v", separator, key, value)
		separator = "&"
	}
	return signString
}

func (impl *MarketRpcImpl) post(uri string, data map[string]interface{}) ([]byte, error) {
	data["stockId"] = data["voucher_id"]
	signStr := impl.getSignString(data)
	sign, err := crypto.Sha256WithRsaSign([]byte(signStr), impl.mrchPriKeys[data["mem_id"].(string)])
	if err != nil {
		return nil, err
	}
	data["sign"] = sign
	client := &http.Client{
		Transport: &http.Transport{ // 配置连接池
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   60 * time.Second,
				KeepAlive: 60 * time.Second,
			}).DialContext,
			IdleConnTimeout: 30 * time.Second,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	reqData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	impl.logHelper.Infof("market请求:%s:request:%s", uri, string(reqData))
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s%s", impl.conf.MarketMap[data["mem_id"].(string)].NotifyUrl, uri), bytes.NewBuffer(reqData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	impl.logHelper.Infof("market请求:%s:resp:%s", uri, string(respData))
	return respData, nil
}
