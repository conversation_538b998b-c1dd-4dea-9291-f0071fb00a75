package helper

import (
	"strconv"
)

// SliceConvertSlice 整型切片互转
func SliceConvertSlice[O int | int32 | int64 | int8, T int | int32 | int64 | int8 | string](inputSlice []T) []O {
	output := make([]O, 0)
	for _, item := range inputSlice {
		var anyV any = item
		switch v := anyV.(type) {
		case string:
			vv, err := strconv.Atoi(v)
			if err != nil {
				continue
			}
			output = append(output, O(vv))
		case int:
			output = append(output, O(v))
		case int32:
			output = append(output, O(v))
		case int64:
			output = append(output, O(v))
		case int8:
			output = append(output, O(v))
		}
	}
	return output
}
