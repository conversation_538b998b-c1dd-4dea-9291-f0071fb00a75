// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Product struct {
	ent.Schema
}

/*
*
attr:
权益类型:3直充 4优惠券
权益发放时效性：1同步2异步
权益的使用方式：1兑换码 4动态核销码 5静态核销码 6直充
用户标识类型：1第三方2手机号 3其他
是否支持一次接口调用发放多张优惠券:0不支持 1支持
优惠券是否支持多次使用:0不支持 1金额模式2次数3卡包
是否支持回传优惠券的核销状态:0不支持 1支持
是否支持作废 0不支持 1真正核销之前支持作废  2兑换码模式的产品,在兑换前支持作废,兑换后不支持作废
*/
func (Product) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("code").Comment("产品编码"),
		field.String("name").Comment("产品名"),
		field.String("desc").Comment("产品介绍"),
		field.Int("status").Comment("状态"),
		field.Int("type").Comment("产品类型 1 直充  2直连天下卡密 3直连天下延伸卡密 4营销系统延伸卡密 5微信立减金 6易码通商品 [建行目前未使用 2 3 4]"),
		field.String("attr").Comment("产品属性"),
		field.Int("face_price").Comment("面值"),
		field.Int("cost_price").Comment("成本单价"),
		field.String("send_mch_id").Comment("发券商户号"),
		field.String("creator_mch_id").Comment("建券商户号"),
		field.Time("created_time").Optional().Comment("创建时间"),
		field.Time("updated_time").Optional().Comment("更新时间"),
	}
}
func (Product) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("stock", Stock.Type),
		edge.To("coupon", Coupon.Type),
	}
}

func (Product) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "product"}}
}
