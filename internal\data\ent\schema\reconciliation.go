// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type Reconciliation struct {
	ent.Schema
}

func (Reconciliation) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("number"),
		field.String("file"),
		field.String("merchant_id"),
		field.Time("start_date").Optional(),
		field.Time("end_date").Optional(),
		field.Int("status").Optional().Comment("1待推送  2推送中 3已推送  4已处理  5处理失败"),
		field.String("msg").Optional(),
		field.Time("create_time").Optional(),
		field.Time("update_time").Optional(),
	}
}
func (Reconciliation) Edges() []ent.Edge {
	return nil
}
func (Reconciliation) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "reconciliation"}}
}
