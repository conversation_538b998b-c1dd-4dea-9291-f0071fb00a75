package helper

import (
	"regexp"

	"github.com/duke-git/lancet/v2/convertor"
)

// chineseMobileFormMatcher 手机号格式正则匹配器
var chineseMobileFormMatcher = regexp.MustCompile(`^1\d{10}$`)

// IsMobile 手机号格式，不严谨的验证
func IsMobile(mobile any) bool {
	mobileStr := convertor.ToString(mobile)
	return chineseMobileFormMatcher.MatchString(mobileStr)
}

// IsEmail 邮箱格式
func IsEmail(email string) bool {
	return regexp.MustCompile(`^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$`).MatchString(email)
}
