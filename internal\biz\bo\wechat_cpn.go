package bo

import "ccb/internal/biz/valobj"

type WechatSendCouponBo struct {
	StockSendMchId    string // 发券商户号
	StockCreatorMchId string // 创建券商户号
	Appid             string // 微信公众号/小程序appid
	OutRequestNo      string // 商户侧需保持唯一性
	Openid            string // 用户openid
	StockId           string // 券批次号
}

type WechatQueryCouponBo struct {
	StockSendMchId string // 发券商户号
	CouponId       string // 券id
	Appid          string // 微信公众号/小程序appid
	Openid         string // 用户openid
}

// ConsumeInformation 定义消费信息结构体
type ConsumeInformation struct {
	// 代金券核销时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，yyyy-MM-DD表示年月日
	ConsumeTime string `json:"consume_time"`
	// 该参数目前现在返回的是收款商户号，间连模式下，目前传的是银联和网联的商户号
	// 如果需要知道核销的二级商户号，可以在下载批次核销明细API里查询看到
	ConsumeMchid string `json:"consume_mchid"`
	// 微信支付系统生成的订单号
	TransactionID string `json:"transaction_id"`
	// 核销金额，仅有当business_type=MULTIUSE时，才会返回。单位，分
	ConsumeAmount int64 `json:"consume_amount,omitempty"`
}

// PlainText 定义明文数据结构体
type PlainText struct {
	StockCreatorMchid  string                 `json:"stock_creator_mchid"`
	StockID            string                 `json:"stock_id"`
	CouponID           string                 `json:"coupon_id"`
	CouponName         string                 `json:"coupon_name"`
	Description        string                 `json:"description"`
	Status             valobj.WechatCpnStatus `json:"status"`
	CreateTime         string                 `json:"create_time"`
	CouponType         string                 `json:"coupon_type"`
	NoCash             bool                   `json:"no_cash"`
	Singleitem         bool                   `json:"singleitem"`
	BusinessType       string                 `json:"business_type,omitempty"`
	ConsumeInformation ConsumeInformation     `json:"consume_information,omitempty"`
}

type WechatCpnNotifyBo struct {
	ID             string    `json:"id"`
	CreateTime     string    `json:"create_time"`
	ResourceType   string    `json:"resource_type"`
	EventType      string    `json:"event_type"`
	Summary        string    `json:"summary"`
	OriginalType   string    `json:"original_type"`
	AssociatedData string    `json:"associated_data"`
	PlainText      PlainText `json:"plain_text"`
}

type WechatCpnRegisterNotifyTagBo struct {
	ProductId   int    `json:"productId"`
	ProductCode string `json:"productCode"`
	BatchNo     string `json:"batchNo"`
}
