package biz

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/conf"
	"ccb/internal/data/ent"
	"ccb/internal/pkg/helper"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
)

type YmtBiz struct {
	c             *conf.Bootstrap
	log           *log.Helper
	ymtRpc        rpc.YmtRpc
	ccbRpc        rpc.CCBRpc
	orderRepo     repository.OrderRepo
	couponRepo    repository.CouponRepo
	couponLogRepo repository.CouponLogRepo
	stockRepo     repository.StockRepo
	transRepo     repository.TransactionRepo
}

func NewYmtBiz(c *conf.Bootstrap, log *log.Helper, ymtRpc rpc.YmtRpc, ccbrpc rpc.CCBRpc, orderRepo repository.OrderRepo, couponRepo repository.CouponRepo, couponLogRepo repository.CouponLogRepo, stockRepo repository.StockRepo, transRepo repository.TransactionRepo) *YmtBiz {
	return &YmtBiz{c: c, log: log, ymtRpc: ymtRpc, ccbRpc: ccbrpc, orderRepo: orderRepo, couponRepo: couponRepo, couponLogRepo: couponLogRepo, stockRepo: stockRepo, transRepo: transRepo}
}

// 订单回调
func (m *YmtBiz) OrderNotify(body []byte) error {
	// 解析请求参数
	req := new(key.Notify)
	err := json.Unmarshal(body, req)
	if err != nil {
		return err
	}

	// 验证回调，解析参数
	reqBo, err := m.ymtRpc.Notify(req, "")
	if err != nil {
		return err
	}
	key := reqBo.Key
	if key == "" && reqBo.Url != "" {
		if temp := strings.Split(reqBo.Url, "/"); len(temp) > 0 {
			key = temp[len(temp)-1]
		}
	}
	return m.handle(key, reqBo.OutBizNo, string(body), reqBo.UsageTime, "receive", "")
}

// 订单回调
func (m *YmtBiz) OrderNotifyV2(body []byte, ymtKey string) error {
	// 解析请求参数
	req := new(key.Notify)
	err := json.Unmarshal(body, req)
	if err != nil {
		return err
	}

	// 验证回调，解析参数
	reqBo, err := m.ymtRpc.Notify(req, ymtKey)
	if err != nil {
		return err
	}
	cpn := reqBo.Key
	if cpn == "" && reqBo.Url != "" {
		if temp := strings.Split(reqBo.Url, "/"); len(temp) > 0 {
			cpn = temp[len(temp)-1]
		}
	}
	return m.handle(cpn, reqBo.OutBizNo, string(body), reqBo.UsageTime, "receive", ymtKey)
}

func (m *YmtBiz) handle(code, tradeNo, reqStr, usageTime, typ, ymtKey string) error {
	ctx := context.Background()
	coupon, err := m.couponRepo.FindByCode(ctx, defaultChannelId, code)
	if err != nil {
		if ent.IsNotFound(err) {
			return errors.New("优惠券不存在")
		}
		return fmt.Errorf("coupon verify notify find coupon error: %s", err)
	}
	if coupon.Residue == 0 {
		return nil
	}
	oldStatus := coupon.Status
	if oldStatus == valobj.CouponStatusUnkown {
		oldStatus = valobj.CouponStatusUnused
	}
	order, err := m.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		return fmt.Errorf("coupon verify notify find order error: %s", err)
	}
	usTm := time.Now()
	if usageTime != "" {
		tm, err := helper.DateStringToTimeV2(usageTime)
		if err == nil && tm.Unix() > 0 {
			usTm = tm
		}
	}
	// 优惠券已核销， 通知银行
	if coupon.Status == valobj.CouponStatusVerify {
		_, err = m.ccbRpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
			ProductId:  coupon.ProductCode,
			OrderId:    order.OrderNumber,
			CouponCode: coupon.Code,
			Operation:  valobj.CCBCouponOperationToUsed,
			UseOrderId: tradeNo,
			OrderAmt:   "0",
			PayAmt:     "0",
			PrftAmt:    "0",
			StoreId2:   "lsxd",
			UseTm:      usTm.Format(defaultFormatLayout),
		})
		return err
	}

	// 查询易码通key码状态
	if queryResp, err := m.ymtRpc.Query(tradeNo, ymtKey); err != nil {
		return fmt.Errorf("易码通 coupon verify notify query key error: %s", err)
	} else if queryResp.Status != valobj.YmtStatusNotYmt {
		return fmt.Errorf("请求的状态和真实状态不一致: %d", queryResp.Status)
	}

	// 更新优惠券状态
	coupon, err = m.couponRepo.UpdateStatus(ctx, coupon.Id, -1, coupon.Status, valobj.CouponStatusVerify, tradeNo)
	if err != nil {
		return fmt.Errorf("coupon verify notify update coupon error: %s", err)
	}

	// 记录日志
	dateStatus := fmt.Sprintf("%d,%d", oldStatus, valobj.CouponStatusVerify)
	now := time.Now()
	couponLog, err := m.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
		BeginDate: &now,
		CouponId:  coupon.Id,
	})
	if err != nil && !ent.IsNotFound(err) {
		return fmt.Errorf("coupon verify notify find coupon log error: %s", err)
	}
	if couponLog != nil && couponLog.Id > 0 {
		ss := strings.Split(couponLog.DateStatus, ",")
		dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusVerify)
	}
	_, err = m.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
		CouponId:      coupon.Id,
		BeforeStatus:  oldStatus,
		Status:        valobj.CouponStatusVerify,
		TransactionId: tradeNo,
		DateStatus:    dateStatus,
		RequestData:   reqStr,
	})
	if err != nil {
		m.log.Errorf("coupon verify notify create coupon log error: %s", err)
	}

	// 通知银行
	_, err = m.ccbRpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
		ProductId:  coupon.ProductCode,
		OrderId:    order.OrderNumber,
		CouponCode: coupon.Code,
		Operation:  valobj.CCBCouponOperationToUsed,
		UseOrderId: tradeNo,
		OrderAmt:   "0",
		PayAmt:     "0",
		PrftAmt:    "0",
		StoreId2:   "lsxd",
		UseTm:      usTm.Format(defaultFormatLayout),
	})
	return err
}
