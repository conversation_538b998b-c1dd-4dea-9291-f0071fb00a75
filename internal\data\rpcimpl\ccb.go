package rpcimpl

import (
	"bytes"
	"crypto/rsa"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"
	"unicode/utf8"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/conf"
	"ccb/internal/pkg/crypto"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

type CCBRpcImpl struct {
	conf       *conf.Bootstrap
	logHelper  *log.Helper
	aesKeys    map[string][]byte
	ccbPubKeys map[string]*rsa.PublicKey
	prikeys    map[string]*rsa.PrivateKey
}

func NewCCBRpcImpl(conf *conf.Bootstrap, logHelper *log.Helper) rpc.CCBRpc {
	ccbimpl := &CCBRpcImpl{
		conf: conf, logHelper: log<PERSON>elper, ccbPubKeys: make(map[string]*rsa.PublicKey),
		prikeys: make(map[string]*rsa.PrivateKey), aesKeys: make(map[string][]byte),
	}
	for s := range conf.CcbMap {
		if conf.CcbMap[s].PublicKey != "" {
			ccbimpl.ccbPubKeys[s], _ = crypto.PublicKey(conf.CcbMap[s].PublicKey)
		}
		if conf.CcbMap[s].PrivateKey != "" {
			ccbimpl.prikeys[s], _ = crypto.PrivateKey(conf.CcbMap[s].PrivateKey)
		}
		ccbimpl.aesKeys[s], _ = base64.StdEncoding.DecodeString(conf.CcbMap[s].AesKey)
	}
	return ccbimpl
}

// CouponOrderNotify 优惠券领取结果异步回调
func (c *CCBRpcImpl) CouponOrderNotify(mrhId string, reqBo *bo.CCBCouponOrderNotifyReq) (*do.CodeMsg, error) {
	transCode := "P5STDR001"
	isJf := false
	if strings.HasPrefix(reqBo.OrderId, "CCVCM") {
		isJf = true
	}
	header := c.requestHeader(mrhId, "JSON", transCode, isJf)
	body, _ := json.Marshal(reqBo)
	resp, err := c.post(fmt.Sprintf("/api/%s", transCode), header, body)
	if err != nil {
		c.logHelper.Errorf("CouponOrderNotify error: %s", err.Error())
		return nil, err
	}
	ret := &do.CodeMsg{}
	err = json.Unmarshal(resp, &ret)
	return ret, err
}

// CouponStatusNotify 优惠券状态变动回调
func (c *CCBRpcImpl) CouponStatusNotify(mrhId string, reqBo *bo.CCBCouponStatusNotifyReq) (*do.CodeMsg, error) {
	transCode := "P5STDR002"
	isJf := false
	if strings.HasPrefix(reqBo.OrderId, "CCVCM") {
		isJf = true
	}
	header := c.requestHeader(mrhId, "JSON", transCode, isJf)
	body, _ := json.Marshal(reqBo)
	resp, err := c.post(fmt.Sprintf("/api/%s", transCode), header, body)
	if err != nil {
		c.logHelper.Errorf("CouponStatusNotify error: %s", err.Error())
		return nil, err
	}
	c.logHelper.Infof("请求建行优惠券状态变动回调(%s)返回:%s", transCode, string(resp))
	ret := &do.CodeMsg{}
	err = json.Unmarshal(resp, &ret)
	return ret, err
}

// RechargeNotify 充值回调
func (c *CCBRpcImpl) RechargeNotify(mrhId string, reqBo *bo.CCBRechargeNotifyReq) (*do.CodeMsg, error) {
	transCode := "P5STDR003"
	isJf := false
	if strings.HasPrefix(reqBo.OrderId, "CCVCM") {
		isJf = true
	}
	header := c.requestHeader(mrhId, "JSON", transCode, isJf)
	body, _ := json.Marshal(reqBo)
	resp, err := c.post(fmt.Sprintf("/api/%s", transCode), header, body)
	if err != nil {
		c.logHelper.Errorf("RechargeNotify error: %s", err.Error())
		return nil, err
	}
	ret := &do.CodeMsg{}
	err = json.Unmarshal(resp, &ret)
	return ret, err
}

func (c *CCBRpcImpl) requestHeader(mrhId string, format, transCode string, isJf bool) map[string]string {
	header := make(map[string]string)
	header["Version"] = "0.9"
	header["Content-Type"] = "application/octet-stream;charset=UTF-8"
	header["Format"] = format // JSON  FILE
	header["Channel-Id"] = mrhId
	header["Platform-Id"] = c.conf.CcbMap[mrhId].PlatformId
	header["Trans-Code"] = transCode
	header["Trace-Id"] = uuid.New().String()
	header["Request-Time"] = time.Now().Format("20060102150405")
	header["Encrypt-Method"] = string(valobj.ECB) // AES/GCM/PKCS5Padding
	header["Sign-Method"] = "SHA256withRSA"
	header["Sign-Type"] = string(valobj.STANDARD)
	if format == "FILE" {
		header["Sign-Type"] = string(valobj.HEADER)
	}
	if !isJf {
		header["Scene-Id"] = "JXXJ"
	}
	return header
}

func (c *CCBRpcImpl) ResponseHeader(header map[string]string, data []byte) map[string]string {
	resHeader := make(map[string]string)
	resHeader["Version"] = header["Version"]
	resHeader["Content-Type"] = header["Content-Type"]
	resHeader["Format"] = "JSON"
	resHeader["Channel-Id"] = header["Channel-Id"]
	resHeader["Trans-Code"] = header["Trans-Code"]
	resHeader["Trace-Id"] = header["Trace-Id"]
	resHeader["Respond-Time"] = time.Now().Format("20060102150405")
	resHeader["Encrypt-Method"] = string(valobj.ECB) // AES/GCM/PKCS5Padding
	resHeader["Sign-Method"] = "SHA256withRSA"
	resHeader["Sign-Type"] = string(valobj.STANDARD)
	resHeader["Signature"], _ = c.signHeader(header["Channel-Id"], resHeader, data)
	/*if _, ok := header["Scene-Id"]; !ok { // 空值 – 默认场景 JXXJ – 建信消金
		resHeader["Scene-Id"] = header["Scene-Id"]
	}*/
	return resHeader
}

func (c *CCBRpcImpl) signHeader(mrhId string, header map[string]string, body []byte) (string, error) {
	if _, ok := header["Channel-Id"]; !ok {
		return "", errors.New("渠道id不能为空")
	}
	hs := ""
	if header["Sign-Type"] == string(valobj.STANDARD) {
		if len(body) > 0 {
			hs = "&Hash=" + crypto.Sha256Sign(body)
		}
	}
	tm := "Respond-Time"
	if _, ok := header[tm]; !ok {
		tm = "Request-Time"
	}
	signstr := fmt.Sprintf("Version=%s&Channel-Id=%s&Trans-Code=%s&Trace-Id=%s&%s=%s%s", header["Version"], header["Channel-Id"], header["Trans-Code"], header["Trace-Id"], tm, header[tm], hs)
	return crypto.Sha256WithRsaSign([]byte(signstr), c.prikeys[mrhId])
}

func (c *CCBRpcImpl) post(uri string, header map[string]string, body []byte) ([]byte, error) {
	client := &http.Client{
		Transport: &http.Transport{ // 配置连接池
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   60 * time.Second,
				KeepAlive: 60 * time.Second,
			}).DialContext,
			IdleConnTimeout: 30 * time.Second,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	c.logHelper.Infof("请求建行:url:%s,request:%s", uri, string(body))
	body, err := c.EncryptData(header, body)
	if err != nil {
		return nil, err
	}
	mrhid := header["Channel-Id"]
	if _, ok := header["Platform-Id"]; ok {
		header["Channel-Id"] = header["Platform-Id"]
		delete(header, "Platform-Id")
	}
	if _, ok := header["Signature"]; !ok {
		sign, err := c.signHeader(mrhid, header, body)
		if err != nil {
			return nil, err
		}
		header["Signature"] = sign
	}
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s%s", c.conf.CcbMap[mrhid].Url, uri), bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	for s := range header {
		req.Header.Set(s, header[s])
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	c.logHelper.Infof("请求建行返回:url:%s,未解密resp:%s,header:%v", uri, string(respData), header)
	if utf8.RuneCountInString(string(respData)) < 16 {
		return nil, errors.New(string(respData))
	}
	for s := range resp.Header {
		header[s] = resp.Header.Get(s)
	}
	header["Channel-Id"] = mrhid
	respstr, err := c.DecryptData(header, respData)
	if err != nil {
		return nil, err
	}
	c.logHelper.Infof("请求建行返回:url:%s,resp:%s", uri, string(respstr))
	return respstr, nil
}

func (c *CCBRpcImpl) VerifySignature(header map[string]string, body []byte) (err error) {
	if header["Signature"] == "" {
		return errors.New("no signature")
	}
	hs := ""
	if header["Sign-Type"] == string(valobj.STANDARD) {
		if len(body) > 0 {
			hs = "&Hash=" + crypto.Sha256Sign(body)
		}
	}
	signstr := fmt.Sprintf("Version=%s&Channel-Id=%s&Trans-Code=%s&Trace-Id=%s&Request-Time=%s%s", header["Version"], header["Channel-Id"], header["Trans-Code"], header["Trace-Id"], header["Request-Time"], hs)
	if header["Sign-Method"] == "SHA256withRSA" {
		err = crypto.Sha256WithRsaVerify(signstr, header["Signature"], c.ccbPubKeys[header["Channel-Id"]])
	} else {
		err = errors.New(header["Sign-Method"] + " not support")
	}
	return
}

// DecryptData 解密报文
func (c *CCBRpcImpl) DecryptData(header map[string]string, body []byte) (data []byte, err error) {
	mode := crypto.GCMMode
	if header["Encrypt-Method"] == string(valobj.ECB) {
		mode = crypto.ECBMode
	}
	return crypto.NewAESCipher(c.aesKeys[header["Channel-Id"]], mode, crypto.Pkcs7, crypto.PrintByte).Decrypt(string(body))
}

// EncryptData 加密报文
func (c *CCBRpcImpl) EncryptData(header map[string]string, body []byte) (data []byte, err error) {
	mode := crypto.GCMMode
	if header["Encrypt-Method"] == string(valobj.ECB) {
		mode = crypto.ECBMode
	}
	return crypto.NewAESCipher(c.aesKeys[header["Channel-Id"]], mode, crypto.Pkcs7, crypto.PrintByte).Encrypt(body)
}

// SendReconciliation 推送对账单
func (c *CCBRpcImpl) SendReconciliation(mrhId, batchNum string, body []byte) (*do.CodeMsg, error) {
	transCode := "P5STDE001"
	header := c.requestHeader(mrhId, "FILE", transCode, false)
	resp, err := c.post(fmt.Sprintf("/api/%s?batchNum=%s", transCode, batchNum), header, body)
	if err != nil {
		c.logHelper.Errorf("推送对账单%s失败:%s", batchNum, err.Error())
		return nil, err
	}
	ret := &do.CodeMsg{}
	err = json.Unmarshal(resp, &ret)
	return ret, err
}

// EncryptCpn 加密券码
func (c *CCBRpcImpl) EncryptCpn(channelId string, body []byte) (data []byte, err error) {
	body = reverseByte(string(body))
	return crypto.NewAESCipher(c.aesKeys[channelId], crypto.CBCMode, crypto.Pkcs7, crypto.PrintBase64).Encrypt(body)
}

// DecryptCpn 解密券码
func (c *CCBRpcImpl) DecryptCpn(channelId string, body []byte) (data []byte, err error) {
	data, err = crypto.NewAESCipher(c.aesKeys[channelId], crypto.CBCMode, crypto.Pkcs7, crypto.PrintBase64).Decrypt(string(body))
	if err != nil {
		return nil, err
	}
	data = reverseByte(string(data))
	return data, nil
}

func reverseByte(str string) []byte {
	return []byte(ReverseString(str))
}

func ReverseString(str string) string {
	byt := []rune(str)
	length := len(byt)
	for i := 0; i < length-1; i += 2 {
		byt[i], byt[i+1] = byt[i+1], byt[i]
	}
	str = string(byt)
	return str
}
