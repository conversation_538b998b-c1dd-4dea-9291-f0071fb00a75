package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type OrderDo struct {
	Id                  int                `json:"id"`
	OrderNumber         string             `json:"order_number"`
	UpstreamOrderNumber string             `json:"upstream_order_number"`
	UserId              string             `json:"user_id"`
	Type                valobj.OrderType   `json:"type"` // 1直充  2优惠券
	MerchantId          string             `json:"merchant_id"`
	UpstreamMerchantID  string             `json:"upstream_merchant_id"`
	ProductId           int                `json:"product_id"`
	ProductCode         string             `json:"product_code"`
	Number              int                `json:"number"`
	TotalFee            valobj.Money       `json:"total_fee"` // 支付金额
	Price               valobj.Money       `json:"price"`     // 订单金额
	Status              valobj.OrderStatus `json:"status"`
	ActivityId          string             `json:"activity_id"`
	PayInfo             string             `json:"pay_info"`
	ExtraData           string             `json:"extra_data"`
	ExpiredTime         *time.Time         `json:"expired_time"`
	CreateTime          *time.Time         `json:"create_time"`
	UpdateTime          *time.Time         `json:"update_time"`
	OurAppId            string             `json:"our_app_id"`
	SendMchId           string             `json:"send_mch_id"`
	CreatorMchId        string             `json:"creator_mch_id"`
	BatchNo             string             `json:"batch_no"`
}
