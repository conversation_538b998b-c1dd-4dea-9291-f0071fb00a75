// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"ccb/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type Stock struct {
	ent.Schema
}

func (Stock) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("product_id"),
		field.String("product_code").Comment("产品编码"),
		field.String("code").Comment("内部产品编码"),
		field.Int("status").Comment("状态"),
		field.String("merchant_id").Comment("商户ID"),
		field.String("activity_id").Comment("总库存"),
		field.String("batch_no").Comment("借记卡(01)批次"),
		field.String("batch_no2").Comment("信用卡(02)批次"),
		field.Int("total").Comment("总库存"),
		field.Int("residue").Comment("剩余库存"),
		field.Int("used_num").Comment("已使用库存"),
		field.Int("account_type").GoType(valobj.AccountType(0)).Default(0).Comment("领取卡密时,关联账号类型:0默认 1手机号"),
		field.String("key").Default("test01").Comment("使用配置key 直充商品为直连天下配置的key, 兑换商品为易码通配置的key"),
		field.Time("created_time").Optional().Comment("创建时间"),
		field.Time("updated_time").Optional().Comment("更新时间"),
		field.Int("send_type").GoType(valobj.StockSendType(0)).Default(0).Comment("发送方式 1手机"),
	}
}

func (Stock) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("product", Product.Type).Ref("stock").Unique().Field("product_id").Required(),
	}
}

func (Stock) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "stock"}}
}
