package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type ReconciliationDo struct {
	Id         int                         `json:"id"`
	Number     string                      `json:"number"`
	File       string                      `json:"file"`
	MerchantId string                      `json:"merchant_id"`
	StartDate  *time.Time                  `json:"start_date"`
	EndDate    *time.Time                  `json:"end_date"`
	Status     valobj.ReconciliationStatus `json:"status"` // 1待推送  2已推送  3已处理  4处理失败
	Msg        string                      `json:"msg"`
	CreateTime *time.Time                  `json:"create_time"`
	UpdateTime *time.Time                  `json:"update_time"`
}
