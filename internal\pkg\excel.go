package pkg

import (
	"bytes"
	"fmt"
	"io"

	"github.com/xuri/excelize/v2"
)

type Excel struct {
	file             *excelize.File // excel 文件
	currentSheet     string         // 当前活动 sheet
	currentSheetLine int            // 当前 sheet 行
}

// NewExcel 创建excel
func NewExcel(sheets ...string) (*Excel, error) {
	f := excelize.NewFile()
	excel := &Excel{
		file: f,
	}
	if len(sheets) > 0 {
		if err := f.SetSheetName("Sheet1", sheets[0]); err != nil {
			return nil, err
		}
		for i := 1; i < len(sheets); i++ {
			_, err := f.NewSheet(sheets[i])
			if err != nil {
				return nil, err
			}

		}
	} else {
		sheets = []string{"Sheet1"}
	}
	excel.currentSheet = sheets[0]
	excel.currentSheetLine = 1
	return excel, nil
}

// OpenFile 打开excel
func OpenFile(excelName string, sheet ...string) (*Excel, error) {
	f, err := excelize.OpenFile(excelName)
	if err != nil {
		return nil, err
	}
	res := &Excel{
		file: f,
	}
	if len(sheet) == 1 {
		err = res.SetActiveSheet(sheet[0])
	} else {
		index := f.GetActiveSheetIndex()
		res.currentSheet = f.GetSheetName(index)
	}
	return res, err
}

// OpenReader 打开excel
func OpenReader(r io.Reader, sheet ...string) (*Excel, error) {
	f, err := excelize.OpenReader(r)
	if err != nil {
		return nil, err
	}
	res := &Excel{
		file: f,
	}
	if len(sheet) == 1 {
		err = res.SetActiveSheet(sheet[0])
	} else {
		index := f.GetActiveSheetIndex()
		res.currentSheet = f.GetSheetName(index)
	}
	return res, err
}

// SetSheetHeader 设置表头
func (e *Excel) SetSheetHeader(header []string) error {
	if err := e.file.SetSheetRow(e.currentSheet, fmt.Sprintf("A%d", e.currentSheetLine), &header); err != nil {
		return err
	}
	e.currentSheetLine++
	return nil
}

// SetSheetList 设置全部数据
func (e *Excel) SetSheetList(list [][]interface{}) error {
	for _, data := range list {
		return e.SetSheetRow(data)
	}
	return nil
}

// SetSheetRow 设置行数据
func (e *Excel) SetSheetRow(data []interface{}) error {
	if err := e.file.SetSheetRow(e.currentSheet, fmt.Sprintf("A%d", e.currentSheetLine), &data); err != nil {
		return err
	}
	e.currentSheetLine++
	return nil
}

// GetCurrentSheetLine 获取当前活动 sheet 行数
func (e *Excel) GetCurrentSheetLine() int {
	return e.currentSheetLine
}

// SetActiveSheet 设置当前活动
// attention: 如果设置的sheet不存在,会报错;
// 如果设置活动表后,会自动切换到设置的sheet，之前sheet表不能再操作，不然数据会被覆盖；
func (e *Excel) SetActiveSheet(sheet string) error {
	sheetIndex, err := e.file.GetSheetIndex(sheet)
	if err != nil {
		return fmt.Errorf("sheet %s not found,err: %s", sheet, err.Error())
	}
	if sheetIndex == -1 {
		return fmt.Errorf("sheet %s not found", sheet)
	}
	e.file.SetActiveSheet(sheetIndex)
	e.currentSheet = sheet
	return nil
}

// Save 保存
func (e *Excel) Save(path string) error {
	return e.file.SaveAs(path)
}

// GetBuffer 获取 excel buffer
func (e *Excel) GetBuffer() (*bytes.Buffer, error) {
	return e.file.WriteToBuffer()
}

// Write 写入
func (e *Excel) Write(w io.Writer) error {
	return e.file.Write(w)
}

// GetRows 获取行
func (e *Excel) GetRows() ([][]string, error) {
	return e.file.GetRows(e.currentSheet)
}

// Close 关闭
func (e *Excel) Close() error {
	return e.file.Close()
}
