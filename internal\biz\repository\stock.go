package repository

import (
	"context"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
)

type StockRepo interface {
	// GetE 通过 id 获取一条数据
	GetE(ctx context.Context, id int) (*do.StockDo, error)

	// FindE 通过多个 id 获取多条数据
	FindE(ctx context.Context, ids ...int) ([]*do.StockDo, error)

	// CreateE 创建数据
	CreateE(ctx context.Context, creatData *do.StockDo) (*do.StockDo, error)

	// CreateBulkE 批量创建数据
	CreateBulkE(ctx context.Context, dos []*do.StockDo) ([]*do.StockDo, error)

	// UpdateE 更新数据
	UpdateE(ctx context.Context, updateData *do.StockDo) (int, error)

	UpdateUsed(ctx context.Context, id, num int) error

	UpdateResidue(ctx context.Context, id, num int) error

	FindOne(ctx context.Context, req *bo.ProductStockBo) (*do.StockDo, error)

	FindAll(ctx context.Context, req *bo.ProductStockListBo) ([]*do.StockDo, *bo.RespPageBo, error)
}
