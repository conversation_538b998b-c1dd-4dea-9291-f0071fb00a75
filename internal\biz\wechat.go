package biz

import (
	"context"
	"fmt"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/valobj"
	"ccb/internal/biz/wechatrepository"
)

type WechatBiz struct {
	wechatCpnRepo wechatrepository.WechatCpnRepo
}

func NewWechatBiz(wechatCpnRepo wechatrepository.WechatCpnRepo) *WechatBiz {
	return &WechatBiz{wechatCpnRepo: wechatCpnRepo}
}

func (this *WechatBiz) SendCoupon(ctx context.Context) {
	// demo
	couponId, err := this.wechatCpnRepo.SendCoupon(ctx, nil)
	fmt.Println("券id", couponId, err)
}

func (this *WechatBiz) QueryCoupon(ctx context.Context) {
	// demo
	resp, _ := this.wechatCpnRepo.QueryCoupon(ctx, nil)

	switch *resp.Status {
	case valobj.WechatCpnStatusSended.GetValue():
		// todo
	case valobj.WechatCpnStatusUsed.GetValue():
		// todo
	case valobj.WechatCpnStatusExpired.GetValue():
	// todo
	default:
		// todo
	}
}

func (this *WechatBiz) QueryStock(ctx context.Context) {
	// demo
	resp, _ := this.wechatCpnRepo.QueryStock(ctx, "", "", "")

	fmt.Printf("最大发券数量%d", resp.StockUseRule.MaxCoupons)
	fmt.Printf("已发券数量%d", resp.DistributedCoupons)
}

func (this *WechatBiz) Notify(ctx context.Context, req *bo.WechatCpnNotifyBo) {
	// demo
	fmt.Printf("券创建商户%s", req.PlainText.StockCreatorMchid)
	fmt.Printf("券批次号%s", req.PlainText.StockID)
	fmt.Printf("券id%s", req.PlainText.CouponID)

	switch req.PlainText.Status {
	case valobj.WechatCpnStatusSended:
		// todo
	case valobj.WechatCpnStatusUsed:
		// todo
	case valobj.WechatCpnStatusExpired:
	// todo
	default:
		// todo
	}
}

func (this *WechatBiz) RegisterNotifyTag(ctx context.Context) {
	// demo
	// 说明：批次商品创建后需要请求该接口注册回调通知tag，所属的制券商户消费通知回调地址设置为“刚哥那边的统一回调转发中心服务”，建议注册数据留存
	// 发券成功后，用户使用消费，队列消费所对应的组的tag的券消费通知数据
	_, err := this.wechatCpnRepo.RegisterNotifyTag(ctx, "")
	fmt.Println(err)
}
