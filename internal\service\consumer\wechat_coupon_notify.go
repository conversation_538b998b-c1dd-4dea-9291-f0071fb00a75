package consumer

import (
	"context"
	"encoding/json"

	"ccb/internal/biz"
	"ccb/internal/biz/bo"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
)

type WechatCouponService struct {
	bc        *conf.Bootstrap
	logger    *log.Helper
	wechatBiz *biz.WechatBiz
	ccbBiz    *biz.CCBBiz
}

func NewWechatCouponService(
	bc *conf.Bootstrap,
	logger *log.Helper,
	wechatBiz *biz.WechatBiz,
	ccbBiz *biz.CCBBiz,
) *WechatCouponService {
	return &WechatCouponService{
		bc:        bc,
		logger:    logger,
		wechatBiz: wechatBiz,
		ccbBiz:    ccbBiz,
	}
}

func (this *WechatCouponService) Notify(ctx context.Context, tag, msg string) error {
	this.logger.Infof("消费监听立减金消费:%s", msg)
	var req *bo.WechatCpnNotifyBo

	if err := json.Unmarshal([]byte(msg), &req); err != nil {
		return err
	}
	err := this.ccbBiz.HandleWeChatCpnNotify(ctx, req)
	if err != nil {
		this.logger.Errorf("消费监听立减金消费失败:%s", err.Error())
	}
	return err
}
