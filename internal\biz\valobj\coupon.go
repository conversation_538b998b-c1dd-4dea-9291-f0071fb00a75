package valobj

type CouponStatus int

// 1 可用  2 已使用  3使用完 4已核销 5 作废 6 过期
const (
	CouponStatusUnkown CouponStatus = iota
	CouponStatusUnused
	CouponStatusUsed
	CouponStatusOut
	CouponStatusVerify
	CouponStatusCancel
	CouponStatusExpired
)

var couponStatusMap = map[CouponStatus]string{
	CouponStatusUnkown:  "未知",
	CouponStatusUnused:  "未使用",
	CouponStatusUsed:    "已使用",
	CouponStatusOut:     "使用完",
	CouponStatusVerify:  "已核销",
	CouponStatusCancel:  "已作废",
	CouponStatusExpired: "已过期",
}

func (r CouponStatus) GetName() string {
	return couponStatusMap[r]
}

func (r CouponStatus) GetValue() int {
	return int(r)
}
