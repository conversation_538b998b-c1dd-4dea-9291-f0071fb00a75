package repositoryimpl

import (
	"context"
	"errors"
	"time"

	"ccb/internal/biz/valobj"

	"ccb/internal/biz/bo"
	"ccb/internal/data/ent/predicate"

	"ccb/internal/data/ent/reconciliation"

	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/data"
	"ccb/internal/data/ent"
)

type ReconciliationRepoImpl struct {
	Base[ent.Reconciliation, do.ReconciliationDo, ent.ReconciliationQuery]
	data *data.Data
}

func NewReconciliationRepoImpl(data *data.Data) repository.ReconciliationRepo {
	return &ReconciliationRepoImpl{data: data}
}

// ToEntity 转换成实体
func (r *ReconciliationRepoImpl) ToEntity(po *ent.Reconciliation) *do.ReconciliationDo {
	if po == nil {
		return nil
	}
	dc := r.Base.ToEntity(po)
	dc.StartDate = &po.StartDate
	dc.EndDate = &po.EndDate
	dc.CreateTime = &po.CreateTime
	dc.UpdateTime = &po.UpdateTime
	return dc
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (r *ReconciliationRepoImpl) ToEntities(pos []*ent.Reconciliation) []*do.ReconciliationDo {
	entities := make([]*do.ReconciliationDo, len(pos))
	for k, p := range pos {
		entities[k] = r.ToEntity(p)
	}
	return entities
}

func (r *ReconciliationRepoImpl) CreateE(ctx context.Context, d *do.ReconciliationDo) (*do.ReconciliationDo, error) {
	row, err := r.data.GetDb(ctx).Reconciliation.Create().
		SetCreateTime(time.Now()).
		SetStatus(1).
		SetMerchantID(d.MerchantId).
		SetNumber(d.Number).
		SetFile(d.File).
		SetMsg(d.Msg).
		SetStartDate(*d.StartDate).
		SetEndDate(*d.EndDate).
		Save(ctx)
	return r.ToEntity(row), err
}

func (r *ReconciliationRepoImpl) UpdateStatus(ctx context.Context, id int, status valobj.ReconciliationStatus) (*do.ReconciliationDo, error) {
	row, err := r.data.GetDb(ctx).Reconciliation.UpdateOneID(id).
		SetStatus(status.GetValue()).
		SetUpdateTime(time.Now()).
		Save(ctx)
	return r.ToEntity(row), err
}

func (r *ReconciliationRepoImpl) UpdateStatusByNumber(ctx context.Context, number, msg string, status valobj.ReconciliationStatus) error {
	_, err := r.data.GetDb(ctx).Reconciliation.Update().
		Where(reconciliation.NumberEQ(number)).
		SetStatus(status.GetValue()).
		SetMsg(msg).
		SetUpdateTime(time.Now()).
		Save(ctx)
	return err
}

func (r *ReconciliationRepoImpl) FindByNumber(ctx context.Context, number string) (*do.ReconciliationDo, error) {
	row, err := r.data.GetDb(ctx).Reconciliation.Query().
		Where(reconciliation.NumberEQ(number)).
		First(ctx)
	return r.ToEntity(row), err
}

func (r *ReconciliationRepoImpl) SearchList(ctx context.Context, reqBo *bo.ReconciliationListBo) ([]*do.ReconciliationDo, error) {
	where := r.setQuery(reqBo)
	if len(where) == 0 {
		return nil, errors.New("条件不能为空")
	}
	query := r.data.GetDb(ctx).Reconciliation.Query().Where(where...)
	rows, err := query.All(ctx)
	if err != nil {
		return nil, err
	}
	return r.ToEntities(rows), err
}

func (r *ReconciliationRepoImpl) setQuery(reqBo *bo.ReconciliationListBo) []predicate.Reconciliation {
	where := make([]predicate.Reconciliation, 0)
	if reqBo.Number != "" {
		where = append(where, reconciliation.Number(reqBo.Number))
	}
	if reqBo.BeginDate != nil {
		where = append(where, reconciliation.StartDate(*reqBo.BeginDate))
	}
	if reqBo.EndDate != nil {
		where = append(where, reconciliation.EndDate(*reqBo.EndDate))
	}
	if len(reqBo.Status) > 0 {
		where = append(where, reconciliation.StatusIn(reqBo.Status...))
	}
	return where
}
