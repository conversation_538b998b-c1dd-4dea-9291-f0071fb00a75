package biz

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/conf"
	"ccb/internal/data/ent"

	"github.com/go-kratos/kratos/v2/log"
)

var defaultChannelId = "2024CCB"

type MarketBiz struct {
	c             *conf.Bootstrap
	log           *log.Helper
	marketRpc     rpc.MarketRpc
	ccbRpc        rpc.CCBRpc
	orderRepo     repository.OrderRepo
	couponRepo    repository.CouponRepo
	couponLogRepo repository.CouponLogRepo
	stockRepo     repository.StockRepo
}

func NewMarketBiz(c *conf.Bootstrap, log *log.Helper, marketRpc rpc.MarketRpc, ccbRpc rpc.CCBRpc, orderRepo repository.OrderRepo, couponRepo repository.CouponRepo, couponLogRepo repository.CouponLogRepo, stockRepo repository.StockRepo) *MarketBiz {
	return &MarketBiz{c: c, log: log, marketRpc: marketRpc, ccbRpc: ccbRpc, orderRepo: orderRepo, couponRepo: couponRepo, couponLogRepo: couponLogRepo, stockRepo: stockRepo}
}

func (m *MarketBiz) CouponReceiveNotify(req *bo.MarketCouponReceiveNotifyReq) error {
	reqStr, err := json.Marshal(req)
	if err != nil {
		return err
	}
	data := make(map[string]interface{})
	err = json.Unmarshal(reqStr, &data)
	if err != nil {
		return err
	}
	err = m.marketRpc.VerifySign(data)
	if err != nil {
		return err
	}
	if req.CnclSt != "4" && req.RedeemResult != "00" {
		return nil
	}
	return m.handle(req.VoucherCode, req.TradeNo, string(reqStr), "receive")
}

func (m *MarketBiz) CouponVerifyNotify(req *bo.MarketCouponVerifyNotifyReq) error {
	if req.CodeStatus == 3 { // 核销
		code, err := m.marketRpc.Decode(req.CardCode)
		if err != nil {
			return fmt.Errorf("coupon verify notify decode error: %s", err)
		}
		reqStr, _ := json.Marshal(req)
		return m.handle(code, req.TradeNo, string(reqStr), "verify")
	}
	return nil
}

func (m *MarketBiz) handle(code, tradeNo, reqStr, typ string) error {
	ctx := context.Background()
	coupon, err := m.couponRepo.FindByCode(ctx, defaultChannelId, code)
	if err != nil {
		if ent.IsNotFound(err) {
			return errors.New("优惠券不存在")
		}
		return fmt.Errorf("coupon verify notify find coupon error: %s", err)
	}
	if coupon.Residue == 0 {
		return nil
	}
	oldStatus := coupon.Status
	order, err := m.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		return fmt.Errorf("coupon verify notify find order error: %s", err)
	}
	stock, err := m.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductCode: order.ProductCode,
		MerchantId:  order.MerchantId,
	})
	if err != nil {
		return fmt.Errorf("coupon verify notify find productStock error: %s", err)
	}
	if typ == "receive" && stock.Product.Type == valobj.ProductTypeVoucherWechat {
		return nil
	}
	if coupon.Status == valobj.CouponStatusVerify {
		_, err = m.ccbRpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
			ProductId:  coupon.ProductCode,
			OrderId:    order.OrderNumber,
			CouponCode: coupon.Code,
			Operation:  valobj.CCBCouponOperationToUsed,
			UseOrderId: tradeNo,
			OrderAmt:   "0",
			PayAmt:     "0",
			PrftAmt:    "0",
			StoreId2:   "lsxd",
			UseTm:      time.Now().Format(defaultFormatLayout),
		})
		return err
	}
	queryResp, err := m.marketRpc.QueryKey(&bo.MarketQueryKeyReq{
		AppId:       order.UpstreamMerchantID,
		MemId:       order.UpstreamMerchantID,
		MobileNo:    order.UserId,
		Timestamp:   time.Now().Format(defaultFormatLayout),
		VoucherCode: coupon.Code,
		VoucherId:   coupon.ProductCode,
	})
	if err != nil {
		return fmt.Errorf("coupon verify notify query key error: %s", err)
	}
	coupon, err = m.couponRepo.UpdateStatus(ctx, coupon.Id, -1, coupon.Status, valobj.CouponStatusVerify, coupon.TransactionId)
	if err != nil {
		return fmt.Errorf("coupon verify notify update coupon error: %s", err)
	}
	if queryResp.Data.VoucherStatus != "4" {
		return fmt.Errorf("请求的状态和真实状态不一致: %s", queryResp.Data.VoucherStatus)
	}
	dateStatus := fmt.Sprintf("%d,%d", oldStatus, valobj.CouponStatusVerify)
	now := time.Now()
	couponLog, err := m.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
		BeginDate: &now,
		CouponId:  coupon.Id,
	})
	if err != nil && !ent.IsNotFound(err) {
		return fmt.Errorf("coupon verify notify find coupon log error: %s", err)
	}
	if couponLog != nil && couponLog.Id > 0 {
		ss := strings.Split(couponLog.DateStatus, ",")
		dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusVerify)
	}
	_, err = m.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
		CouponId:     coupon.Id,
		BeforeStatus: oldStatus,
		Status:       valobj.CouponStatusVerify,
		DateStatus:   dateStatus,
		RequestData:  string(reqStr),
	})
	if err != nil {
		m.log.Errorf("coupon verify notify create coupon log error: %s", err)
	}
	_, err = m.ccbRpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
		ProductId:  coupon.ProductCode,
		OrderId:    order.OrderNumber,
		CouponCode: coupon.Code,
		Operation:  valobj.CCBCouponOperationToUsed,
		UseOrderId: tradeNo,
		OrderAmt:   "0",
		PayAmt:     "0",
		PrftAmt:    "0",
		StoreId2:   "lsxd",
		UseTm:      time.Now().Format(defaultFormatLayout),
	})
	return err
}
