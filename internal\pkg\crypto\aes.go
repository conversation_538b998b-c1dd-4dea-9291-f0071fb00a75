package crypto

import (
	"crypto/aes"

	"github.com/pkg/errors"
)

type CipherAES struct {
	Cipher
}

func NewAESCipher(key []byte, groupMode GroupMode, fillMode FillMode, decodeType int) *CipherAES {
	return &CipherAES{
		Cipher{
			GroupMode:  groupMode,
			FillMode:   fillMode,
			DecodeType: decodeType,
			Key:        key,
		},
	}
}

func (c *CipherAES) Encrypt(plainText []byte) (cipherText []byte, err error) {
	block, err := aes.NewCipher(c.Key)
	if err != nil {
		return
	}
	plainData := c.Fill(plainText, block.BlockSize())
	if plainData == nil {
		err = errors.New("unsupported content to be encrypted")
		return
	}
	if err = c.DataEncrypt(block, plainData); err != nil {
		return
	}
	return c.Value(), nil
}

func (c *CipherAES) Decrypt(cipherText string) (plainText []byte, err error) {
	cipherData, err := c.Decode(cipherText)
	if err != nil {
		return
	}
	block, err := aes.NewCipher(c.Key)
	if err != nil {
		return
	}
	if err = c.DataDecrypt(block, cipherData); err != nil {
		return
	}
	return c.UnFill(c.Output)
}
