package rpcimpl

import (
	"context"
	"encoding/json"
	"fmt"

	"ccb/internal/biz/do"
	"ccb/internal/biz/rpc"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/core"
)

/*
易码通
*/

type YmtRpcImpl struct {
	conf      *conf.Bootstrap
	logHelper *log.Helper
}

func NewYmtRpcImpl(conf *conf.Bootstrap, logHelper *log.Helper) rpc.YmtRpc {
	return &YmtRpcImpl{conf: conf, logHelper: logHelper}
}

func (this *YmtRpcImpl) getService(ymtKey string) *key.Key {
	c, ok := this.conf.YmtMap[ymtKey]
	if !ok {
		c = this.conf.YmtMap["test01"]
	}
	cc, err := core.NewCore(&core.Config{
		SignType:   "RSA",
		AppID:      c.AppId,
		BaseURL:    c.Host,
		Key:        c.Key,
		PrivateKey: c.PrivateKey,
		PublicKey:  c.PublicKey,
	})
	if err != nil {
		panic(err)
	}
	return &key.Key{Core: cc}
}

// 领取key码
func (this *YmtRpcImpl) GetKey(in *key.OrderRequest, ymtKey string) (rsp do.OrderResponse, err error) {
	_, r, err := this.getService(ymtKey).Order(context.Background(), in)
	if err != nil {
		return
	}
	if r.Code != 200 {
		err = fmt.Errorf("code=%d msg=%s", r.Code, r.Message)
		return
	}
	err = json.Unmarshal(r.Data, &rsp)

	return
}

// 通知
func (this *YmtRpcImpl) Notify(req *key.Notify, ymtKey string) (*key.NotifyData, error) {
	n, err := this.getService(ymtKey).Notify(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("ymt err=%v", err)
	}
	return n, nil
}

// 查询
func (this *YmtRpcImpl) Query(outBizNo, ymtKey string) (rsp do.OrderResponse, err error) {
	req := key.QueryRequest{
		OutBizNo: outBizNo,
	}
	_, r, err := this.getService(ymtKey).Query(context.Background(), &req)
	if err != nil {
		return
	}
	if r.Code != 200 {
		err = fmt.Errorf("code=%d msg=%s", r.Code, r.Message)
		return
	}
	err = json.Unmarshal(r.Data, &rsp)
	return
}

// 作废
func (this *YmtRpcImpl) Discard(outBizNo, ymtKey string) (rsp do.OrderResponse, err error) {
	req := key.DiscardRequest{
		OutBizNo: outBizNo,
	}

	_, r, err := this.getService(ymtKey).Discard(context.Background(), &req)
	if err != nil {
		return
	}

	if r.Code != 200 {
		err = fmt.Errorf("code=%d msg=%s", r.Code, r.Message)
		return
	}
	err = json.Unmarshal(r.Data, &rsp)
	return
}
