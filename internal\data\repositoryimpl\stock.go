package repositoryimpl

import (
	"context"
	"errors"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/valobj"
	"ccb/internal/data"
	"ccb/internal/data/ent"
	"ccb/internal/data/ent/predicate"
	"ccb/internal/data/ent/product"
	"ccb/internal/data/ent/stock"
)

type StockRepoImpl struct {
	Base[ent.Stock, do.StockDo, ent.StockQuery]
	data *data.Data
}

// NewStockRepoImpl 创建 StockRepo的实现者
func NewStockRepoImpl(data *data.Data) repository.StockRepo {
	return &StockRepoImpl{data: data}
}

// ToEntity 转换成实体
func (s *StockRepoImpl) ToEntity(po *ent.Stock) *do.StockDo {
	if po == nil {
		return nil
	}
	return &do.StockDo{
		ID:          po.ID,
		ProductID:   po.ProductID,
		ProductCode: po.ProductCode,
		Code:        po.Code,
		Status:      valobj.IsEnable(po.Status),
		MerchantID:  po.MerchantID,
		ActivityID:  po.ActivityID,
		BatchNo:     po.BatchNo,
		BatchNo2:    po.BatchNo2,
		Total:       po.Total,
		Residue:     po.Residue,
		UsedNum:     po.UsedNum,
		AccountType: po.AccountType,
		Key:         po.Key,
		CreatedTime: po.CreatedTime,
		UpdatedTime: po.UpdatedTime,
		Product:     productRepoImpl.ToEntity(po.Edges.Product),
		SendType:    po.SendType,
	}
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (s *StockRepoImpl) ToEntities(pos []*ent.Stock) []*do.StockDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.StockDo, len(pos))
	for k, p := range pos {
		entities[k] = s.ToEntity(p)
	}
	return entities
}

// GetE 通过 id 获取一条数据
func (s *StockRepoImpl) GetE(ctx context.Context, id int) (*do.StockDo, error) {
	row, err := s.data.GetDb(ctx).Stock.Query().Where(stock.ID(id)).WithProduct().First(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(row), nil
}

// FindE 通过多个 id 获取多条数据
func (s *StockRepoImpl) FindE(ctx context.Context, ids ...int) ([]*do.StockDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := s.data.GetDb(ctx).Stock.Query().Where(stock.IDIn(ids...)).WithProduct().All(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntities(rows), nil
}

// CreateE 创建数据
func (s *StockRepoImpl) CreateE(ctx context.Context, creatData *do.StockDo) (*do.StockDo, error) {
	row, err := s.data.GetDb(ctx).Stock.Create().
		SetProductID(creatData.ProductID).
		SetProductCode(creatData.ProductCode).
		SetCode(creatData.Code).
		SetStatus(creatData.Status.GetValue()).
		SetMerchantID(creatData.MerchantID).
		SetActivityID(creatData.ActivityID).
		SetBatchNo(creatData.BatchNo).
		SetBatchNo2(creatData.BatchNo2).
		SetTotal(creatData.Total).
		SetResidue(creatData.Residue).
		SetUsedNum(creatData.UsedNum).
		SetSendType(creatData.SendType).
		SetAccountType(creatData.AccountType).
		SetKey(creatData.Key).
		SetCreatedTime(time.Now()).
		SetUpdatedTime(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntity(row), nil
}

// CreateBulkE 批量创建数据
func (s *StockRepoImpl) CreateBulkE(ctx context.Context, dos []*do.StockDo) ([]*do.StockDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent.StockCreate, len(dos))
	for i, item := range dos {
		values[i] = s.data.GetDb(ctx).Stock.Create().
			SetProductID(item.ProductID).
			SetProductCode(item.ProductCode).
			SetCode(item.Code).
			SetStatus(item.Status.GetValue()).
			SetMerchantID(item.MerchantID).
			SetActivityID(item.ActivityID).
			SetBatchNo(item.BatchNo).
			SetBatchNo2(item.BatchNo2).
			SetTotal(item.Total).
			SetResidue(item.Residue).
			SetUsedNum(item.UsedNum).
			SetSendType(item.SendType).
			SetAccountType(item.AccountType).
			SetKey(item.Key).
			SetCreatedTime(time.Now()).
			SetUpdatedTime(time.Now())
	}
	rows, err := s.data.GetDb(ctx).Stock.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return s.ToEntities(rows), nil
}

// UpdateE 更新数据
func (s *StockRepoImpl) UpdateE(ctx context.Context, updateData *do.StockDo) (int, error) {
	cnt, err := s.data.GetDb(ctx).Stock.Update().Where(stock.ID(updateData.ID)).
		SetProductID(updateData.ProductID).
		SetProductCode(updateData.ProductCode).
		SetCode(updateData.Code).
		SetStatus(updateData.Status.GetValue()).
		SetMerchantID(updateData.MerchantID).
		SetActivityID(updateData.ActivityID).
		SetBatchNo(updateData.BatchNo).
		SetBatchNo2(updateData.BatchNo2).
		SetTotal(updateData.Total).
		SetSendType(updateData.SendType).
		SetResidue(updateData.Residue).
		SetUsedNum(updateData.UsedNum).
		SetAccountType(updateData.AccountType).
		SetKey(updateData.Key).
		SetUpdatedTime(time.Now()).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

func (s *StockRepoImpl) UpdateUsed(ctx context.Context, id, num int) error {
	return s.data.GetDb(ctx).Stock.Update().Where(stock.ID(id)).
		AddUsedNum(num).
		SetUpdatedTime(time.Now()).
		Exec(ctx)
}

func (s *StockRepoImpl) UpdateResidue(ctx context.Context, id, num int) error {
	return s.data.GetDb(ctx).Stock.Update().Where(stock.ID(id)).
		AddResidue(num).
		SetUpdatedTime(time.Now()).
		Exec(ctx)
}

func (s *StockRepoImpl) FindOne(ctx context.Context, req *bo.ProductStockBo) (*do.StockDo, error) {
	where := s.setQuery(req)
	if len(where) == 0 {
		return nil, errors.New("条件不能为空")
	}
	row, err := s.data.GetDb(ctx).Stock.Query().Where(where...).WithProduct().First(ctx)
	return s.ToEntity(row), err
}

func (s *StockRepoImpl) FindAll(ctx context.Context, req *bo.ProductStockListBo) ([]*do.StockDo, *bo.RespPageBo, error) {
	where := s.setListQuery(req)
	if len(where) == 0 {
		return nil, nil, errors.New("条件不能为空")
	}
	respPage := new(bo.RespPageBo)
	query := s.data.GetDb(ctx).Stock.Query().Where(where...).WithProduct()
	if req.Page != nil {
		s.SetPageByBo(query, req.Page)
		respPage = s.QueryRespPage(ctx, query, req.Page)
	}
	row, err := query.Order(ent.Asc(stock.FieldID)).All(ctx)
	return s.ToEntities(row), respPage, err
}

func (s *StockRepoImpl) setQuery(reqBo *bo.ProductStockBo) []predicate.Stock {
	where := make([]predicate.Stock, 0)
	if reqBo.ProductId > 0 {
		where = append(where, stock.ProductID(reqBo.ProductId))
	}
	if reqBo.ProductCode != "" {
		where = append(where, stock.ProductCode(reqBo.ProductCode))
	}
	if reqBo.MerchantId != "" {
		where = append(where, stock.MerchantID(reqBo.MerchantId))
	}
	if reqBo.Status != 0 {
		where = append(where, stock.Status(reqBo.Status))
	}
	if reqBo.ActivityId != "" {
		where = append(where, stock.ActivityID(reqBo.ActivityId))
	}
	if reqBo.BatchNo != "" {
		where = append(where, stock.Or(
			stock.BatchNo(reqBo.BatchNo),
			stock.BatchNo2(reqBo.BatchNo),
		))
	}
	return where
}

func (s *StockRepoImpl) setListQuery(reqBo *bo.ProductStockListBo) []predicate.Stock {
	where := make([]predicate.Stock, 0)
	if reqBo.ProductId > 0 {
		where = append(where, stock.ProductID(reqBo.ProductId))
	}
	if reqBo.ProductCode != "" {
		where = append(where, stock.ProductCode(reqBo.ProductCode))
	}
	if reqBo.MerchantId != "" {
		where = append(where, stock.MerchantID(reqBo.MerchantId))
	}
	if reqBo.ProductType > 0 {
		productIds := s.data.GetDb(context.Background()).Product.Query().
			Where(product.Type(reqBo.ProductType)).
			Select(product.FieldID).
			IntsX(context.Background())
		if len(productIds) == 0 {
			productIds = append(productIds, 0)
		}
		where = append(where, stock.ProductIDIn(productIds...))
	}
	if reqBo.Status > 0 {
		where = append(where, stock.Status(reqBo.Status))
	}
	if len(reqBo.Statuses) > 0 {
		where = append(where, stock.StatusIn(reqBo.Statuses...))
	}
	return where
}
