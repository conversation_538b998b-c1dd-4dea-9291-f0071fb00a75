server:
  http:
    addr: 0.0.0.0:18007
    timeout: 30s
    isOpenSwagger: true
    isResponseReqHeaders: true
data:
  database:
    isDebug: true #记录sql日志
    useEntcache: false #是否使用二级缓存
    entcacheTTL: 60s #默认缓存时间，单位秒
    driver: mysql
    source: root:root@tcp(192.168.6.74:3307)/ccb_platform?parseTime=True&loc=Local
    maxIdle: 3 #最大的空闲连接数
    maxOpen: 10 #最大连接数，0表示不受限制
    maxLifetime: 8s #连接复用的最大生命周期
  redis:
    addr: 127.0.0.1:6379
    password:
    readTimeout: 3s
    writeTimeout: 3s
    poolSize: 5 #连接池大小，不配置，或配置为0表示不启用连接池
    minIdleConns: 1 #最小空闲连接数
    connMaxIdleTime: 30s #每个连接最大空闲时间，如果超过了这个时间会被关闭
logs:
#  business: business.log #业务日志路径：如果不写日志，则不配置或配置为空
#  access: access.log #访问日志路径：如果不写日志，则不配置或配置为空

#告警配置
alarm:
#  dingToken: 2e3169276a54375e3c81dc65237ec94f1bc9a27352fce7fbcf3a2492e8ccc862 #推送群机器人

sendSms:
  checkSmsCodeMaxNum: 3
  accessKeyId: LTAI4GJDGonszvbynzhtp9Rf
  accessKeySecret: ******************************
  isOpen: false
  sendCpnSms:
    templateCode: SMS_468315763
    signName: 蓝色兄弟

jwt:
  jwtKey: S#D12G3ac
  timeout: 86400s #24*60*60分钟
  privateKey: |-
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  publicKey: |-
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAugN9kUs5lYXsxf3XyUPV
    Rr2+gBpolKJj33CphESUExPuMjRaQ/mHku6ArKtLNGZLTx8psyQgvoJH908GfTEa
    gceqjzhpUAF8LcegFCMqJ2GGdUatMsLHiAXH0I7DJSfyKAeeRPaskjFVk0XvE9vA
    WDByLt4aKWcOJukOKjw5VscKkywrcS7Cuk0UConm7SLH1keG6IGedk96hyuvNIqL
    rE1Jw9wGYekoskOg7jnIKJIfyPB0USRAlXr/RzZYudE76519KuAQsoZppWRqDfTg
    rTb1IQE6eKDWw2Cl7UYUlO6qyodoaXkr7fXxgy+Pd/AlLFmkDr9XXLlCF67Zdc0r
    zQIDAQAB
    -----END PUBLIC KEY-----

rpc:
  attachmentDomain: http://*************:8004 # 附件服务域名地址

cron:
  createReconciliation: "56 8 * * *"

ccbMap:
      "2024CCB":
        url: https://youhui1.ccb.com
        merchantId: 2024CCB # 建行权益平台请求权益供应商的渠道标识
        platformId: lansexiongdi # 权益供应商请求建行权益平台的渠道标识
        upstreamMerchantId: "23329" #直连天下商户号
        marketMerchantId: "" #营销系统商户号
        ymtMerchantId: "26015" #易码通商户号
        aesKey: LBXkGHP8gvu5wF3Zq95ooQ== # AES报文加密密钥
        ourAppId: "wx9ed74283ad25bca1" # 微信主体
        publicKey: |- 
          -----BEGIN PUBLIC KEY-----
          MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgNqgg9F4UNB8kc8CHRTT
          yN9a7bmen6oL8hyYCsTKpYhsiXAs33/msitAblD4k9zrJJem7NNabnReKgSyOJDX
          9fajgLV7XCWT2D8WiUT8xAWwn5lydQBS0Mp+By/AAsrd1n+ijqLkf+dd2yGueTDe
          YR/Z3p0chyfWgQAf1Z/MOHKHOe06eXnySr+shu0GFZuaabkiTghO7bH45wWa0e8q
          yZzv0F3YvnJjVbJg7BHP9hynr9zmS5q8ckhblmAeB369F6523zhADZ3MYDaXks3V
          6HIj5jJmO4PxElb3pRlPh7k7+dp9WAH7If1xD6X5e9JkVH8RgZYQP5Z7H1yQ5AQA
          0wIDAQAB
          -----END PUBLIC KEY-----
        privateKey: |-
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

marketMap:
  "test001":
    merchantId: "test001"
    appId: "test001"
    platformId: "test001"
#    notifyUrl: "http://test.marketapi.1688sup.com"
    notifyUrl: "http://127.0.0.1:8008"
    merchantPublicKey: |-
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApB7XD1M5tvwZQ81nyuH8
      aIfD+Q7cSzfBmeEkQ9+uoL9YbAXUc+uO1rFn1Lo2I6yeZcgjDcd67yq2kzf/zhLR
      UbZnM4VrnAMuo5dzMzLR+Ie6CRP0r5PT50vl7hUGeRhubv5AoqWpWeIVetTMgGKb
      rSh9ViJv8dSrmNceexqlU1hy0Y3AsEInucQoGVdFfHCKW+2rNIIawpoIbidCCMiv
      UZDi7v3qkPQ4qB81e+5RRoMaq9SRPvMfXoEhOlaoMuZIL4T4UaMZBsPYJJlvwn/I
      Z/1WhS7H5vD6/fRxiPMHewUlNe1o8CMWdBFu8GGBAN2V/Hs52MmjFxq2GAoV4K1u
      XQIDAQAB
      -----END PUBLIC KEY-----
    publicKey: |-
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxCR0c0IzkzrckdNQWKu2
      bDJcwZY0x6hpfRT7FX259ZCHjEHG7W2LCYYiCkuHsW8TFw0adtdTMPPUl1OA/2sK
      b9IT2vqzIsFsUBMy7HiNxIU5eP7wwYYPxLlYhGjOtCLj0LVuXIXhWwAU8PE/h4sr
      EE81I9dWHD1FDClYwwB62nJz7hMDcaWTi3Lgit0pbcf/RpENi6pNidVCdJAYPXbT
      3GPVsKEF3vn9Rbf3JZohQOYVy+ILpTYogqdq91nrRgrtfP0Jf47afHd9j0BecRPs
      06K26EfShoKEcJ+5MPWJaNDtjgAngKF/GXNHi6l/vKGRguus8PNU7ZaGRikm/E4T
      zQIDAQAB
      -----END PUBLIC KEY-----
    privateKey: |-
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

zltxMap:
  "23329":
    merchantId: "23329"
    secretKey: "8db16e8cc8363ed4eb4c14f9520bcc32"
    notifyUrl: "http://***************:18000/api/zltx/order_notify"

wechat:
  - mchID: "1605446142" # 证书所属商户 蓝色兄弟服务商立减金配置
    name: "蓝色兄弟old"
    mchCertificateSerialNumber: "4D081089DEB385316CBDCB55C070287E4920AC76" #商户证书序列号
    wechatPayPublicKeyID: "" #微信支付公钥ID
  - mchID: "1710953361" # 证书所属商户 蓝色兄弟服务商立减金配置,新申请的商户号，招行使用的发券商户号
    name: "蓝色兄弟new"
    mchCertificateSerialNumber: "6006B8208815DB5EAC5BF2E783CB9D34082C3772" #商户证书序列号
    wechatPayPublicKeyID: "PUB_KEY_ID_0117109533612025031800326400002563" #微信支付公钥ID
  - mchID: "1652465541" # 证书所属商户 蓝色兄弟服务商立减金配置,新申请的商户号，招行使用的发券商户号
    name: "福建兴旺"
    mchCertificateSerialNumber: "1E3F2CE013203BA9C3DEFC5782FCD3329C3DAC1C" #商户证书序列号
    wechatPayPublicKeyID: "" #微信支付公钥ID

wechatNotifyMQ:
  accessKeyId: "LTAI5tPyV7FynQNTfEvbEBuX"
  accessKeySecret: "******************************"
  endPoint: "http://1389288909295870.mqrest.cn-hangzhou.aliyuncs.com"
  regionId: "hangzhou"
  instanceId: "MQ_INST_1389288909295870_BYSoMttI"
  topic: "notify"
  groupId: "GID_ccb_dev"  # 创建属于自己的项目标识消费组
  tag: "ccb_couponNotify_dev" #切记，不要随便消费其它tag
  isOpenConsumer: false #是否启动消费 true/false
  registerTagUrl: "https://wpcallbacks.api.1688sup.com/wechatPay/register_tag"

ymtMap:
  "test01":
    host: "https://gateway.dev.cdlsxd.cn"
    appId: "MRLLdpKzWRGI"
    publicKey: "MIIBCgKCAQEArH+p54Hu7qCN4eai5snIjvii0IcuR59O8blEbBkuURtwErekzh+TkwQPLIYfJMx4qFiNLxe8j92EX74E2b9FhjHgPa+YBvDYQTKt/LUvzm2gyxwNUJu6gOU3ypSmB+MQ9bHkna4v4InyATlAen3id6h6o5/sqfVcaBF8jEzAve77P531Qgsr+VHk3xVcxAfXoj0vvxOgqN0ns46paZnyP5An07x7SHD5I5BIDDIug42B8uZSwPP6dMiAFZZdEwIRVjoAPHfets1g0imDbqyLtZMvG4pnG72Y+ok/s4Ni5vyAuMx7BQSDADPIs/RUljdsbHoaorOEdryfEmK7SUpoHwIDAQAB"
    privateKey: "MIIEowIBAAKCAQEArH+p54Hu7qCN4eai5snIjvii0IcuR59O8blEbBkuURtwErekzh+TkwQPLIYfJMx4qFiNLxe8j92EX74E2b9FhjHgPa+YBvDYQTKt/LUvzm2gyxwNUJu6gOU3ypSmB+MQ9bHkna4v4InyATlAen3id6h6o5/sqfVcaBF8jEzAve77P531Qgsr+VHk3xVcxAfXoj0vvxOgqN0ns46paZnyP5An07x7SHD5I5BIDDIug42B8uZSwPP6dMiAFZZdEwIRVjoAPHfets1g0imDbqyLtZMvG4pnG72Y+ok/s4Ni5vyAuMx7BQSDADPIs/RUljdsbHoaorOEdryfEmK7SUpoHwIDAQABAoIBAAM6xKiT8zA+i0l+cvBVOM8Mcqfq0AABSI4YBOLtOcPE+r0u+QPu7WlkOxB/VlfvCBdGySXnkN00rYTJXvfgr+GKT4N4JSWEHthhpYZEsidwQAFtfgZTVBOTtw6w58bgBnJDKV+WYkkW7BP/yWhBsRrCacFkfh7UfC1qnmVWDom/xs10m1KZmXwkRkoeqrcRAkbEBBdWHfBTSez+Bm60pnnaymhKFpptfaP5E79rk98MXCqv0AWeMSrFlfPKMCGveYw08VbFybu2DLw+gloX9vGrJCbeE5AqxFvY9t/xH27gRYSVmwTtvmxoSbbd2eX8Rdrx2lzMNFcKvqqRM4CUOFECgYEA3RAucZ0ubpLnn+CsqxF8MW1C9rwvBmXzI+H7atlRoxWaIUGWZEeNJZKJryLN3UJCPlsnyT/NUy5aaYOmfy7jTm9at7iyRFAYyJPxdxEDOoCb7liU1AR2xdj12OJm6kkm2KeiYxNsMnzOpLIO+6+EgveCxpZYjTAXh/0LIMYvTAcCgYEAx8KmOVNM+EpMjYSSqj35UmGQrmbJoXiyDBsGveO4hOMtQqRn1WIYmQ0EJuQbpnUyZCFzR3wZWGrI51bc5aMbX91hkZkGzTR3/wps6xEGXMIyVdUQMMVr2k4oOBntPZJry1tqBQ4E8SI6wvVCcwPfYqF3k8GLV2g30wCBhodWLSkCgYEAguubZeP95LSSAsiP2jxfvfIEj5XBj9t/FMg4Gv6bb6dSgdGTHM6XVCIl8pf9LcWY788SkAh+NH4mQ4kZaQc+zWv7YQU8ZmK9WsTrY2Wq8NHMB2tksO/325Jzf0AOTUCHhhY5gM227WunAjkF0J8RLnMhezT9Opi5xurg1UcqKa8CgYA2sb0BhSVV9kHvnZTqt/1D6SLMHainZXyag+vcojUrbWg/DvS/IBTMExxH6b0IXp5lfCMsN3ZZFyPa85WX6ojcPqkwTE3oca1JT3lzAz5St3lAtw2vzN4y76NjePXUHfsVuvnUWHpR66VbK/v9sdnAD6XF9wBH9mkShNjyFKwrwQKBgGm6aiKKSU9Ns7o9X8fPv2+VnAJ32gUcRjOGkN1wGH8nxOe9TlZ/UieT0IQe25TXLQGD2eMslFhaQtw4A772op3tjoMYKauH+iFVHqIEXu7JWQo2Hy8/rA7s0z4yphgPklostsNXGX9CfE3ELIonGWIrcOD2W+Y6pDZfuTOyuK8S"
    key: "7cd883a5ff67f56d833b83bdcde4e3a0"


ccbProxyMap:
  "2024CCB":
    desKey: XK2WMSsDVWNcahkCQQDdAJOR5ziy9Cdv
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCH1nax22Jkl9zUiyWrPUwl4aqV52orLWY81qXlqkK40wV00zTrmeowu7m4+JjX6ueOUy68r4yzKOE+5U28lMopJZ/cVpMaq0iWhpF7b1+Bs5rUHiPba++Q53pHWSce4GcSaK0z1dg6SymlAL+7lNoNpGq7+jq9UN4ExEf2a3Of0wIDAQAB
