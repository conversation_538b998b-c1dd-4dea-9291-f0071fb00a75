package front

import (
	"ccb/internal/biz"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type ZltxNotifyService struct {
	logger  *log.Helper
	c       *conf.Bootstrap
	zltxBiz *biz.ZLTXBiz
}

func NewZltxNotifyService(logger *log.Helper, c *conf.Bootstrap, zltxBiz *biz.ZLTXBiz) *ZltxNotifyService {
	return &ZltxNotifyService{logger: logger, c: c, zltxBiz: zltxBiz}
}

// OrderNotify 订单结果回调
func (srv *ZltxNotifyService) OrderNotify(ctx http.Context) error {
	err := srv.zltxBiz.OrderNotify(ctx)
	if err != nil {
		return ctx.String(200, err.Error())
	}
	return ctx.String(200, "success")
}

// CouponStatusNotify 优惠券状态回调
func (srv *ZltxNotifyService) CouponStatusNotify(ctx http.Context) error {
	err := srv.zltxBiz.CouponStatusChange(ctx)
	if err != nil {
		return ctx.String(200, err.Error())
	}
	return ctx.String(200, "success")
}

func (srv *ZltxNotifyService) Products(ctx http.Context) error {
	list, err := srv.zltxBiz.ProductList(ctx)
	if err != nil {
		return ctx.String(200, err.Error())
	}
	return ctx.JSON(200, list)
}
