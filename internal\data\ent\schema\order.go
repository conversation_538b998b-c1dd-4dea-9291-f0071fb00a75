// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type Order struct {
	ent.Schema
}

func (Order) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("order_number").Unique().Comment("订单号"),
		field.String("upstream_order_number").Optional().Comment("上游订单号"),
		field.String("user_id").Comment("用户id"),
		field.Int("type").Comment("类型  1直充  2优惠券"),
		field.String("merchant_id").Comment("商户id"),
		field.String("upstream_merchant_id").Comment("上游商户id"),
		field.Int("product_id").Comment("产品id"),
		field.String("product_code").Comment("产品编码"),
		field.Int("number").Comment("数量"),
		field.Int("total_fee").Comment("支付金额（分）"),
		field.Int("price").Comment("订单金额（分）"),
		field.Int("status").Comment("状态"),
		field.String("activity_id").Comment("活动id"),
		field.String("pay_info").Optional().Comment("支付信息"),
		field.String("extra_data").Optional().Comment("扩展数据"),
		field.Time("expired_time").Optional(),
		field.Time("create_time").Optional(),
		field.Time("update_time").Optional(),
		field.String("our_app_id").Comment("我们的微信appid"),
		field.String("send_mch_id").Comment("发券商户号"),
		field.String("creator_mch_id").Comment("建券商户号"),
		field.String("batch_no").Comment("批次号"),
	}
}
func (Order) Edges() []ent.Edge {
	return nil
}
func (Order) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "order"}}
}
