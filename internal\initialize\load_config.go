package initialize

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
)

var (
	initData initializeData
	once     sync.Once
)

type initializeData struct {
	isCommandMode bool            // 是否是脚本模式
	flagconf      string          // 加载配置问价你的路径
	bc            *conf.Bootstrap // configs
}

func IsCommandMode() bool {
	loadConfig()
	return initData.isCommandMode
}

func LoadConfig() *conf.Bootstrap {
	loadConfig()
	return initData.bc
}

func loadConfig() {
	once.Do(func() {
		if len(os.Args) < 2 || strings.HasPrefix(os.Args[1], "-") {
			// server 模式
			flag.StringVar(&initData.flagconf, "conf", "./configs", "config path, eg: -conf config.yaml")
			flag.Parse()
		} else {
			// 命令行模式： ./server 命令名 -conf xxx
			// 特别处理：解析出-conf，flag 包解析不了这种格式
			for k, v := range os.Args {
				if v == "-conf" {
					initData.flagconf = os.Args[k+1]
					break
				}
			}
			initData.isCommandMode = true
		}

		// 支持子目录, 线上的系统配置会挂载为子目录
		sources := make([]config.Source, 0)
		err := filepath.Walk(initData.flagconf, func(path string, info os.FileInfo, err error) error {
			if err == nil && info.IsDir() {
				sources = append(sources, file.NewSource(path))
			}
			return nil
		})
		if err != nil {
			fmt.Printf("load config error: %+v", err)
			panic(err)
		}
		c := config.New(
			config.WithSource(sources...),
		)
		defer c.Close()

		if err = c.Load(); err != nil {
			panic(err)
		}

		var bc conf.Bootstrap
		if err = c.Scan(&bc); err != nil {
			panic(err)
		}
		initData.bc = &bc
	})
}
