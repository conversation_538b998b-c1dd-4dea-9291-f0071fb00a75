package helper

import (
	"encoding/json"

	"google.golang.org/protobuf/types/known/structpb"
)

func JsonStringToPBStruct(jsonStr string) (*structpb.Struct, error) {
	// 解析 JSON 字符串到中间数据结构
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &jsonData); err != nil {
		return nil, err
	}

	// 创建一个新的 google.protobuf.Struct 对象
	return structpb.NewStruct(jsonData)
}

func MapToPBStruct(m map[string]interface{}) (*structpb.Struct, error) {
	return structpb.NewStruct(m)
}

func JsonStringToPBValue(jsonStr string) (*structpb.Value, error) {
	// 解析 JSON 字符串到中间数据结构
	var jsonData interface{}
	if err := json.Unmarshal([]byte(jsonStr), &jsonData); err != nil {
		return nil, err
	}
	return structpb.NewValue(jsonData)
}

// MustPBValue 解析 JSON 字符串到中间数据结构
func MustPBValue(jsonStr *structpb.Value, err error) *structpb.Value {
	return jsonStr
}

func Int32PointerToPBValue(a *int32) (*structpb.Value, error) {
	if a == nil {
		return structpb.NewValue(nil)
	}
	return structpb.NewValue(*a)
}

func GetNullValue() *structpb.Value {
	return structpb.NewNullValue()
}

func StringToPBValue(str string) *structpb.Value {
	return structpb.NewStringValue(str)
}

func ToPBValue(value interface{}) (*structpb.Value, error) {
	return structpb.NewValue(value)
}
