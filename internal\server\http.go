package server

import (
	"context"
	http2 "net/http"

	"ccb/api/myerr"
	"ccb/internal/conf"
	log2 "ccb/internal/pkg/log"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	c *conf.Bootstrap,
	hLogger *log.Helper,
	accessLogger *log2.AccessLogger,
) *http.Server {
	// 构建 server
	srv := buildHTTPServer(c, accessLogger)
	return srv
}

// buildHTTPServer 构建 HTTP Server
func buildHTTPServer(c *conf.Bootstrap, accessLogger *log2.AccessLogger) *http.Server {
	var opts []http.ServerOption

	if c.Server.Http.Network != "" {
		opts = append(opts, http.Network(c.Server.Http.Network))
	}
	if c.Server.Http.Addr != "" {
		opts = append(opts, http.Address(c.Server.Http.Addr))
	}
	if c.Server.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Server.Http.Timeout.AsDuration()))
	}

	// 允许跨域
	filters := []http.FilterFunc{responseCROSHandler, handlers.CORS(
		// 域名配置
		handlers.AllowedOrigins([]string{"*"}),
		handlers.AllowedHeaders([]string{"Authorization", "User-Id", "Content-Type", "Accept", "Referer", "User-Agent", "X-Requested-With"}),
		handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE"}),
	)}
	opts = append(opts, http.Filter(filters...))

	middlewares := make([]middleware.Middleware, 0)
	// 打印访问日志
	if accessLogger != nil {
		middlewares = append(middlewares, logging.Server(*accessLogger))
	}

	// 拦截恢复异常
	middlewares = append(middlewares, recovery.Recovery(recovery.WithHandler(func(ctx context.Context, req, err interface{}) error {
		// 做一些panic处理
		newErr, isOk := err.(*errors.Error)
		if isOk {
			// 如果是自己定义的类型，则直接抛出去，目的是支持 panic(myErr) 类型的写法，提升开发速度
			return newErr
		}
		return myerr.ErrorSystemPanic("系统错误，请重试")
	})))

	middlewares = append(
		middlewares,
		validate.Validator(),
	)

	opts = append(opts, http.Middleware(middlewares...))

	srv := http.NewServer(opts...)

	// 注册 openap
	if c.Server.Http.GetIsOpenSwagger() {
		srv.HandlePrefix("/doc/", http2.StripPrefix("/doc/", http2.FileServer(http2.Dir("./third_party/swagger_ui"))))
	}

	return srv
}

// responseCROSHandler 跨域handlers.CORS有问题
// 在前端发起 Options 请求时，没有响应Access-Control-Allow-Origin导致跨域失败
func responseCROSHandler(h http2.Handler) http2.Handler {
	return http2.HandlerFunc(func(w http2.ResponseWriter, r *http2.Request) {
		w.Header().Add("Access-Control-Allow-Origin", "*")
		h.ServeHTTP(w, r)
	})
}
