package repository

import (
	"context"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
)

type ProductRepo interface {
	// GetE 通过 id 获取一条数据
	GetE(ctx context.Context, id int) (*do.ProductDo, error)

	// FindE 通过多个 id 获取多条数据
	FindE(ctx context.Context, ids ...int) ([]*do.ProductDo, error)

	// CreateE 创建数据
	CreateE(ctx context.Context, creatData *do.ProductDo) (*do.ProductDo, error)

	// CreateBulkE 批量创建数据
	CreateBulkE(ctx context.Context, dos []*do.ProductDo) ([]*do.ProductDo, error)

	// UpdateE 更新数据
	UpdateE(ctx context.Context, updateData *do.ProductDo) (int, error)

	// SearchListE 搜索列表
	SearchListE(ctx context.Context, reqBo *bo.ReqPageBo) (dos []*do.ProductDo, respPage *bo.RespPageBo, err error)
}
