package helper

import (
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	DefaultFormatLayout      = "2006-01-02 15:04:05" // 默认时间格式
	DefaultParseFormatLayout = "2006-1-02 15:04:05"  // 默认解析时间格式
)

func TimeFormat(t *time.Time, layout ...string) string {
	if t == nil {
		return ""
	}
	if len(layout) == 0 {
		layout = []string{DefaultFormatLayout}
	}
	return t.Format(layout[0])
}

// TimeStampFormat 时间戳转时间格式
func TimeStampFormat(timestamp int64, layout ...string) string {
	if timestamp == 0 {
		return ""
	}
	if len(layout) == 0 {
		layout = []string{DefaultFormatLayout}
	}
	return time.Unix(timestamp, 0).Format(layout[0])
}

// Int32TimeStampFormat int32类型时间戳转时间格式
func Int32TimeStampFormat(timestamp int32, layout ...string) string {
	return TimeStampFormat(int64(timestamp), layout...)
}

// IsoDateStringToTimeStamp iso格式字符串转时间戳
func IsoDateStringToTimeStamp(isoTimeStr string) (int64, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, err
	}
	isoTime, err := time.ParseInLocation(time.RFC3339Nano, isoTimeStr, loc)
	if err != nil {
		return 0, err
	}
	return isoTime.Unix(), nil
}

// LayOutDateStringToTimeStamp layout格式字符串转时间戳
func LayOutDateStringToTimeStamp(localTimeStr string) (int64, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, err
	}
	localTime, err := time.ParseInLocation(DefaultParseFormatLayout, localTimeStr, loc)
	if err != nil {
		return 0, err
	}
	return localTime.Unix(), nil
}

// LayOutDateStringToTimeStampX 字符串转时间戳(兼容格式)
func LayOutDateStringToTimeStampX(date string) int64 {
	u, err := LayOutDateStringToTimeStamp(date)
	if err != nil {
		panic(errors.Errorf("时间格式错误 %s", date))
	}
	return u
}

// DateStringToTimeStamp 字符串转时间戳(兼容格式)
func DateStringToTimeStamp(date string) (int64, error) {
	u, err := IsoDateStringToTimeStamp(date)
	if err != nil {
		u, err = LayOutDateStringToTimeStamp(date)
	}
	return u, err
}

// DateStringToTimeStampInt32 字符串转时间戳(兼容格式)
func DateStringToTimeStampInt32(date string) (int32, error) {
	u, err := DateStringToTimeStamp(date)
	return int32(u), err
}

// GoogleTimeToString google proto时间转字符串
func GoogleTimeToString(date *timestamppb.Timestamp) string {
	loc, _ := time.LoadLocation(GetTimeZone())
	return date.AsTime().In(loc).Format(DefaultFormatLayout)
}

func DateStringToTimeStampV2(localTimeStr string) (int64, error) {
	if localTimeStr == "" {
		return 0, nil
	}

	layouts := []string{
		DefaultParseFormatLayout,
		DefaultFormatLayout,
		"2006-01-02 15:04",
		"2006-1-02 15:04",
		"2006-01-02",
		"2006-1-02",
		time.RFC3339Nano,
	}

	var (
		localTime time.Time
		err       error
	)

	loc, _ := time.LoadLocation(GetTimeZone())
	for _, layout := range layouts {
		localTime, err = time.ParseInLocation(layout, localTimeStr, loc)
		if err == nil {
			break
		}
	}

	if err != nil {
		return 0, errors.Errorf("解析时间错误: %s", err.Error())
	}

	return localTime.Unix(), nil
}

func DateStringToTimeV2(localTimeStr string) (time.Time, error) {
	if localTimeStr == "" {
		return time.Time{}, nil
	}

	layouts := []string{
		DefaultParseFormatLayout,
		DefaultFormatLayout,
		"2006-01-02 15:04",
		"2006-1-02 15:04",
		"2006-01-02",
		"2006-1-02",
		time.RFC3339Nano,
	}

	var (
		localTime time.Time
		err       error
	)

	loc, _ := time.LoadLocation(GetTimeZone())
	for _, layout := range layouts {
		localTime, err = time.ParseInLocation(layout, localTimeStr, loc)
		if err == nil {
			break
		}
	}

	if err != nil {
		return time.Time{}, errors.Errorf("解析时间错误: %s", err.Error())
	}

	return localTime, nil
}

func ZeroToday() time.Time {
	now := time.Now() // 获取当前时间
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
}
