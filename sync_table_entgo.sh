#!/bash/bin
# 同步生成 entgo 模型 代码
# 运行请在项目根目录

# 配置数据库连接信息
dsn="mysql://root:root@tcp(192.168.6.74:3307)/ccb_platform"
SHELL_FOLDER=$(cd "$(dirname "$0")";pwd)
echo $SHELL_FOLDER
table=$1

entPath="${SHELL_FOLDER}/internal/data/ent"


if ! entimport -h >/dev/null 2>&1; then
  # entimport 新版本与 ent 版本不对应，所以通过此方式来安装
  echo "当前首次运行，正在安装 entimport 命令..."
  rm -rf _temp_install_entimport
  mkdir -p _temp_install_entimport
  cd _temp_install_entimport
  go mod init temp && go get entgo.io/ent@v0.12.2 && go get ariga.io/entimport/cmd/entimport && go install ariga.io/entimport/cmd/entimport
  cd ./..
  rm -r ./_temp_install_entimport
fi

if [ "${1}" != "" ]; then
  if [ -f "${entPath}/schema/${1}.go" ]; then
    echo "文件已存在，如果要重新生成，请先删除文件：rm -f internal/data/ent/schema/${1}.go"
    exit
  fi

  # 注意配置数据库连接信息、root:Lsxd123.@tcp(192.168.6.71:3308)/cmbi
  entimport -dsn ${dsn} -schema-path="${entPath}/schema" -tables="${table}"

  if [ ! -f "${entPath}/schema/${1}.go" ]; then
    #文件不存在，此工具会去掉后面的s，去掉 s 后再替换
    table=${table%?}
  fi
  # windows处理
  system=$(uname -s)
  if [ "${system}" == "Darwin" ]; then
    #entimport 生成出来不知道为啥有 gorm.io 的东西
    sed -i '' 's/gorm.io\/gen\/field/entgo.io\/ent\/schema\/field/' ${entPath}/schema/${table}.go

    sed -i '' 's/field\.Time("\([a-zA-Z_]*\)")\.Comment/field.Time("\1").Nillable().Comment/g' ${entPath}/schema/${table}.go
    sed -i '' 's/field.Bool/field.Int/g' ${entPath}/schema/${table}.go
    #将所有的 整型 统一为 Int
    sed -i '' 's/field.Uint/field.Int/g;s/field.Uint8/field.Int/g;s/field.Uint16/field.Int/g;s/field.Uint32/field.Int/g;s/field.Uint64/field.Int/g;s/field.Int8/field.Int/g;s/field.Int16/field.Int/g;s/field.Int32/field.Int/g;s/field.Int64/field.Int/g' ${entPath}/schema/${table}.go
  else
    #entimport 生成出来不知道为啥有 gorm.io 的东西
    sed -i 's/gorm.io\/gen\/field/entgo.io\/ent\/schema\/field/' ${entPath}/schema/${table}.go
    sed -i 's/field\.Time("\([a-zA-Z_]*\)")\.Comment/field.Time("\1").Nillable().Comment/g' ${entPath}/schema/${table}.go
    sed -i 's/field.Bool/field.Int/g' ${entPath}/schema/${table}.go
    #将所有的 整型 统一为 Int
    sed -i 's/field.Uint/field.Int/g;s/field.Uint8/field.Int/g;s/field.Uint16/field.Int/g;s/field.Uint32/field.Int/g;s/field.Uint64/field.Int/g;s/field.Int8/field.Int/g;s/field.Int16/field.Int/g;s/field.Int32/field.Int/g;s/field.Int64/field.Int/g' ${entPath}/schema/${table}.go
  fi
  echo "${1} schema 生成完成!!!"
fi

echo "正根据schema 文件生成模型代码..."
go generate ${entPath}/template
echo "代码模型生成完成!"