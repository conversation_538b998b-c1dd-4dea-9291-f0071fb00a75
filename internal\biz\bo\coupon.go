package bo

import "time"

type CouponListBo struct {
	BeginDate *time.Time
	EndDate   *time.Time
	Status    []int
	Code      string
	ReqPageBo *ReqPageBo
	Ids       []int
	ChannelId string
}

type ReconciliationInfoData struct {
	Status   int                      `json:"status"`
	DataList []ReconciliationInfoItem `json:"dataList"`
}

type ReconciliationInfoItem struct {
	OrderId  string  `json:"orderId"`
	OrderNum float64 `json:"orderNum"`
}

type ReconciliationInfo struct {
	Code string                 `json:"code"`
	Data ReconciliationInfoData `json:"data"`
	Msg  string                 `json:"msg"`
}

type OrderListBo struct {
	StartTime  *time.Time
	EndTime    *time.Time
	MerchantId string
	ProductIds []int
}

type ProxyData struct {
	SerialNo    string `json:"serialNo"`
	TranCode    string `json:"tranCode"`
	RequestTime string `json:"requestTime"`
	SysId       string `json:"sysId"`
	BizData     struct {
		RedirectUrl string `json:"redirectUrl"`
		UserId      string `json:"userId"`
		Channel     string `json:"channel"`
	} `json:"bizData"`
}
