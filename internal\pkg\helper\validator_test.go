package helper

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/duke-git/lancet/v2/strutil"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/exp/rand"
)

func TestIsMobileForm(t *testing.T) {
	type args struct {
		mobile string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "手机号格式验证-少于11位", args: args{mobile: "1814015401"}, want: false},
		{name: "手机号格式验证-大于11位", args: args{mobile: "181401540111"}, want: false},
		{name: "手机号格式验证-2开头", args: args{mobile: "28140154011"}, want: false},
	}

	for i := 0; i < 10; i++ {
		rand.Seed(uint64(time.Now().UnixNano()))
		randNum := strconv.Itoa(rand.Intn(1000000000))
		randNumStr := strutil.PadStart(randNum, 9, "0")
		mobile := fmt.Sprintf("1%d%s", i, randNumStr)
		name := fmt.Sprintf("手机号格式验证-1%d开头后随机9位", i)
		tests = append(tests, struct {
			name string
			args args
			want bool
		}{name: name, args: args{mobile: mobile}, want: true})
	}
	convey.Convey("手机号格式验证", t, func() {
		for _, test := range tests {
			convey.Convey(test.name, func() {
				convey.So(IsMobile(test.args.mobile), convey.ShouldEqual, test.want)
			})
		}
	})
}

func TestIsEmail(t *testing.T) {
	type args struct {
		email string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "邮箱格式验证-不带@符号", args: args{email: "1814015401"}, want: false},
		{name: "邮箱格式验证-不带域名", args: args{email: "1814015401@"}, want: false},
		{name: "邮箱格式验证-不带域名后缀", args: args{email: "1814015401@qq"}, want: false},
		{name: "邮箱格式验证-带域名后缀", args: args{email: "<EMAIL>"}, want: true},
	}

	convey.Convey("手机号格式验证", t, func() {
		for _, test := range tests {
			convey.Convey(test.name, func() {
				fmt.Println(IsEmail(test.args.email), test.want)
				convey.So(IsEmail(test.args.email), convey.ShouldEqual, test.want)
			})
		}
	})
}
