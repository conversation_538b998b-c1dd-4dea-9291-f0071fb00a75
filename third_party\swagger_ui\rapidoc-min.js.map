{"version": 3, "file": "rapidoc-min.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDA;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;ACKA;;ACKA;ACoRA;AACA;AACA;AAIA;AAIA;AAIA;AAEA;;;AAIA;;AAEA;AACA;AACA;AACA;;;;;AAKA;AAGA;;AAGA;AACA;AACA;;AAIA;;AAGA;AACA;;;;AAIA;AAGA;AACA;;;AAIA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AAGA;;AAGA;AACA;;;AAKA;;AAKA;AAuBA;;;;AAIA;AAEA;;AAEA;;AAIA;;AAGA;AACA;;;AAGA;AACA;AAEA;AACA;AACA;;AAIA;;AAGA;AACA;;AAIA;;AAGA;;;AAMA;AAEA;;;AAGA;AACA;AACA;AAGA;AAGA;;;;;AAMA;AACA;;AAEA;;;AAGA;;AAEA;;;AAKA;;;AAIA;;;AAaA;;AAIA;;;AAtGA;;;AAqJA;AACA;AAEA;;;AAGA;;;AAMA;;AAEA;;;;;AAOA;;AAEA;AAKA;;AAGA;AAIA;AACA;AACA;;AAIA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;;;AAMA;;;AAvFA;;;AClfA;;AAYA;;AAEA;AACA;AACA;AACA;AACA;;AAIA;;AC1BA;;AAEA;AACA;;;AAGA;;;AAGA;;AAEA;;AAEA;;;;AAIA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAOA;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;ACtEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoFA;AAQA;;;;AAIA;;AAGA;AAYA;;AAEA;;AAEA;AACA;;;AAIA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2PA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7RA;AAQA;;AAEA;AACA;;;AAIA;AACA;;AAIA;AACA;AAEA;;AASA;AA0BA;AACA;AASA;AAyDA;AACA;AACA;AAMA;AACA;AACA;AAGA;;AAEA;;AAEA;AACA;AAGA;AACA;AAwBA;;;AAIA;AASA;AA4BA;AACA;AACA;AACA;AAMA;AACA;AACA;;;;AAIA;AAEA;AAKA;AAIA;AACA;AACA;AACA;;;AAIA;;AChUA;AACA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiEA;AAOA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AA0DA;;;;AAIA;AACA;AACA;AACA;AAMA;AACA;AAEA;AAIA;AACA;AAKA;;AAEA;AACA;AACA;;AAEA;AAEA;AJvHA;AI2MA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;;AAMA;AAEA;AACA;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAKA;;AAIA;AAOA;AACA;;AAEA;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAKA;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;;;;;;AAOA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAMA;AAGA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;;AAGA;;;AAKA;;AAEA;AACA;;;AAIA;AAGA;;;AAGA;;AAGA;AA0EA;AACA;AACA;AACA;;;AAOA;AAqBA;;AAEA;AAGA;AACA;AACA;;;;AAKA;AAGA;AACA;AACA;;;AAGA;;AAEA;;AAEA;;;;;;;AAOA;AACA;AACA;;AAEA;AACA;;;;;;AAWA;;AAoBA;;AAOA;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAKA;AAIA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;;AAIA;;;AAmBA;AAiBA;AACA;;;AAGA;AACA;;;;;;AAMA;AACA;AACA;AACA;;;;AAKA;AACA;;AAEA;AACA;AACA;AACA;;;;AAMA;AAgBA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AAGA;;;;;;AAMA;AACA;;;;;;;;AAQA;;;AAIA;AACA;AACA;;;AAGA;;;AAGA;AAEA;AAGA;AAEA;;AAEA;;;AAGA;AACA;AACA;;AAEA;;;AAOA;;AAIA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAWA;;AAIA;;;AAMA;;;;AAMA;AACA;;;AAKA;;AAGA;;AAGA;;AAIA;;;;AAIA;AACA;;;AAGA;AAEA;AAIA;AACA;AACA;;AAGA;;AAuBA;;AAEA;;;AAGA;AAIA;AACA;AACA;;AAIA;AAEA;AACA;;;AAGA;AAIA;AAEA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AAEA;AAOA;AACA;;AAIA;;AAEA;AACA;;;AAIA;;AAEA;;AAMA;;;;AAIA;;;;AAIA;AAEA;AAEA;AAIA;;;AAOA;AAGA;;;AAGA;;AAEA;AAGA;;;AAGA;;AAEA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACniCA;AAQA;;AAEA;AACA;;;AAIA;;AAEA;;AAIA;;;;;;;AAOA;AAEA;;;AAUA;AAyBA;AACA;AACA;AAUA;AAsCA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAGA;AAMA;;AAEA;AACA;AACA;;AAEA;AACA;AAEA;;;;AAKA;;;AAGA;;AAKA;AAGA;AACA;AAwBA;;;AASA;AAYA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAMA;;AAEA;AACA;;AAOA;AACA;AACA;AACA;;;AAIA;AC/QA;AACA;AACA;;AAGA;AACA;;AAGA;;;;;AAKA;AACA;;AAIA;AAGA;AACA;AACA;;AAGA;;AAKA;AAGA;AACA;;AAIA;AACA;;;AAIA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAGA;AAGA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAOA;AAKA;AACA;AACA;AACA;;AAEA;;;;AASA;;;AAhBA;;ACjKA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAEA;;AAEA;;AAGA;AACA;AACA;AACA;;AAEA;;;AAKA;;AAQA;AACA;AACA;AACA;;AAGA;AACA;;;AAKA;AAKA;AACA;AACA;;AAEA;;;;AAIA;;;AAVA;;AC5CA;AACA;;AAGA;AACA;;AAEA;AAFA;;;AAOA;AAEA;AACA;AAGA;AAIA;AAEA;AAMA;AAIA;;AAGA;AACA;AAIA;;;;AAMA;;AASA;;AAKA;;;ACtCA;;AAEA;;AAEA;;AAGA;AACA;;AAEA;;;AAIA;AACA;AACA;;AAGA;AACA;;AAGA;;;;;AAMA;AACA;AACA;;;;AAIA;;;AAMA;AAOA;;;AAGA;AAGA;;;AAGA;AACA;AACA;AACA;;;AAGA;AACA;;;;AAbA;AAkBA;;;AAGA;AAEA;;;;AClDA;AACA;;AAEA;AAGA;AACA;;;;;;;AASA;;;;;AAKA;AAEA;;AAEA;;AAKA;AAGA;;;;;;AAQA;AACA;AAGA;AAEA;AAEA;AACA;;AAIA;;AAEA;AACA;;AAEA;;AAEA;;;AAIA;AACA;AAEA;;AA1BA;;AAgCA;AAIA;;AAKA;;AAEA;;;;;;;;;;AAWA;;;;;;AAQA;AAGA;AACA;;AAIA;;AAEA;AACA;AACA;AACA;;;AAGA;;;;AAKA;AAEA;;AAIA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AAIA;;AAEA;;AAOA;;;AAGA;AACA;;;AAGA;AACA;AACA;;AAGA;AACA;;;;;AAQA;;;AAIA;;;;;AAMA;;;;AAKA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;;AAUA;;ACzPA;AAEA;AAiBA;AACA;;AAGA;AAMA;AAIA;;;AAsBA;AC5BA;AACA;AACA;AACA;AACA;;AAEA;;;AAIA;AAGA;;AAGA;;AAKA;AAqBA;;AAEA;AAMA;;AAGA;;AAKA;;AAIA;AACA;AAEA;AACA;AAEA;AACA;AAGA;AAGA;AACA;AACA;;;AAGA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAOA;AAKA;;AAGA;;;;AAIA;;;;AAIA;AAGA;AACA;AAEA;AACA;AACA;;;AAGA;;AAEA;;AAEA;AAMA;AACA;AACA;;;AAIA;;AAGA;AAMA;AACA;AACA;;;;AAnDA;;;;ACtLA;AAEA;;;;;;;;;;;;;ACOA;;;AAGA;;;;;;;AASA;AACA;;;;;AAMA;;;;;AAOA;AACA;;;AAGA;;;AAIA;AAGA;;;;AAKA;AAGA;;;;;ADtDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE8EA;AAOA;AACA;AACA;;;AAGA;;;AAIA;AAgDA;AAEA;AACA;;AAIA;AAQA;AACA;;;AAGA;AACA;;;AAOA;AACA;;AAEA;AACA;;AAKA;;AAIA;AACA;AACA;;AAEA;;AAKA;AAEA;AACA;AAEA;;;AAIA;AAIA;;;AAMA;;;AAGA;;;AAGA;;;AAGA;;;AAGA;;;AAGA;AAEA;AAIA;AACA;AAEA;AAkBA;AAEA;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAGA;AACA;;AAEA;;;;;AAxCA;AACA;AAgDA;AASA;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAvBA;AACA;AA0BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtUA;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;AAGA;AACA;;AAEA;;AASA;AACA;AACA;;;;AAMA;ACvBA;;;;AAKA;AACA;;;;;;AAMA;;;;;;;AC8IA;AACA;AACA;;AAEA;;AAMA;AACA;AACA;AACA;;;AAGA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAGA;AC3PA;AACA;AAGA;AACA;;AAEA;;;;AAIA;AACA;;;AAKA;AAEA;;;AAGA;;;;AAOA;AACA;;;AAGA;;;AAGA;;AAEA;;AAEA;;;;;AASA;AACA;AAGA;AAGA;AACA;AAGA;AACA;AACA;;;;AAIA;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4RA;;;;;;;;;AASA;;;;;;;;;;;;;;;;;;;;;;;AC5RA;;;;;;;;;ACvGA;;;;;;AAMA;AACA;AACA;;AAIA;;;;AAMA;AAKA;;AAEA;AAIA;;AAEA;AACA;;;;;AAKA;AACA;AACA;AACA;;;;;;AAMA;AAEA;AACA;AACA;;AAGA;AAGA;;AAEA;AACA;;AAEA;;;;AAKA;;AAKA;AAyBA;AACA;AAGA;AACA;;AAEA;;;;;AAKA;AACA;;;AAKA;AAEA;;;AAGA;;;;AAOA;AACA;;;AAGA;;AAEA;;;AAGA;;;;;AAKA;AACA;AAGA;AAGA;AACA;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA", "sources": ["webpack://rapidoc/./src/styles/input-styles.js", "webpack://rapidoc/./src/styles/flex-styles.js", "webpack://rapidoc/./src/styles/table-styles.js", "webpack://rapidoc/./src/styles/endpoint-styles.js", "webpack://rapidoc/./src/styles/prism-styles.js", "webpack://rapidoc/./src/styles/tab-styles.js", "webpack://rapidoc/./src/styles/nav-styles.js", "webpack://rapidoc/./src/styles/info-styles.js", "webpack://rapidoc/./src/styles/custom-styles.js", "webpack://rapidoc/./src/utils/common-utils.js", "webpack://rapidoc/./src/templates/security-scheme-template.js", "webpack://rapidoc/./src/templates/code-samples-template.js", "webpack://rapidoc/./src/templates/callback-template.js", "webpack://rapidoc/./src/utils/schema-utils.js", "webpack://rapidoc/./src/components/json-tree.js", "webpack://rapidoc/./src/components/schema-tree.js", "webpack://rapidoc/./src/components/tag-input.js", "webpack://rapidoc/./src/components/api-request.js", "webpack://rapidoc/./src/components/schema-table.js", "webpack://rapidoc/./src/templates/expanded-endpoint-template.js", "webpack://rapidoc/./src/templates/components-template.js", "webpack://rapidoc/./src/templates/overview-template.js", "webpack://rapidoc/./src/templates/server-template.js", "webpack://rapidoc/./src/templates/navbar-template.js", "webpack://rapidoc/./src/templates/focused-endpoint-template.js", "webpack://rapidoc/./src/templates/endpoint-template.js", "webpack://rapidoc/./src/templates/logo-template.js", "webpack://rapidoc/./src/templates/header-template.js", "webpack://rapidoc/./src/components/api-response.js", "webpack://rapidoc/./src/templates/advance-search-template.js", "webpack://rapidoc/./src/components/dialog-box.js", "webpack://rapidoc/./src/utils/theme.js", "webpack://rapidoc/./src/templates/main-body-template.js", "webpack://rapidoc/./src/rapidoc.js", "webpack://rapidoc/./src/rapidoc-mini.js", "webpack://rapidoc/./src/templates/json-schema-viewer-template.js", "webpack://rapidoc/./src/json-schema-viewer.js"], "sourcesContent": ["import { css } from 'lit';\n\n/* eslint-disable max-len */\nexport default css`\n/* Button */\n.m-btn {\n  border-radius: var(--border-radius);\n  font-weight: 600;\n  display: inline-block;\n  padding: 6px 16px;\n  font-size: var(--font-size-small);\n  outline: 0;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  border: 2px solid var(--primary-color);\n  background-color:transparent;\n  transition: background-color 0.2s;\n  user-select: none;\n  cursor: pointer;\n  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);\n}\n.m-btn.primary {\n  background-color: var(--primary-color);\n  color: var(--primary-color-invert);\n}\n.m-btn.thin-border { border-width: 1px; }\n.m-btn.large { padding:8px 14px; }\n.m-btn.small { padding:5px 12px; }\n.m-btn.tiny { padding:5px 6px; }\n.m-btn.circle { border-radius: 50%; }\n.m-btn:hover { \n  background-color: var(--primary-color);\n  color: var(--primary-color-invert);\n}\n.m-btn.nav { border: 2px solid var(--nav-accent-color); }\n.m-btn.nav:hover { \n  background-color: var(--nav-accent-color);\n}\n.m-btn:disabled{ \n  background-color: var(--bg3);\n  color: var(--fg3);\n  border-color: var(--fg3);\n  cursor: not-allowed;\n  opacity: 0.4;\n}\n.toolbar-btn{\n  cursor: pointer;\n  padding: 4px;\n  margin:0 2px;\n  font-size: var(--font-size-small);\n  min-width: 50px;\n  color: var(--primary-color-invert);\n  border-radius: 2px;\n  border: none;\n  background-color: var(--primary-color);\n}\n\ninput, textarea, select, button, pre {\n  color:var(--fg);\n  outline: none;\n  background-color: var(--input-bg);\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius);\n}\nbutton {\n  font-family: var(--font-regular);\n}\n\n/* Form Inputs */\npre,\nselect,\ntextarea,\ninput[type=\"file\"],\ninput[type=\"text\"],\ninput[type=\"password\"] {\n  font-family: var(--font-mono);\n  font-weight: 400;\n  font-size: var(--font-size-small);\n  transition: border .2s;\n  padding: 6px 5px;\n}\n\nselect {\n  font-family: var(--font-regular);\n  padding: 5px 30px 5px 5px;\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%2212%22%3E%3Cpath%20d%3D%22M10.3%203.3L6%207.6%201.7%203.3A1%201%200%2000.3%204.7l5%205a1%201%200%20001.4%200l5-5a1%201%200%2010-1.4-1.4z%22%20fill%3D%22%23777777%22%2F%3E%3C%2Fsvg%3E\"); \n  background-position: calc(100% - 5px) center;\n  background-repeat: no-repeat;  \n  background-size: 10px;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  cursor: pointer;\n}\n\nselect:hover {\n  border-color: var(--primary-color);\n}\n\ntextarea::placeholder,\ninput[type=\"text\"]::placeholder,\ninput[type=\"password\"]::placeholder {\n  color: var(--placeholder-color);\n  opacity:1;\n}\n\n\ninput[type=\"file\"]{\n  font-family: var(--font-regular);\n  padding:2px;\n  cursor:pointer;\n  border: 1px solid var(--primary-color);\n  min-height: calc(var(--font-size-small) + 18px);\n}\n\ninput[type=\"file\"]::-webkit-file-upload-button {\n  font-family: var(--font-regular);\n  font-size: var(--font-size-small);\n  outline: none;\n  cursor:pointer;\n  padding: 3px 8px;\n  border: 1px solid var(--primary-color);\n  background-color: var(--primary-color);\n  color: var(--primary-color-invert);\n  border-radius: var(--border-radius);;\n  -webkit-appearance: none;\n}\n\npre,\ntextarea {\n  scrollbar-width: thin;\n  scrollbar-color: var(--border-color) var(--input-bg);\n}\n\npre::-webkit-scrollbar,\ntextarea::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\npre::-webkit-scrollbar-track,\ntextarea::-webkit-scrollbar-track {\n  background:var(--input-bg);\n}\n \npre::-webkit-scrollbar-thumb,\ntextarea::-webkit-scrollbar-thumb {\n  border-radius: 2px;\n  background-color: var(--border-color);\n}\n\n.link {\n  font-size:var(--font-size-small);\n  text-decoration: underline;\n  color:var(--blue);\n  font-family:var(--font-mono);\n  margin-bottom:2px;\n}\n\n/* Toggle Body */\ninput[type=\"checkbox\"] {\n  appearance: none;\n  display: inline-block;\n  background-color: var(--light-bg);\n  border: 1px solid var(--light-bg);\n  border-radius: 9px;\n  cursor: pointer;\n  height: 18px;\n  position: relative;\n  transition: border .25s .15s, box-shadow .25s .3s, padding .25s;\n  min-width: 36px;\n  width: 36px;\n  vertical-align: top;\n}\n/* Toggle Thumb */\ninput[type=\"checkbox\"]:after {\n  position: absolute;\n  background-color: var(--bg);\n  border: 1px solid var(--light-bg);\n  border-radius: 8px;\n  content: '';\n  top: 0px;\n  left: 0px;\n  right: 16px;\n  display: block;\n  height: 16px;\n  transition: border .25s .15s, left .25s .1s, right .15s .175s;\n}\n\n/* Toggle Body - Checked */\ninput[type=\"checkbox\"]:checked {\n  background-color: var(--green);\n  border-color: var(--green);\n}\n/* Toggle Thumb - Checked*/\ninput[type=\"checkbox\"]:checked:after {\n  border: 1px solid var(--green);\n  left: 16px;\n  right: 1px;\n  transition: border .25s, left .15s .25s, right .25s .175s;\n}`;\n", "import { css } from 'lit';\n\nexport default css`\n.row, .col{\n  display:flex;\n} \n.row {\n  align-items:center;\n  flex-direction: row;\n}\n.col {\n  align-items:stretch;\n  flex-direction: column;\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\n.m-table {\n  border-spacing: 0;  \n  border-collapse: separate;\n  border: 1px solid var(--light-border-color);\n  border-radius: var(--border-radius);\n  margin: 0;\n  max-width: 100%;\n  direction: ltr;\n}\n.m-table tr:first-child td,\n.m-table tr:first-child th {\n    border-top: 0 none;\n}\n.m-table td, \n.m-table th {\n  font-size: var(--font-size-small);\n  line-height: calc(var(--font-size-small) + 4px);\n  padding: 4px 5px 4px;\n  vertical-align: top;\n}\n\n.m-table.padded-12 td, \n.m-table.padded-12 th {\n  padding: 12px;\n}\n\n.m-table td:not([align]), \n.m-table th:not([align]) {\n  text-align: left;\n}\n\n.m-table th {\n  color: var(--fg2);\n  font-size: var(--font-size-small);\n  line-height: calc(var(--font-size-small) + 18px);\n  font-weight: 600;\n  letter-spacing: normal;\n  background-color: var(--bg2);\n  vertical-align: bottom;\n  border-bottom: 1px solid var(--light-border-color);\n}\n\n.m-table > tbody > tr > td,\n.m-table > tr > td {\n  border-top: 1px solid var(--light-border-color);\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.table-title {\n  font-size:var(--font-size-small);\n  font-weight:bold;\n  vertical-align: middle;\n  margin: 12px 0 4px 0;\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\n.only-large-screen { display:none; }\n.endpoint-head .path{\n  display: flex;\n  font-family:var(--font-mono);\n  font-size: var(--font-size-small);\n  align-items: center;\n  overflow-wrap: break-word;\n  word-break: break-all;\n}\n\n.endpoint-head .descr {\n  font-size: var(--font-size-small);\n  color:var(--light-fg);\n  font-weight:400;\n  align-items: center;\n  overflow-wrap: break-word;\n  word-break: break-all;\n  display:none;\n}\n\n.m-endpoint.expanded{margin-bottom:16px; }\n.m-endpoint > .endpoint-head{\n  border-width:1px 1px 1px 5px;\n  border-style:solid;\n  border-color:transparent;\n  border-top-color:var(--light-border-color);\n  display:flex;\n  padding:6px 16px;\n  align-items: center;\n  cursor: pointer;\n}\n.m-endpoint > .endpoint-head.put:hover,\n.m-endpoint > .endpoint-head.put.expanded{\n  border-color:var(--orange); \n  background-color:var(--light-orange); \n}\n.m-endpoint > .endpoint-head.post:hover,\n.m-endpoint > .endpoint-head.post.expanded {\n  border-color:var(--green); \n  background-color:var(--light-green); \n}\n.m-endpoint > .endpoint-head.get:hover,\n.m-endpoint > .endpoint-head.get.expanded {\n  border-color:var(--blue); \n  background-color:var(--light-blue); \n}\n.m-endpoint > .endpoint-head.delete:hover,\n.m-endpoint > .endpoint-head.delete.expanded {\n  border-color:var(--red); \n  background-color:var(--light-red); \n}\n\n.m-endpoint > .endpoint-head.head:hover,\n.m-endpoint > .endpoint-head.head.expanded,\n.m-endpoint > .endpoint-head.patch:hover,\n.m-endpoint > .endpoint-head.patch.expanded,\n.m-endpoint > .endpoint-head.options:hover,\n.m-endpoint > .endpoint-head.options.expanded {\n  border-color:var(--yellow); \n  background-color:var(--light-yellow); \n}\n\n.m-endpoint > .endpoint-head.deprecated:hover,\n.m-endpoint > .endpoint-head.deprecated.expanded {\n  border-color:var(--border-color); \n  filter:opacity(0.6);\n}\n\n.m-endpoint .endpoint-body {\n  flex-wrap:wrap;\n  padding:16px 0px 0 0px;\n  border-width:0px 1px 1px 5px;\n  border-style:solid;\n  box-shadow: 0px 4px 3px -3px rgba(0, 0, 0, 0.15);\n}\n.m-endpoint .endpoint-body.delete{ border-color:var(--red); }\n.m-endpoint .endpoint-body.put{ border-color:var(--orange); }\n.m-endpoint .endpoint-body.post{border-color:var(--green);}\n.m-endpoint .endpoint-body.get{ border-color:var(--blue); }\n.m-endpoint .endpoint-body.head,\n.m-endpoint .endpoint-body.patch,\n.m-endpoint .endpoint-body.options { \n  border-color:var(--yellow); \n}\n\n.m-endpoint .endpoint-body.deprecated{ \n  border-color:var(--border-color);\n  filter:opacity(0.6);\n}\n\n.endpoint-head .deprecated{\n  color: var(--light-fg);\n  filter:opacity(0.6);\n}\n\n.summary{\n  padding:8px 8px;\n}\n.summary .title{\n  font-size:calc(var(--font-size-regular) + 2px);\n  margin-bottom: 6px;\n  word-break: break-all;\n}\n\n.endpoint-head .method{\n  padding:2px 5px;\n  vertical-align: middle;\n  font-size:var(--font-size-small);\n  height: calc(var(--font-size-small) + 16px);\n  line-height: calc(var(--font-size-small) + 8px);\n  width: 60px;\n  border-radius: 2px;\n  display:inline-block;\n  text-align: center;\n  font-weight: bold;\n  text-transform:uppercase;\n  margin-right:5px;\n}\n.endpoint-head .method.delete{ border: 2px solid var(--red);}\n.endpoint-head .method.put{ border: 2px solid var(--orange); }\n.endpoint-head .method.post{ border: 2px solid var(--green); }\n.endpoint-head .method.get{ border: 2px solid var(--blue); }\n.endpoint-head .method.get.deprecated{ border: 2px solid var(--border-color); }\n.endpoint-head .method.head,\n.endpoint-head .method.patch,\n.endpoint-head .method.options { \n  border: 2px solid var(--yellow); \n}\n\n.req-resp-container {\n  display: flex;\n  margin-top:16px;\n  align-items: stretch;\n  flex-wrap: wrap;\n  flex-direction: column;\n  border-top:1px solid var(--light-border-color);\n}\n\n.view-mode-request,\napi-response.view-mode {\n  flex:1; \n  min-height:100px;\n  padding:16px 8px;\n  overflow:hidden;\n}\n.view-mode-request {\n  border-width:0 0 1px 0;\n  border-style:dashed;\n}\n\n.head .view-mode-request,\n.patch .view-mode-request,\n.options .view-mode-request { \n  border-color:var(--yellow); \n}\n.put .view-mode-request { \n  border-color:var(--orange); \n}\n.post .view-mode-request { \n  border-color:var(--green); \n}\n.get .view-mode-request { \n  border-color:var(--blue); \n}\n.delete .view-mode-request { \n  border-color:var(--red); \n}\n\n@media only screen and (min-width: 1024px) {\n  .only-large-screen { display:block; }\n  .endpoint-head .path{\n    font-size: var(--font-size-regular);\n  }\n  .endpoint-head .descr{\n    display: flex;\n  }\n  .endpoint-head .m-markdown-small,\n  .descr .m-markdown-small{\n    display:block;\n  }\n  .req-resp-container{\n    flex-direction: var(--layout, row);\n    flex-wrap: nowrap;\n  }\n  api-response.view-mode {\n    padding:16px;\n  }\n  .view-mode-request.row-layout {\n    border-width:0 1px 0 0;\n    padding:16px;\n  }\n  .summary{\n    padding:8px 16px;\n  }\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\ncode[class*=\"language-\"],\npre[class*=\"language-\"] {\n  text-align: left;\n  white-space: pre;\n  word-spacing: normal;\n  word-break: normal;\n  word-wrap: normal;\n  line-height: 1.5;\n  tab-size: 2;\n\n  -webkit-hyphens: none;\n  -moz-hyphens: none;\n  -ms-hyphens: none;\n  hyphens: none;\n}\n\n/* Code blocks */\npre[class*=\"language-\"] {\n  padding: 1em;\n  margin: .5em 0;\n  overflow: auto;\n}\n\n/* Inline code */\n:not(pre) > code[class*=\"language-\"] {\n  white-space: normal;\n}\n\n.token.comment,\n.token.block-comment,\n.token.prolog,\n.token.doctype,\n.token.cdata {\n  color: var(--light-fg)\n}\n\n.token.punctuation {\n  color: var(--fg);\n}\n\n.token.tag,\n.token.attr-name,\n.token.namespace,\n.token.deleted {\n  color:var(--pink);\n}\n\n.token.function-name {\n  color: var(--blue);\n}\n\n.token.boolean,\n.token.number,\n.token.function {\n  color: var(--red);\n}\n\n.token.property,\n.token.class-name,\n.token.constant,\n.token.symbol {\n  color: var(--code-property-color);\n}\n\n.token.selector,\n.token.important,\n.token.atrule,\n.token.keyword,\n.token.builtin {\n  color: var(--code-keyword-color);\n}\n\n.token.string,\n.token.char,\n.token.attr-value,\n.token.regex,\n.token.variable { \n  color: var(--green);\n}\n\n.token.operator,\n.token.entity,\n.token.url {\n  color: var(--code-operator-color);\n}\n\n.token.important,\n.token.bold {\n  font-weight: bold;\n}\n.token.italic {\n  font-style: italic;\n}\n\n.token.entity {\n  cursor: help;\n}\n\n.token.inserted {\n  color: green;\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\n.tab-panel {\n  border: none;\n}\n.tab-buttons {\n  height:30px;\n  padding: 4px 4px 0 4px;\n  border-bottom: 1px solid var(--light-border-color) ;\n  align-items: stretch;\n  overflow-y: hidden;\n  overflow-x: auto;\n  scrollbar-width: thin;\n}\n.tab-buttons::-webkit-scrollbar {\n  height: 1px;\n  background-color: var(--border-color);\n}\n.tab-btn {\n  border: none;\n  border-bottom: 3px solid transparent; \n  color: var(--light-fg);\n  background-color: transparent;\n  white-space: nowrap;\n  cursor:pointer;\n  outline:none;\n  font-family:var(--font-regular); \n  font-size:var(--font-size-small);\n  margin-right:16px;\n  padding:1px;\n}\n.tab-btn.active {\n  border-bottom: 3px solid var(--primary-color); \n  font-weight:bold;\n  color:var(--primary-color);\n}\n\n.tab-btn:hover {\n  color:var(--primary-color);\n}\n.tab-content {\n  margin:-1px 0 0 0;\n  position:relative;\n  min-height: 50px;\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\n.nav-bar-info:focus-visible,\n.nav-bar-tag:focus-visible,\n.nav-bar-path:focus-visible {\n  outline: 1px solid;\n  box-shadow: none;\n  outline-offset: -4px;\n}\n.nav-bar-expand-all:focus-visible,\n.nav-bar-collapse-all:focus-visible,\n.nav-bar-tag-icon:focus-visible {\n  outline: 1px solid;\n  box-shadow: none;\n  outline-offset: 2px;\n}\n.nav-bar {\n  width:0;\n  height:100%;\n  overflow: hidden;\n  color:var(--nav-text-color);\n  background-color: var(--nav-bg-color);\n  background-blend-mode: multiply;\n  line-height: calc(var(--font-size-small) + 4px);\n  display:none;\n  position:relative;\n  flex-direction:column;\n  flex-wrap:nowrap;\n  word-break:break-word;\n}\n::slotted([slot=nav-logo]){\n  padding:16px 16px 0 16px;\n}\n.nav-scroll {\n  overflow-x: hidden;\n  overflow-y: auto;\n  overflow-y: overlay;\n  scrollbar-width: thin;\n  scrollbar-color: var(--nav-hover-bg-color) transparent;\n}\n\n.nav-bar-tag {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-direction: row;\n}\n.nav-bar.read .nav-bar-tag-icon {\n  display:none;\n}\n.nav-bar-paths-under-tag {\n  overflow:hidden;\n  transition: max-height .2s ease-out, visibility .3s;\n}\n.collapsed .nav-bar-paths-under-tag {\n  visibility: hidden;\n}\n\n.nav-bar-expand-all {\n  transform: rotate(90deg); \n  cursor:pointer; \n  margin-right:10px;\n}\n.nav-bar-collapse-all {\n  transform: rotate(270deg); \n  cursor:pointer;\n}\n.nav-bar-expand-all:hover, .nav-bar-collapse-all:hover {\n  color: var(--primary-color);\n}\n\n.nav-bar-tag-icon {\n  color: var(--nav-text-color);\n  font-size: 20px; \n}\n.nav-bar-tag-icon:hover {\n  color:var(--nav-hover-text-color);\n}\n.nav-bar.focused .nav-bar-tag-and-paths.collapsed .nav-bar-tag-icon::after {\n  content: '⌵';\n  width:16px;\n  height:16px;\n  text-align: center;\n  display: inline-block;\n  transform: rotate(-90deg);\n  transition: transform 0.2s ease-out 0s;\n}\n.nav-bar.focused .nav-bar-tag-and-paths.expanded .nav-bar-tag-icon::after {\n  content: '⌵';\n  width:16px;\n  height:16px;\n  text-align: center;\n  display: inline-block;\n  transition: transform 0.2s ease-out 0s;\n}\n.nav-scroll::-webkit-scrollbar {\n  width: var(--scroll-bar-width, 8px);\n}\n.nav-scroll::-webkit-scrollbar-track {\n  background:transparent;\n}\n.nav-scroll::-webkit-scrollbar-thumb {\n  background-color: var(--nav-hover-bg-color);\n}\n\n.nav-bar-tag {\n  font-size: var(--font-size-regular);\n  color: var(--nav-accent-color);\n  border-left:4px solid transparent;\n  font-weight:bold;\n  padding: 15px 15px 15px 10px;\n  text-transform: capitalize;\n}\n\n.nav-bar-components,\n.nav-bar-h1,\n.nav-bar-h2,\n.nav-bar-info,\n.nav-bar-tag,\n.nav-bar-path {\n  display:flex;\n  cursor: pointer;\n  width: 100%;\n  border: none;\n  border-radius:4px; \n  color: var(--nav-text-color);\n  background: transparent;\n  border-left:4px solid transparent;\n}\n\n.nav-bar-h1,\n.nav-bar-h2,\n.nav-bar-path {\n  font-size: calc(var(--font-size-small) + 1px);\n  padding: var(--nav-item-padding);\n}\n.nav-bar-path.small-font {\n  font-size: var(--font-size-small);\n}\n\n.nav-bar-info {\n  font-size: var(--font-size-regular);\n  padding: 16px 10px;\n  font-weight:bold;\n}\n.nav-bar-section {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  font-size: var(--font-size-small);\n  color: var(--nav-text-color);\n  padding: var(--nav-item-padding);\n  font-weight:bold;\n}\n.nav-bar-section.operations {\n  cursor:pointer;\n}\n.nav-bar-section.operations:hover {\n  color:var(--nav-hover-text-color);\n  background-color:var(--nav-hover-bg-color);\n}\n\n.nav-bar-section:first-child {\n  display: none;\n}\n.nav-bar-h2 {margin-left:12px;}\n\n.nav-bar-h1.left-bar.active,\n.nav-bar-h2.left-bar.active,\n.nav-bar-info.left-bar.active,\n.nav-bar-tag.left-bar.active,\n.nav-bar-path.left-bar.active,\n.nav-bar-section.left-bar.operations.active {\n  border-left:4px solid var(--nav-accent-color);\n  color:var(--nav-hover-text-color);\n}\n\n.nav-bar-h1.colored-block.active,\n.nav-bar-h2.colored-block.active,\n.nav-bar-info.colored-block.active,\n.nav-bar-tag.colored-block.active,\n.nav-bar-path.colored-block.active,\n.nav-bar-section.colored-block.operations.active {\n  background-color: var(--nav-accent-color);\n  color: var(--nav-accent-text-color);\n  border-radius: 0;\n}\n\n.nav-bar-h1:hover,\n.nav-bar-h2:hover,\n.nav-bar-info:hover,\n.nav-bar-tag:hover,\n.nav-bar-path:hover {\n  color:var(--nav-hover-text-color);\n  background-color:var(--nav-hover-bg-color);\n}\n`;\n", "import { css } from 'lit';\n\nexport default css`\n#api-info {\n  font-size: calc(var(--font-size-regular) - 1px);\n  margin-top: 8px;\n  margin-left: -15px;\n}\n\n#api-info span:before {\n  content: \"|\";\n  display: inline-block;\n  opacity: 0.5;\n  width: 15px;\n  text-align: center;\n}\n#api-info span:first-child:before {\n  content: \"\";\n  width: 0px;\n}\n`;\n", "import { css } from 'lit';\n\n/*\nThis file is reserved for any custom css that developers want to add to\ncustomize their theme. Simply add your css to this file and yarn build.\n*/\n\nexport default css`\n\n`;\n", "/* For Delayed Event Handler Execution */\nexport function debounce(fn, delay) {\n  let timeoutID = null;\n  return (...args) => {\n    clearTimeout(timeoutID);\n    const that = this;\n    timeoutID = setTimeout(() => {\n      fn.apply(that, args);\n    }, delay);\n  };\n}\n\nexport const invalidCharsRegEx = /[\\s#:?&={}]/g; // used for generating valid html element ids by replacing the invalid chars with hyphen (-)\nexport const rapidocApiKey = '_rapidoc_api_key';\n\nexport function sleep(ms) {\n  // eslint-disable-next-line no-promise-executor-return\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\nexport function copyToClipboard(data, e) {\n  const btnEl = e.target;\n  const textArea = document.createElement('textarea');\n  textArea.value = data;\n  textArea.style.position = 'fixed'; // avoid scrolling to bottom\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  try {\n    document.execCommand('copy');\n    btnEl.innerText = 'Copied';\n    setTimeout(() => {\n      btnEl.innerText = 'Copy';\n    }, 5000);\n  } catch (err) {\n    console.error('Unable to copy', err); // eslint-disable-line no-console\n  }\n  document.body.removeChild(textArea);\n}\n\nexport function getBaseUrlFromUrl(url) {\n  const pathArray = url.split('/');\n  return `${pathArray[0]}//${pathArray[2]}`;\n}\n\nexport async function wait(ms) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, ms);\n  });\n}\n\nexport function componentIsInSearch(searchVal, component) {\n  return component.name.toLowerCase().includes(searchVal.toLowerCase());\n}\n\nexport function pathIsInSearch(searchVal, path, matchType = 'includes') {\n  if (matchType === 'includes') {\n    const stringToSearch = `${path.method} ${path.path} ${path.summary || path.description || ''} ${path.operationId || ''}`.toLowerCase();\n    return stringToSearch.includes(searchVal.toLowerCase());\n  }\n  const regex = new RegExp(searchVal, 'i');\n  return regex.test(`${path.method} ${path.path}`);\n}\n\nexport function schemaKeys(schemaProps, result = new Set()) {\n  if (!schemaProps) {\n    return result;\n  }\n  Object.keys(schemaProps).forEach((key) => {\n    result.add(key);\n    if (schemaProps[key].properties) {\n      schemaKeys(schemaProps[key].properties, result);\n    } else if (schemaProps[key].items?.properties) {\n      schemaKeys(schemaProps[key].items?.properties, result);\n    }\n  });\n  return result;\n}\n\nexport function advancedSearch(searchVal, allSpecTags, searchOptions = []) {\n  if (!searchVal.trim() || searchOptions.length === 0) {\n    return;\n  }\n\n  const pathsMatched = [];\n  allSpecTags.forEach((tag) => {\n    tag.paths.forEach((path) => {\n      let stringToSearch = '';\n      if (searchOptions.includes('search-api-path')) {\n        stringToSearch = path.path;\n      }\n      if (searchOptions.includes('search-api-descr')) {\n        stringToSearch = `${stringToSearch} ${path.summary || path.description || ''}`;\n      }\n      if (searchOptions.includes('search-api-params')) {\n        stringToSearch = `${stringToSearch} ${path.parameters?.map((v) => v.name).join(' ') || ''}`;\n      }\n\n      if (searchOptions.includes('search-api-request-body') && path.requestBody) {\n        let schemaKeySet = new Set();\n        for (const contentType in path.requestBody?.content) {\n          if (path.requestBody.content[contentType].schema?.properties) {\n            schemaKeySet = schemaKeys(path.requestBody.content[contentType].schema?.properties);\n          }\n          stringToSearch = `${stringToSearch} ${[...schemaKeySet].join(' ')}`;\n        }\n      }\n\n      if (searchOptions.includes('search-api-resp-descr')) {\n        stringToSearch = `${stringToSearch} ${Object.values(path.responses).map((v) => v.description || '').join(' ')}`;\n      }\n\n      if (stringToSearch.toLowerCase().includes(searchVal.trim().toLowerCase())) {\n        pathsMatched.push({\n          elementId: path.elementId,\n          method: path.method,\n          path: path.path,\n          summary: path.summary || path.description || '',\n          deprecated: path.deprecated,\n        });\n      }\n    });\n  });\n  return pathsMatched;\n}\n\n/*\nexport function prettyXml(sourceXmlString) {\n  const xmlDoc = new DOMParser().parseFromString(sourceXmlString, 'text/xml');\n  const xsltDoc = new DOMParser().parseFromString([\n    // describes how we want to modify the XML - indent everything\n    `<xsl:stylesheet xmlns:xsl=\"http://www.w3.org/1999/XSL/Transform\">\n      <xsl:strip-space elements=\"*\"/>\n        <xsl:template match=\"para[content-style][not(text())]\">\n          <xsl:value-of select=\"normalize-space(.)\"/>\n        </xsl:template>\n        <xsl:template match=\"node()|@*\">\n          <xsl:copy><xsl:apply-templates select=\"node()|@*\"/></xsl:copy>\n        </xsl:template>\n        <xsl:output indent=\"yes\"/>\n      </xsl:stylesheet>`,\n  ].join('\\n'), 'application/xml');\n  const xsltProcessor = new XSLTProcessor();\n  xsltProcessor.importStylesheet(xsltDoc);\n  const resultDoc = xsltProcessor.transformToDocument(xmlDoc);\n  return new XMLSerializer().serializeToString(resultDoc);\n}\n*/\n\nexport function downloadResource(url, fileName) {\n  if (url) {\n    const a = document.createElement('a');\n    document.body.appendChild(a);\n    a.style = 'display: none';\n    a.href = url;\n    a.download = fileName;\n    a.click();\n    a.remove();\n  }\n}\n\nexport function viewResource(url) {\n  if (url) {\n    const a = document.createElement('a');\n    document.body.appendChild(a);\n    a.style = 'display: none';\n    a.href = url;\n    a.target = '_blank';\n    a.click();\n    a.remove();\n  }\n}\n", "/* eslint-disable arrow-body-style */\nimport { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\n\nconst codeVerifier = '731DB1C3F7EA533B85E29492D26AA-1234567890-1234567890';\nconst codeChallenge = '4FatVDBJKPAo4JgLLaaQFMUcQPn5CrPRvLlaob9PTYc'; // Base64 encoded SHA-256\n\nconst localStorageKey = 'rapidoc';\n\nexport function applyApiKey(securitySchemeId, username = '', password = '', providedApikeyVal = '') {\n  const securityObj = this.resolvedSpec.securitySchemes?.find((v) => (v.securitySchemeId === securitySchemeId));\n  if (!securityObj) {\n    return false;\n  }\n  let finalApiKeyValue = '';\n  if (securityObj.scheme?.toLowerCase() === 'basic') {\n    if (username) {\n      finalApiKeyValue = `Basic ${Buffer.from(`${username}:${password}`, 'utf8').toString('base64')}`;\n    }\n  } else if (providedApikeyVal) {\n    securityObj.value = providedApikeyVal;\n    finalApiKeyValue = `${securityObj.scheme?.toLowerCase() === 'bearer' ? 'Bearer ' : ''}${providedApikeyVal}`;\n  }\n  if (finalApiKeyValue) {\n    securityObj.finalKeyValue = finalApiKeyValue;\n    this.requestUpdate();\n    return true;\n  }\n  return false;\n}\n\nexport function onClearAllApiKeys() {\n  this.resolvedSpec.securitySchemes?.forEach((v) => {\n    v.user = '';\n    v.password = '';\n    v.value = '';\n    v.finalKeyValue = '';\n  });\n  this.requestUpdate();\n}\n\nfunction getPersistedApiKeys() {\n  return JSON.parse(localStorage.getItem(localStorageKey)) || {};\n}\n\nfunction setPersistedApiKeys(obj) {\n  localStorage.setItem(localStorageKey, JSON.stringify(obj));\n}\n\nexport function recoverPersistedApiKeys() {\n  const rapidocLs = getPersistedApiKeys.call(this);\n  Object.values(rapidocLs).forEach((p) => {\n    applyApiKey.call(this, p.securitySchemeId, p.username, p.password, p.value);\n  });\n}\n\nfunction onApiKeyChange(securitySchemeId) {\n  let apiKeyValue = '';\n  const securityObj = this.resolvedSpec.securitySchemes.find((v) => (v.securitySchemeId === securitySchemeId));\n  if (securityObj) {\n    const trEl = this.shadowRoot.getElementById(`security-scheme-${securitySchemeId}`);\n    if (trEl) {\n      if (securityObj.type && securityObj.scheme && securityObj.type === 'http' && securityObj.scheme.toLowerCase() === 'basic') {\n        const userVal = trEl.querySelector('.api-key-user').value.trim();\n        const passwordVal = trEl.querySelector('.api-key-password').value.trim();\n        applyApiKey.call(this, securitySchemeId, userVal, passwordVal);\n      } else {\n        apiKeyValue = trEl.querySelector('.api-key-input').value.trim();\n        applyApiKey.call(this, securitySchemeId, '', '', apiKeyValue);\n      }\n      if (this.persistAuth === 'true') {\n        const rapidocLs = getPersistedApiKeys.call(this);\n        rapidocLs[securitySchemeId] = securityObj;\n        setPersistedApiKeys.call(this, rapidocLs);\n      }\n    }\n  }\n}\n\n// Updates the OAuth Access Token (API key), so it reflects in UI and gets used in TRY calls\nfunction updateOAuthKey(securitySchemeId, accessToken, tokenType = 'Bearer') {\n  const securityObj = this.resolvedSpec.securitySchemes.find((v) => (v.securitySchemeId === securitySchemeId));\n  securityObj.finalKeyValue = `${(tokenType.toLowerCase() === 'bearer' ? 'Bearer' : (tokenType.toLowerCase() === 'mac' ? 'MAC' : tokenType))} ${accessToken}`;\n  this.requestUpdate();\n}\n\n/* eslint-disable no-console */\n// Gets Access-Token in exchange of Authorization Code\nasync function fetchAccessToken(tokenUrl, clientId, clientSecret, redirectUrl, grantType, authCode, securitySchemeId, authFlowDivEl, sendClientSecretIn = 'header', scopes = null, username = null, password = null) {\n  const respDisplayEl = authFlowDivEl ? authFlowDivEl.querySelector('.oauth-resp-display') : undefined;\n  const urlFormParams = new URLSearchParams();\n  const headers = new Headers();\n  urlFormParams.append('grant_type', grantType);\n  if (grantType === 'authorization_code') {\n    urlFormParams.append('client_id', clientId);\n    urlFormParams.append('client_secret', clientSecret);\n  }\n  if (grantType !== 'client_credentials' && grantType !== 'password') {\n    urlFormParams.append('redirect_uri', redirectUrl);\n  }\n  if (authCode) {\n    urlFormParams.append('code', authCode);\n    urlFormParams.append('code_verifier', codeVerifier); // for PKCE\n  }\n  if (sendClientSecretIn === 'header') {\n    headers.set('Authorization', `Basic ${Buffer.from(`${clientId}:${clientSecret}`, 'utf8').toString('base64')}`);\n  } else if (grantType !== 'authorization_code') {\n    urlFormParams.append('client_id', clientId);\n    urlFormParams.append('client_secret', clientSecret);\n  }\n  if (grantType === 'password') {\n    urlFormParams.append('username', username);\n    urlFormParams.append('password', password);\n  }\n  if (scopes) {\n    urlFormParams.append('scope', scopes);\n  }\n\n  try {\n    const resp = await fetch(tokenUrl, { method: 'POST', headers, body: urlFormParams });\n    const tokenResp = await resp.json();\n    if (resp.ok) {\n      if (tokenResp.token_type && tokenResp.access_token) {\n        updateOAuthKey.call(this, securitySchemeId, tokenResp.access_token, tokenResp.token_type);\n        if (respDisplayEl) {\n          respDisplayEl.innerHTML = '<span style=\"color:var(--green)\">Access Token Received</span>';\n        }\n        return true;\n      }\n    } else {\n      if (respDisplayEl) {\n        respDisplayEl.innerHTML = `<span style=\"color:var(--red)\">${tokenResp.error_description || tokenResp.error_description || 'Unable to get access token'}</span>`;\n      }\n      return false;\n    }\n  } catch (err) {\n    if (respDisplayEl) {\n      respDisplayEl.innerHTML = '<span style=\"color:var(--red)\">Failed to get access token</span>';\n    }\n    return false;\n  }\n}\n\n// Gets invoked when it receives the Authorization Code from the other window via message-event\nasync function onWindowMessageEvent(msgEvent, winObj, tokenUrl, clientId, clientSecret, redirectUrl, grantType, sendClientSecretIn, securitySchemeId, authFlowDivEl) {\n  sessionStorage.removeItem('winMessageEventActive');\n  winObj.close();\n  if (msgEvent.data.fake) {\n    return;\n  }\n  if (!msgEvent.data) {\n    console.warn('RapiDoc: Received no data with authorization message');\n  }\n  if (msgEvent.data.error) {\n    console.warn('RapiDoc: Error while receiving data');\n  }\n  if (msgEvent.data) {\n    if (msgEvent.data.responseType === 'code') {\n      // Authorization Code flow\n      fetchAccessToken.call(this, tokenUrl, clientId, clientSecret, redirectUrl, grantType, msgEvent.data.code, securitySchemeId, authFlowDivEl, sendClientSecretIn);\n    } else if (msgEvent.data.responseType === 'token') {\n      // Implicit flow\n      updateOAuthKey.call(this, securitySchemeId, msgEvent.data.access_token, msgEvent.data.token_type);\n    }\n  }\n}\n\n// code_challenge generator for PKCE flow\n// TODO: Implement dynamic generation of code-challenge based on code-verifier\n/*\nasync function generateCodeChallenge() {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(codeVerifier);\n  const sha256Hash = await window.crypto.subtle.digest('SHA-256', data); // returns Unit8Array\n  // const utf8Decoder = new TextDecoder();\n  // const b64EncodedSha256 = btoa(utf8Decoder.decode(sha256Hash));\n  const b64EncodedSha256 = base64encode(sha256Hash);\n  return b64EncodedSha256;\n}\n*/\n\nasync function onInvokeOAuthFlow(securitySchemeId, flowType, authUrl, tokenUrl, e) {\n  const authFlowDivEl = e.target.closest('.oauth-flow');\n  const clientId = authFlowDivEl.querySelector('.oauth-client-id') ? authFlowDivEl.querySelector('.oauth-client-id').value.trim() : '';\n  const clientSecret = authFlowDivEl.querySelector('.oauth-client-secret') ? authFlowDivEl.querySelector('.oauth-client-secret').value.trim() : '';\n  const username = authFlowDivEl.querySelector('.api-key-user') ? authFlowDivEl.querySelector('.api-key-user').value.trim() : '';\n  const password = authFlowDivEl.querySelector('.api-key-password') ? authFlowDivEl.querySelector('.api-key-password').value.trim() : '';\n  const sendClientSecretIn = authFlowDivEl.querySelector('.oauth-send-client-secret-in') ? authFlowDivEl.querySelector('.oauth-send-client-secret-in').value.trim() : 'header';\n  const checkedScopeEls = [...authFlowDivEl.querySelectorAll('.scope-checkbox:checked')];\n  const pkceCheckboxEl = authFlowDivEl.querySelector(`#${securitySchemeId}-pkce`);\n  const state = (`${Math.random().toString(36).slice(2, 9)}random${Math.random().toString(36).slice(2, 9)}`);\n  const nonce = (`${Math.random().toString(36).slice(2, 9)}random${Math.random().toString(36).slice(2, 9)}`);\n  // const codeChallenge = await generateCodeChallenge(codeVerifier);\n  const redirectUrlObj = new URL(`${window.location.origin}${window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'))}/${this.oauthReceiver}`);\n  let grantType = '';\n  let responseType = '';\n  let newWindow;\n\n  // clear previous error messages\n  const errEls = [...authFlowDivEl.parentNode.querySelectorAll('.oauth-resp-display')];\n  errEls.forEach((v) => { v.innerHTML = ''; });\n\n  if (flowType === 'authorizationCode' || flowType === 'implicit') {\n    const authUrlObj = new URL(authUrl);\n    if (flowType === 'authorizationCode') {\n      grantType = 'authorization_code';\n      responseType = 'code';\n    } else if (flowType === 'implicit') {\n      responseType = 'token';\n    }\n    const authCodeParams = new URLSearchParams(authUrlObj.search);\n    const selectedScopes = checkedScopeEls.map((v) => v.value).join(' ');\n    if (selectedScopes) {\n      authCodeParams.set('scope', selectedScopes);\n    }\n    authCodeParams.set('client_id', clientId);\n    authCodeParams.set('redirect_uri', redirectUrlObj.toString());\n    authCodeParams.set('response_type', responseType);\n    authCodeParams.set('state', state);\n    authCodeParams.set('nonce', nonce);\n    if (pkceCheckboxEl && pkceCheckboxEl.checked) {\n      authCodeParams.set('code_challenge', codeChallenge);\n      authCodeParams.set('code_challenge_method', 'S256');\n    }\n    authCodeParams.set('show_dialog', true);\n    authUrlObj.search = authCodeParams.toString();\n    // If any older message-event-listener is active then fire a fake message to remove it (these are single time listeners)\n    if (sessionStorage.getItem('winMessageEventActive') === 'true') {\n      window.postMessage({ fake: true }, this);\n    }\n    setTimeout(() => {\n      newWindow = window.open(authUrlObj.toString());\n      if (!newWindow) {\n        console.error(`RapiDoc: Unable to open ${authUrlObj.toString()} in a new window`);\n      } else {\n        sessionStorage.setItem('winMessageEventActive', 'true');\n        window.addEventListener(\n          'message',\n          (msgEvent) => onWindowMessageEvent.call(this, msgEvent, newWindow, tokenUrl, clientId, clientSecret, redirectUrlObj.toString(), grantType, sendClientSecretIn, securitySchemeId, authFlowDivEl),\n          { once: true },\n        );\n      }\n    }, 10);\n  } else if (flowType === 'clientCredentials') {\n    grantType = 'client_credentials';\n    const selectedScopes = checkedScopeEls.map((v) => v.value).join(' ');\n    fetchAccessToken.call(this, tokenUrl, clientId, clientSecret, redirectUrlObj.toString(), grantType, '', securitySchemeId, authFlowDivEl, sendClientSecretIn, selectedScopes);\n  } else if (flowType === 'password') {\n    grantType = 'password';\n    const selectedScopes = checkedScopeEls.map((v) => v.value).join(' ');\n    fetchAccessToken.call(this, tokenUrl, clientId, clientSecret, redirectUrlObj.toString(), grantType, '', securitySchemeId, authFlowDivEl, sendClientSecretIn, selectedScopes, username, password);\n  }\n}\n/* eslint-enable no-console */\n\n/* eslint-disable indent */\n\nfunction oAuthFlowTemplate(flowName, clientId, clientSecret, securitySchemeId, authFlow, defaultScopes = [], receiveTokenIn = 'header') {\n  let { authorizationUrl, tokenUrl, refreshUrl } = authFlow;\n  const pkceOnly = authFlow['x-pkce-only'] || false;\n  const isUrlAbsolute = (url) => (url.indexOf('://') > 0 || url.indexOf('//') === 0);\n  // Calculcate base URL\n  const url = new URL(this.selectedServer.computedUrl);\n  const baseUrl = url.origin;\n\n  if (refreshUrl && !isUrlAbsolute(refreshUrl)) {\n    refreshUrl = `${baseUrl}/${refreshUrl.replace(/^\\//, '')}`;\n  }\n  if (tokenUrl && !isUrlAbsolute(tokenUrl)) {\n    tokenUrl = `${baseUrl}/${tokenUrl.replace(/^\\//, '')}`;\n  }\n  if (authorizationUrl && !isUrlAbsolute(authorizationUrl)) {\n    authorizationUrl = `${baseUrl}/${authorizationUrl.replace(/^\\//, '')}`;\n  }\n  let flowNameDisplay;\n  if (flowName === 'authorizationCode') {\n    flowNameDisplay = 'Authorization Code Flow';\n  } else if (flowName === 'clientCredentials') {\n    flowNameDisplay = 'Client Credentials Flow';\n  } else if (flowName === 'implicit') {\n    flowNameDisplay = 'Implicit Flow';\n  } else if (flowName === 'password') {\n    flowNameDisplay = 'Password Flow';\n  } else {\n    flowNameDisplay = flowName;\n  }\n  return html`\n    <div class=\"oauth-flow ${flowName}\" style=\"padding: 12px 0; margin-bottom:12px;\">\n      <div class=\"tiny-title upper\" style=\"margin-bottom:8px;\">${flowNameDisplay}</div>\n      ${authorizationUrl\n        ? html`<div style=\"margin-bottom:5px\"><span style=\"width:75px; display: inline-block;\">Auth URL</span> <span class=\"mono-font\"> ${authorizationUrl} </span></div>`\n        : ''\n      }\n      ${tokenUrl\n        ? html`<div style=\"margin-bottom:5px\"><span style=\"width:75px; display: inline-block;\">Token URL</span> <span class=\"mono-font\">${tokenUrl}</span></div>`\n        : ''\n      }\n      ${refreshUrl\n        ? html`<div style=\"margin-bottom:5px\"><span style=\"width:75px; display: inline-block;\">Refresh URL</span> <span class=\"mono-font\">${refreshUrl}</span></div>`\n        : ''\n      }\n      ${flowName === 'authorizationCode' || flowName === 'clientCredentials' || flowName === 'implicit' || flowName === 'password'\n        ? html`\n          ${authFlow.scopes\n            ? html`\n              <span> Scopes </span>\n              <div class= \"oauth-scopes\" part=\"section-auth-scopes\" style = \"width:100%; display:flex; flex-direction:column; flex-wrap:wrap; margin:0 0 10px 24px\">\n                ${Object.entries(authFlow.scopes).map((scopeAndDescr, index) => html`\n                  <div class=\"m-checkbox\" style=\"display:inline-flex; align-items:center\">\n                    <input type=\"checkbox\" part=\"checkbox checkbox-auth-scope\" class=\"scope-checkbox\" id=\"${securitySchemeId}${flowName}${index}\" ?checked=\"${defaultScopes.includes(scopeAndDescr[0])}\" value=\"${scopeAndDescr[0]}\">\n                    <label for=\"${securitySchemeId}${flowName}${index}\" style=\"margin-left:5px; cursor:pointer\">\n                      <span class=\"mono-font\">${scopeAndDescr[0]}</span>\n                        ${scopeAndDescr[0] !== scopeAndDescr[1] ? ` - ${scopeAndDescr[1] || ''}` : ''}\n                    </label>\n                  </div>\n                `)}\n              </div>\n            `\n            : ''\n          }\n          ${flowName === 'password'\n            ? html`\n              <div style=\"margin:5px 0\">\n                <input type=\"text\" value = \"\" placeholder=\"username\" spellcheck=\"false\" class=\"oauth2 ${flowName} ${securitySchemeId} api-key-user\" part=\"textbox textbox-username\">\n                <input type=\"password\" value = \"\" placeholder=\"password\" spellcheck=\"false\" class=\"oauth2 ${flowName} ${securitySchemeId} api-key-password\" style = \"margin:0 5px;\" part=\"textbox textbox-password\">\n              </div>`\n            : ''\n          }\n          <div>\n            ${flowName === 'authorizationCode'\n              ? html`\n                <div style=\"margin: 16px 0 4px\">\n                  <input type=\"checkbox\" part=\"checkbox checkbox-auth-scope\" id=\"${securitySchemeId}-pkce\" checked ?disabled=${pkceOnly}>\n                  <label for=\"${securitySchemeId}-pkce\" style=\"margin:0 16px 0 4px; line-height:24px; cursor:pointer\">\n                   Send Proof Key for Code Exchange (PKCE)\n                  </label>\n                </div>\n              `\n              : ''\n            }\n            <input type=\"text\" part=\"textbox textbox-auth-client-id\" value = \"${clientId || ''}\" placeholder=\"client-id\" spellcheck=\"false\" class=\"oauth2 ${flowName} ${securitySchemeId} oauth-client-id\">\n            ${flowName === 'authorizationCode' || flowName === 'clientCredentials' || flowName === 'password'\n              ? html`\n                <input\n                  type=\"password\" part=\"textbox textbox-auth-client-secret\"\n                  value = \"${clientSecret || ''}\" placeholder=\"client-secret\" spellcheck=\"false\"\n                  class=\"oauth2 ${flowName} ${securitySchemeId}\n                  oauth-client-secret\"\n                  style = \"margin:0 5px;${pkceOnly ? 'display:none;' : ''}\"\n                >\n                <select style=\"margin-right:5px;${pkceOnly ? 'display:none;' : ''}\" class=\"${flowName} ${securitySchemeId} oauth-send-client-secret-in\">\n                  <option value = 'header' .selected = ${receiveTokenIn === 'header'} > Authorization Header </option>\n                  <option value = 'request-body' .selected = ${receiveTokenIn === 'request-body'}> Request Body </option>\n                </select>`\n              : ''\n            }\n            ${flowName === 'authorizationCode' || flowName === 'clientCredentials' || flowName === 'implicit' || flowName === 'password'\n              ? html`\n                <button class=\"m-btn thin-border\" part=\"btn btn-outline\"\n                  @click=\"${(e) => { onInvokeOAuthFlow.call(this, securitySchemeId, flowName, authorizationUrl, tokenUrl, e); }}\"\n                > GET TOKEN </button>`\n              : ''\n            }\n          </div>\n          <div class=\"oauth-resp-display red-text small-font-size\"></div>\n          `\n        : ''\n      }\n    </div>\n  `;\n}\n\nfunction removeApiKey(securitySchemeId) {\n  const securityObj = this.resolvedSpec.securitySchemes?.find((v) => (v.securitySchemeId === securitySchemeId));\n  securityObj.user = '';\n  securityObj.password = '';\n  securityObj.value = '';\n  securityObj.finalKeyValue = '';\n  if (this.persistAuth === 'true') {\n    const rapidocLs = getPersistedApiKeys.call(this);\n    delete rapidocLs[securityObj.securitySchemeId];\n    setPersistedApiKeys.call(this, rapidocLs);\n  }\n  this.requestUpdate();\n}\n\nexport default function securitySchemeTemplate() {\n  if (!this.resolvedSpec) { return ''; }\n  const providedApiKeys = this.resolvedSpec.securitySchemes?.filter((v) => (v.finalKeyValue));\n  if (!providedApiKeys) {\n    return;\n  }\n  return html`\n  <section id='auth' part=\"section-auth\" style=\"text-align:left; direction:ltr; margin-top:24px; margin-bottom:24px;\" class = 'observe-me ${'read focused'.includes(this.renderStyle) ? 'section-gap--read-mode' : 'section-gap '}'>\n    <div class='sub-title regular-font'> AUTHENTICATION </div>\n\n    <div class=\"small-font-size\" style=\"display:flex; align-items: center; min-height:30px\">\n      ${providedApiKeys.length > 0\n        ? html`\n          <div class=\"blue-text\"> ${providedApiKeys.length} API key applied </div>\n          <div style=\"flex:1\"></div>\n          <button class=\"m-btn thin-border\" part=\"btn btn-outline\" @click=${() => { onClearAllApiKeys.call(this); }}>CLEAR ALL API KEYS</button>`\n        : html`<div class=\"red-text\">No API key applied</div>`\n      }\n    </div>\n    ${this.resolvedSpec.securitySchemes && this.resolvedSpec.securitySchemes.length > 0\n      ? html`\n        <table role=\"presentation\" id=\"auth-table\" class='m-table padded-12' style=\"width:100%;\">\n          ${this.resolvedSpec.securitySchemes.map((v) => html`\n            <tr id=\"security-scheme-${v.securitySchemeId}\" class=\"${v.type.toLowerCase()}\">\n              <td style=\"max-width:500px; overflow-wrap: break-word;\">\n                <div style=\"line-height:28px; margin-bottom:5px;\">\n                  <span style=\"font-weight:bold; font-size:var(--font-size-regular)\">${v.typeDisplay}</span>\n                  ${v.finalKeyValue\n                    ? html`\n                      <span class='blue-text'>  ${v.finalKeyValue ? 'Key Applied' : ''} </span>\n                      <button class=\"m-btn thin-border small\" part=\"btn btn-outline\" @click=${() => { removeApiKey.call(this, v.securitySchemeId); }}>REMOVE</button>\n                      `\n                    : ''\n                  }\n                </div>\n                ${v.description\n                  ? html`\n                    <div class=\"m-markdown\">\n                      ${unsafeHTML(marked(v.description || ''))}\n                    </div>`\n                  : ''\n                }\n\n                ${(v.type.toLowerCase() === 'apikey') || (v.type.toLowerCase() === 'http' && v.scheme.toLowerCase() === 'bearer')\n                  ? html`\n                    <div style=\"margin-bottom:5px\">\n                      ${v.type.toLowerCase() === 'apikey'\n                        ? html`Send <code>${v.name}</code> in <code>${v.in}</code>`\n                        : html`Send <code>Authorization</code> in <code>header</code> containing the word <code>Bearer</code> followed by a space and a Token String.`\n                      }\n                    </div>\n                    <div style=\"max-height:28px;\">\n                      ${v.in !== 'cookie'\n                        ? html`\n                          <input type = \"text\" value = \"${v.value}\" class=\"${v.type} ${v.securitySchemeId} api-key-input\" placeholder = \"api-token\" spellcheck = \"false\">\n                          <button class=\"m-btn thin-border\" style = \"margin-left:5px;\"\n                            part = \"btn btn-outline\"\n                            @click=\"${(e) => { onApiKeyChange.call(this, v.securitySchemeId, e); }}\">\n                            ${v.finalKeyValue ? 'UPDATE' : 'SET'}\n                          </button>`\n                        : html`<span class=\"gray-text\" style=\"font-size::var(--font-size-small)\"> cookies cannot be set from here</span>`\n                      }\n                    </div>`\n                  : ''\n                }\n                ${v.type.toLowerCase() === 'http' && v.scheme.toLowerCase() === 'basic'\n                  ? html`\n                    <div style=\"margin-bottom:5px\">\n                      Send <code>Authorization</code> in <code>header</code> containing the word <code>Basic</code> followed by a space and a base64 encoded string of <code>username:password</code>.\n                    </div>\n                    <div>\n                      <input type=\"text\" value = \"${v.user}\" placeholder=\"username\" spellcheck=\"false\" class=\"${v.type} ${v.securitySchemeId} api-key-user\" style=\"width:100px\">\n                      <input type=\"password\" value = \"${v.password}\" placeholder=\"password\" spellcheck=\"false\" class=\"${v.type} ${v.securitySchemeId} api-key-password\" style = \"width:100px; margin:0 5px;\">\n                      <button class=\"m-btn thin-border\"\n                        @click=\"${(e) => { onApiKeyChange.call(this, v.securitySchemeId, e); }}\"\n                        part = \"btn btn-outline\"\n                      >\n                        ${v.finalKeyValue ? 'UPDATE' : 'SET'}\n                      </button>\n                    </div>`\n                  : ''\n                }\n              </td>\n            </tr>\n            ${v.type.toLowerCase() === 'oauth2'\n              ? html`\n                <tr>\n                  <td style=\"border:none; padding-left:48px\">\n                    ${Object.keys(v.flows).map((f) => oAuthFlowTemplate\n                      .call(\n                        this,\n                        f,\n                        (v.flows[f]['x-client-id'] || v['x-client-id'] || ''),\n                        (v.flows[f]['x-client-secret'] || v['x-client-secret'] || ''),\n                        v.securitySchemeId,\n                        v.flows[f],\n                        (v.flows[f]['x-default-scopes'] || v['x-default-scopes']),\n                        (v.flows[f]['x-receive-token-in'] || v['x-receive-token-in']),\n                      ))}\n                  </td>\n                </tr>\n                `\n              : ''\n            }\n          `)}\n        </table>`\n      : ''\n    }\n    <slot name=\"auth\"></slot>\n  </section>\n`;\n}\n\nexport function pathSecurityTemplate(pathSecurity) {\n  if (this.resolvedSpec.securitySchemes && pathSecurity) {\n    const orSecurityKeys1 = [];\n    if (Array.isArray(pathSecurity)) {\n      if (pathSecurity.length === 0) {\n        return '';\n      }\n    } else {\n      return '';\n    }\n    pathSecurity.forEach((pSecurity) => {\n      const andSecurityKeys1 = [];\n      const andKeyTypes = [];\n      if (Object.keys(pSecurity).length === 0) {\n        orSecurityKeys1.push({\n          securityTypes: 'None',\n          securityDefs: [],\n        });\n      } else {\n        Object.keys(pSecurity).forEach((pathSecurityKey) => {\n          let pathScopes = '';\n          const s = this.resolvedSpec.securitySchemes.find((ss) => ss.securitySchemeId === pathSecurityKey);\n          if (pSecurity[pathSecurityKey] && Array.isArray(pSecurity[pathSecurityKey])) {\n            pathScopes = pSecurity[pathSecurityKey].join(', ');\n          }\n          if (s) {\n            andKeyTypes.push(s.typeDisplay);\n            andSecurityKeys1.push({ ...s, ...({ scopes: pathScopes }) });\n          }\n        });\n        orSecurityKeys1.push({\n          securityTypes: andKeyTypes.length > 1 ? `${andKeyTypes[0]} + ${andKeyTypes.length - 1} more` : andKeyTypes[0],\n          securityDefs: andSecurityKeys1,\n        });\n      }\n    });\n    return html`<div style=\"position:absolute; top:3px; right:2px; font-size:var(--font-size-small); line-height: 1.5;\">\n      <div style=\"position:relative; display:flex; min-width:350px; max-width:700px; justify-content: flex-end;\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" fill=\"none\" style=\"stroke:var(--fg3)\"> <rect x=\"5\" y=\"11\" width=\"14\" height=\"10\" rx=\"2\" /> <circle cx=\"12\" cy=\"16\" r=\"1\" /> <path d=\"M8 11v-4a4 4 0 0 1 8 0v4\" /></svg>\n          ${orSecurityKeys1.map((orSecurityItem1, i) => html`\n          ${orSecurityItem1.securityTypes\n            ? html`\n              ${i !== 0 ? html`<div style=\"padding:3px 4px;\"> OR </div>` : ''}\n              <div class=\"tooltip\">\n                <div style = \"padding:2px 4px; white-space:nowrap; text-overflow:ellipsis;max-width:150px; overflow:hidden;\">\n                  ${this.updateRoute === 'true' && this.allowAuthentication === 'true'\n                    ? html`<a part=\"anchor anchor-operation-security\" href=\"#auth\"> ${orSecurityItem1.securityTypes} </a>`\n                    : html`${orSecurityItem1.securityTypes}`\n                  }\n                </div>\n                <div class=\"tooltip-text\" style=\"position:absolute; color: var(--fg); top:26px; right:0; border:1px solid var(--border-color);padding:2px 4px; display:block;\">\n                  ${orSecurityItem1.securityDefs.length > 1 ? html`<div>Requires <b>all</b> of the following </div>` : ''}\n                  <div style=\"padding-left: 8px\">\n                    ${orSecurityItem1.securityDefs.map((andSecurityItem, j) => {\n                      const scopeHtml = html`${andSecurityItem.scopes !== ''\n                        ? html`\n                          <div>\n                            <b>Required scopes:</b>\n                            <br/>\n                            <div style=\"margin-left:8px\">\n                              ${andSecurityItem.scopes.split(',').map((scope, cnt) => html`${cnt === 0 ? '' : '┃'}<span>${scope}</span>`)}\n                            </div>\n                          </div>`\n                        : ''\n                      }`;\n\n                      return html`\n                      ${andSecurityItem.type === 'oauth2'\n                        ? html`\n                          <div>\n                            ${orSecurityItem1.securityDefs.length > 1\n                              ? html`<b>${j + 1}.</b> &nbsp;`\n                              : 'Needs'\n                            }\n                            OAuth Token <span style=\"font-family:var(--font-mono); color:var(--primary-color);\">${andSecurityItem.securitySchemeId}</span> in <b>Authorization header</b>\n                            ${scopeHtml}\n                          </div>`\n                        : andSecurityItem.type === 'http'\n                          ? html`\n                            <div>\n                              ${orSecurityItem1.securityDefs.length > 1 ? html`<b>${j + 1}.</b> &nbsp;` : html`Requires`}\n                              ${andSecurityItem.scheme === 'basic' ? 'Base 64 encoded username:password' : 'Bearer Token'} in <b>Authorization header</b>\n                              ${scopeHtml}\n                            </div>`\n                          : html`\n                            <div>\n                              ${orSecurityItem1.securityDefs.length > 1 ? html`<b>${j + 1}.</b> &nbsp;` : html`Requires`}\n                              Token in <b>${andSecurityItem.name} ${andSecurityItem.in}</b>\n                              ${scopeHtml}\n                            </div>`\n                      }`;\n                    })}\n                  </div>\n                </div>\n              </div>\n            `\n            : ''\n          }\n        `)\n        }\n      </div>\n    `;\n  }\n  return '';\n}\n\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport Prism from 'prismjs';\nimport { copyToClipboard } from '~/utils/common-utils';\n\n/* eslint-disable indent */\nexport default function codeSamplesTemplate(xCodeSamples) {\n  return html`\n  <section class=\"table-title\" style=\"margin-top:24px;\">CODE SAMPLES</div>\n  <div class=\"tab-panel col\"\n    @click=\"${\n      (e) => {\n        if (!e.target.classList.contains('tab-btn')) { return; }\n        const clickedTab = e.target.dataset.tab;\n\n        const tabButtons = [...e.currentTarget.querySelectorAll('.tab-btn')];\n        const tabContents = [...e.currentTarget.querySelectorAll('.tab-content')];\n        tabButtons.forEach((tabBtnEl) => tabBtnEl.classList[tabBtnEl.dataset.tab === clickedTab ? 'add' : 'remove']('active'));\n        tabContents.forEach((tabBodyEl) => { tabBodyEl.style.display = (tabBodyEl.dataset.tab === clickedTab ? 'block' : 'none'); });\n      }\n    }\">\n    <div class=\"tab-buttons row\" style=\"width:100; overflow\">\n      ${xCodeSamples.map((v, i) => html`<button class=\"tab-btn ${i === 0 ? 'active' : ''}\" data-tab = '${v.lang}${i}'> ${v.label || v.lang} </button>`)}\n    </div>\n    ${xCodeSamples.map((v, i) => html`\n      <div class=\"tab-content m-markdown\" style= \"display:${i === 0 ? 'block' : 'none'}\" data-tab = '${v.lang}${i}'>\n        <button class=\"toolbar-btn\" style = \"position:absolute; top:12px; right:8px\" @click='${(e) => { copyToClipboard(v.source, e); }}'> Copy </button>\n        <pre><code class=\"language\">${Prism.languages[v.lang?.toLowerCase()] ? unsafeHTML(Prism.highlight(v.source, Prism.languages[v.lang?.toLowerCase()], v.lang?.toLowerCase())) : v.source}</code></pre>\n      </div>`)\n    }\n  </div>  \n  </section>`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\n\n/* eslint-disable indent */\nexport default function callbackTemplate(callbacks) {\n  return html`\n    <div class=\"req-res-title\" style=\"margin-top:12px\">CALLBACKS</div>\n    ${Object.entries(callbacks).map((kv) => html`\n      <div class=\"tiny-title\" style=\"padding: 12px; border:1px solid var(--light-border-color)\"> \n        ${kv[0]}\n        ${Object.entries(kv[1]).map((pathObj) => html`\n          <div class=\"mono-font small-font-size\" style=\"display:flex; margin-left:16px;\">\n            <div style=\"width:100%\"> \n              ${Object.entries(pathObj[1]).map((method) => html`\n                <div>\n                  <div style=\"margin-top:12px;\">\n                    <div class=\"method method-fg ${method[0]}\" style=\"width:70px; border:none; margin:0; padding:0; line-height:20px; vertical-align: baseline;text-align:left\"> \n                      <span style=\"font-size:20px;\"> &#x2944; </span> \n                      ${method[0]} \n                    </div>\n                    <span style=\"line-height:20px; vertical-align: baseline;\">${pathObj[0]} </span>\n                  </div>  \n                  <div class='expanded-req-resp-container'>\n                    <api-request\n                      class = \"${this.renderStyle}-mode callback\"  \n                      style = \"width:100%;\"\n                      callback = \"true\"\n                      method = \"${method[0] || ''}\", \n                      path = \"${pathObj[0] || ''}\" \n                      .parameters = \"${method[1]?.parameters || ''}\" \n                      .request_body = \"${method[1]?.requestBody || ''}\"\n                      fill-request-fields-with-example = \"${this.fillRequestFieldsWithExample}\"\n                      allow-try = \"false\"\n                      render-style=\"${this.renderStyle}\" \n                      schema-style = \"${this.schemaStyle}\"\n                      active-schema-tab = \"${this.defaultSchemaTab}\"\n                      schema-expand-level = \"${this.schemaExpandLevel}\"\n                      schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n                      allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n                      schema-hide-read-only = \"false\"\n                      schema-hide-write-only = \"${this.schemaHideWriteOnly === 'never' ? 'false' : 'true'}\"\n                      fetch-credentials = \"${this.fetchCredentials}\"\n                      exportparts = \"wrap-request-btn:wrap-request-btn, btn:btn, btn-fill:btn-fill, btn-outline:btn-outline, btn-try:btn-try, btn-clear:btn-clear, btn-clear-resp:btn-clear-resp,\n                        file-input:file-input, textbox:textbox, textbox-param:textbox-param, textarea:textarea, textarea-param:textarea-param, \n                        anchor:anchor, anchor-param-example:anchor-param-example, schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n                      > </api-request>\n\n                    <api-response\n                      style = \"width:100%;\"\n                      class = \"${this.renderStyle}-mode\"\n                      callback = \"true\"\n                      .responses=\"${method[1]?.responses}\"\n                      render-style=\"${this.renderStyle}\"\n                      schema-style=\"${this.schemaStyle}\"\n                      active-schema-tab = \"${this.defaultSchemaTab}\"\n                      schema-expand-level = \"${this.schemaExpandLevel}\"\n                      schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n                      allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n                      schema-hide-read-only = \"${this.schemaHideReadOnly === 'never' ? 'false' : 'true'}\"\n                      schema-hide-write-only = \"false\"\n                      exportparts = \"btn:btn, btn-response-status:btn-response-status, btn-selected-response-status:btn-selected-response-status, btn-fill:btn-fill, btn-copy:btn-copy,\n                      schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n                    > </api-response>\n                  </div>\n                </div>  \n              `)}\n            </div>  \n          </div>  \n        `)}\n      </div>  \n    `)}\n  `;\n}\n/* eslint-enable indent */\n", "// Takes a value as input and provides a printable string to replresent null values, spaces, blankstring etc\nexport function getPrintableVal(val) {\n  if (val === undefined) {\n    return '';\n  }\n  if (val === null) {\n    return 'null';\n  }\n  if (val === '') {\n    return '∅';\n  }\n  if (typeof val === 'boolean' || typeof val === 'number') {\n    return `${val}`;\n  }\n  if (Array.isArray(val)) {\n    return val.map((v) => (v === null ? 'null' : v === '' ? '∅' : v.toString().replace(/^ +| +$/g, (m) => '●'.repeat(m.length)) || '')).join(', ');\n  }\n  return val.toString().replace(/^ +| +$/g, (m) => '●'.repeat(m.length)) || '';\n}\n\n/* Generates an schema object containing type and constraint info */\nexport function getTypeInfo(schema) {\n  if (!schema) {\n    return;\n  }\n  let dataType = '';\n  let constrain = '';\n  // let examples;\n\n  if (schema.$ref) {\n    const n = schema.$ref.lastIndexOf('/');\n    const schemaNode = schema.$ref.substring(n + 1);\n    dataType = `{recursive: ${schemaNode}} `;\n  } else if (schema.type) {\n    dataType = Array.isArray(schema.type) ? schema.type.join(schema.length === 2 ? ' or ' : '┃') : schema.type;\n    if (schema.format || schema.enum || schema.const) {\n      dataType = dataType.replace('string', schema.enum ? 'enum' : schema.const ? 'const' : schema.format);\n    }\n    if (schema.nullable) {\n      dataType += '┃null';\n    }\n  } else if (schema.const) {\n    dataType = 'const';\n  } else if (Object.keys(schema).length === 0) {\n    dataType = 'any';\n  } else {\n    dataType = '{missing-type-info}';\n  }\n\n  const info = {\n    type: dataType,\n    format: schema.format || '',\n    pattern: (schema.pattern && !schema.enum) ? schema.pattern : '',\n    readOrWriteOnly: (schema.readOnly ? '🆁' : schema.writeOnly ? '🆆' : ''),\n    deprecated: schema.deprecated ? '❌' : '',\n    examples: schema.examples || schema.example,\n    default: getPrintableVal(schema.default),\n    description: schema.description || '',\n    constrain: '',\n    allowedValues: '',\n    arrayType: '',\n    html: '',\n  };\n\n  if (info.type === '{recursive}') {\n    info.description = schema.$ref.substring(schema.$ref.lastIndexOf('/') + 1);\n  } else if (info.type === '{missing-type-info}' || info.type === 'any') {\n    info.description = info.description || '';\n  }\n  // Set Allowed Values\n  info.allowedValues = schema.const\n    ? schema.const\n    : Array.isArray(schema.enum)\n      ? schema.enum.map((v) => (getPrintableVal(v))).join('┃')\n      : '';\n\n  if (dataType === 'array' && schema.items) {\n    const arrayItemType = schema.items?.type;\n    const arrayItemDefault = getPrintableVal(schema.items.default);\n\n    info.arrayType = `${schema.type} of ${Array.isArray(arrayItemType) ? arrayItemType.join('') : arrayItemType}`;\n    info.default = arrayItemDefault;\n    info.allowedValues = schema.items.const\n      ? schema.const\n      : Array.isArray(schema.items?.enum)\n        ? schema.items.enum.map((v) => (getPrintableVal(v))).join('┃')\n        : '';\n  }\n  if (dataType.match(/integer|number/g)) {\n    if (schema.minimum !== undefined || schema.exclusiveMinimum !== undefined) {\n      constrain += schema.minimum !== undefined ? `Min ${schema.minimum}` : `More than ${schema.exclusiveMinimum}`;\n    }\n    if (schema.maximum !== undefined || schema.exclusiveMaximum !== undefined) {\n      constrain += schema.maximum !== undefined ? `${constrain ? '┃' : ''}Max ${schema.maximum}` : `${constrain ? '┃' : ''}Less than ${schema.exclusiveMaximum}`;\n    }\n    if (schema.multipleOf !== undefined) {\n      constrain += `${constrain ? '┃' : ''} multiple of ${schema.multipleOf}`;\n    }\n  }\n  if (dataType.match(/string/g)) {\n    if (schema.minLength !== undefined && schema.maxLength !== undefined) {\n      constrain += `${constrain ? '┃' : ''}${schema.minLength} to ${schema.maxLength} chars`;\n    } else if (schema.minLength !== undefined) {\n      constrain += `${constrain ? '┃' : ''}Min ${schema.minLength} chars`;\n    } else if (schema.maxLength !== undefined) {\n      constrain += `Max ${constrain ? '┃' : ''}${schema.maxLength} chars`;\n    }\n  }\n  info.constrain = constrain;\n  info.html = `${info.type}~|~${info.readOrWriteOnly}~|~${info.constrain}~|~${info.default}~|~${info.allowedValues}~|~${info.pattern}~|~${info.description}~|~${schema.title || ''}~|~${info.deprecated ? 'deprecated' : ''}`;\n  return info;\n}\n\nexport function nestExampleIfPresent(example) {\n  if (typeof example === 'boolean' || typeof example === 'number') {\n    return {\n      Example: { value: `${example}` },\n    };\n  }\n  if (example === '') {\n    return {\n      Example: { value: '' },\n    };\n  }\n  return example ? { Example: { value: example } } : example;\n}\n\n/**\n *  Normalize example object in the following format (List of object which is used to render example links and fill the input boxes)\n *  [{\n *     exampleVal  : 'value to be rendered on the input control (text-box)',\n *     exampleList : [\n *       value         : '',\n *       printableValue: '',\n *       summary       : '',\n *       description   : ''\n *     ]\n *  }]\n * */\nexport function normalizeExamples(examples, dataType = 'string') {\n  if (!examples) {\n    return {\n      exampleVal: '',\n      exampleList: [],\n    };\n  }\n  if (examples.constructor === Object) {\n    const exampleList = Object.values(examples)\n      .filter((v) => (v['x-example-show-value'] !== false))\n      .map((v) => ({\n        value: (typeof v.value === 'boolean' || typeof v.value === 'number' ? `${v.value}` : (v.value || '')),\n        printableValue: getPrintableVal(v.value),\n        summary: v.summary || '',\n        description: v.description || '',\n      }));\n    const exampleVal = exampleList.length > 0\n      ? exampleList[0].value.toString()\n      : '';\n    return { exampleVal, exampleList };\n  }\n\n  // This is non-standard way to provide example but will support for now\n  if (!Array.isArray(examples)) {\n    examples = examples ? [examples] : [];\n  }\n\n  if (examples.length === 0) {\n    return {\n      exampleVal: '',\n      exampleList: [],\n    };\n  }\n\n  if (dataType === 'array') {\n    const [exampleVal] = examples;\n    const exampleList = examples.map((v) => ({\n      value: v,\n      printableValue: getPrintableVal(v),\n    }));\n    return { exampleVal, exampleList };\n  }\n\n  const exampleVal = examples[0].toString();\n  const exampleList = examples.map((v) => ({\n    value: v.toString(),\n    printableValue: getPrintableVal(v),\n  }));\n  return { exampleVal, exampleList };\n}\n\nexport function anyExampleWithSummaryOrDescription(examples) {\n  return examples.some((x) => x.summary?.length > 0 || x.description?.length > 0);\n}\n\nexport function getSampleValueByType(schemaObj) {\n  const example = schemaObj.examples\n    ? schemaObj.examples[0]\n    : schemaObj.example === null\n      ? null\n      : schemaObj.example || undefined;\n  if (example === '') { return ''; }\n  if (example === null) { return null; }\n  if (example === 0) { return 0; }\n  if (example === false) { return false; }\n  if (example instanceof Date) {\n    switch (schemaObj.format.toLowerCase()) {\n      case 'date':\n        return example.toISOString().split('T')[0];\n      case 'time':\n        return example.toISOString().split('T')[1];\n      default:\n        return example.toISOString();\n    }\n  }\n  if (example) { return example; }\n\n  if (Object.keys(schemaObj).length === 0) {\n    return null;\n  }\n  if (schemaObj.$ref) {\n    // Indicates a Circular ref\n    return schemaObj.$ref;\n  }\n  if (schemaObj.const === false || schemaObj.const === 0 || schemaObj.const === null || schemaObj.const === '') {\n    return schemaObj.const;\n  }\n  if (schemaObj.const) {\n    return schemaObj.const;\n  }\n  const typeValue = Array.isArray(schemaObj.type) ? schemaObj.type[0] : schemaObj.type;\n  if (!typeValue) {\n    return '?';\n  }\n  if (typeValue.match(/^integer|^number/g)) {\n    const multipleOf = Number.isNaN(Number(schemaObj.multipleOf)) ? undefined : Number(schemaObj.multipleOf);\n    const maximum = Number.isNaN(Number(schemaObj.maximum)) ? undefined : Number(schemaObj.maximum);\n    const minimumPossibleVal = Number.isNaN(Number(schemaObj.minimum))\n      ? Number.isNaN(Number(schemaObj.exclusiveMinimum))\n        ? maximum || 0\n        : Number(schemaObj.exclusiveMinimum) + (typeValue.startsWith('integer') ? 1 : 0.001)\n      : Number(schemaObj.minimum);\n    const finalVal = multipleOf\n      ? multipleOf >= minimumPossibleVal\n        ? multipleOf\n        : minimumPossibleVal % multipleOf === 0\n          ? minimumPossibleVal\n          : Math.ceil(minimumPossibleVal / multipleOf) * multipleOf\n      : minimumPossibleVal;\n    return finalVal;\n  }\n  if (typeValue.match(/^boolean/g)) { return false; }\n  if (typeValue.match(/^null/g)) { return null; }\n  if (typeValue.match(/^string/g)) {\n    if (schemaObj.enum) { return schemaObj.enum[0]; }\n    if (schemaObj.const) { return schemaObj.const; }\n    if (schemaObj.pattern) { return schemaObj.pattern; }\n    if (schemaObj.format) {\n      const u = `${Date.now().toString(16)}${Math.random().toString(16)}0`.repeat(16);\n      switch (schemaObj.format.toLowerCase()) {\n        case 'url':\n        case 'uri':\n          return 'http://example.com';\n        case 'date':\n          return (new Date(0)).toISOString().split('T')[0];\n        case 'time':\n          return (new Date(0)).toISOString().split('T')[1];\n        case 'date-time':\n          return (new Date(0)).toISOString();\n        case 'duration':\n          return 'P3Y6M4DT12H30M5S'; // P=Period 3-Years 6-Months 4-Days 12-Hours 30-Minutes 5-Seconds\n        case 'email':\n        case 'idn-email':\n          return '<EMAIL>';\n        case 'hostname':\n        case 'idn-hostname':\n          return 'www.example.com';\n        case 'ipv4':\n          return '*************';\n        case 'ipv6':\n          return '2001:0db8:5b96:0000:0000:426f:8e17:642a';\n        case 'uuid':\n          return [u.substr(0, 8), u.substr(8, 4), `4000-8${u.substr(13, 3)}`, u.substr(16, 12)].join('-');\n        default:\n          return '';\n      }\n    } else {\n      const minLength = Number.isNaN(schemaObj.minLength) ? undefined : Number(schemaObj.minLength);\n      const maxLength = Number.isNaN(schemaObj.maxLength) ? undefined : Number(schemaObj.maxLength);\n      const finalLength = minLength || (maxLength > 6 ? 6 : maxLength || undefined);\n      return finalLength ? 'A'.repeat(finalLength) : 'string';\n    }\n  }\n  // If type cannot be determined\n  return '?';\n}\n\n/*\njson2xml- TestCase\n  {\n    'prop1' : 'one',\n    'prop2' : 'two',\n    'prop3' : [ 'a', 'b', 'c' ],\n    'prop4' : {\n      'ob1' : 'val-1',\n      'ob2' : 'val-2'\n    }\n  }\n  <root>\n    <prop1>simple</prop1>\n    <prop2>\n      <0>a</0>\n      <1>b</1>\n      <2>c</2>\n    </prop2>\n    <prop3>\n      <ob1>val-1</ob1>\n      <ob2>val-2</ob2>\n    </prop3>\n  </root>\n*/\nexport function json2xml(obj, level = 1) {\n  const indent = '  '.repeat(level);\n  let xmlText = '';\n  if (level === 1 && typeof obj !== 'object') {\n    return `\\n${indent}${obj.toString()}`;\n  }\n  for (const prop in obj) {\n    const tagNameOrProp = (obj[prop]['::XML_TAG'] || prop);\n    let tagName = '';\n    if (Array.isArray(obj[prop])) {\n      tagName = tagNameOrProp[0]['::XML_TAG'] || `${prop}`;\n    } else {\n      tagName = tagNameOrProp;\n    }\n    if (prop.startsWith('::')) {\n      continue;\n    }\n    if (Array.isArray(obj[prop])) {\n      xmlText = `${xmlText}\\n${indent}<${tagName}>${json2xml(obj[prop], level + 1)}\\n${indent}</${tagName}>`;\n    } else if (typeof obj[prop] === 'object') {\n      xmlText = `${xmlText}\\n${indent}<${tagName}>${json2xml(obj[prop], level + 1)}\\n${indent}</${tagName}>`;\n    } else {\n      xmlText = `${xmlText}\\n${indent}<${tagName}>${obj[prop].toString()}</${tagName}>`;\n    }\n  }\n  return xmlText;\n}\n\nfunction addSchemaInfoToExample(schema, obj) {\n  if (typeof obj !== 'object' || obj === null) {\n    return;\n  }\n  if (schema.title) {\n    obj['::TITLE'] = schema.title;\n  }\n  if (schema.description) {\n    obj['::DESCRIPTION'] = schema.description;\n  }\n  if (schema.xml?.name) {\n    obj['::XML_TAG'] = schema.xml?.name;\n  }\n  if (schema.xml?.wrapped) {\n    obj['::XML_WRAP'] = schema.xml?.wrapped.toString();\n  }\n}\n\nfunction removeTitlesAndDescriptions(obj) {\n  if (typeof obj !== 'object' || obj === null) {\n    return;\n  }\n  delete obj['::TITLE'];\n  delete obj['::DESCRIPTION'];\n  delete obj['::XML_TAG'];\n  delete obj['::XML_WRAP'];\n  for (const k in obj) {\n    removeTitlesAndDescriptions(obj[k]);\n  }\n}\n\nfunction addPropertyExampleToObjectExamples(example, obj, propertyKey) {\n  for (const key in obj) {\n    obj[key][propertyKey] = example;\n  }\n}\n\nfunction mergePropertyExamples(obj, propertyName, propExamples) {\n  // Create an example for each variant of the propertyExample, merging them with the current (parent) example\n  let i = 0;\n  const maxCombinations = 10;\n  const mergedObj = {};\n  for (const exampleKey in obj) {\n    for (const propExampleKey in propExamples) {\n      mergedObj[`example-${i}`] = { ...obj[exampleKey] };\n      mergedObj[`example-${i}`][propertyName] = propExamples[propExampleKey];\n      i++;\n      if (i >= maxCombinations) {\n        break;\n      }\n    }\n    if (i >= maxCombinations) {\n      break;\n    }\n  }\n  return mergedObj;\n}\n\n/* For changing JSON-Schema to a Sample Object, as per the schema (to generate examples based on schema) */\nexport function schemaToSampleObj(schema, config = { }) {\n  let obj = {};\n  if (!schema) {\n    return;\n  }\n  if (schema.allOf) {\n    const objWithAllProps = {};\n\n    if (schema.allOf.length === 1 && !schema.allOf[0]?.properties && !schema.allOf[0]?.items) {\n      // If allOf has single item and the type is not an object or array, then its a primitive\n      if (schema.allOf[0].$ref) {\n        return '{  }';\n      }\n      if (schema.allOf[0].readOnly && config.includeReadOnly) {\n        const tempSchema = schema.allOf[0];\n        return getSampleValueByType(tempSchema);\n      }\n      return;\n    }\n\n    schema.allOf.forEach((v) => {\n      if (v.type === 'object' || v.properties || v.allOf || v.anyOf || v.oneOf) {\n        const partialObj = schemaToSampleObj(v, config);\n        Object.assign(objWithAllProps, partialObj);\n      } else if (v.type === 'array' || v.items) {\n        const partialObj = [schemaToSampleObj(v, config)];\n        Object.assign(objWithAllProps, partialObj);\n      } else if (v.type) {\n        const prop = `prop${Object.keys(objWithAllProps).length}`;\n        objWithAllProps[prop] = getSampleValueByType(v);\n      } else {\n        return '';\n      }\n    });\n\n    obj = objWithAllProps;\n  } else if (schema.oneOf) {\n    // 1. First create example with scheme.properties\n    const objWithSchemaProps = {};\n    if (schema.properties) {\n      for (const propertyName in schema.properties) {\n        if (schema.properties[propertyName].properties || schema.properties[propertyName].properties?.items) {\n          objWithSchemaProps[propertyName] = schemaToSampleObj(schema.properties[propertyName], config);\n        } else {\n          objWithSchemaProps[propertyName] = getSampleValueByType(schema.properties[propertyName]);\n        }\n      }\n    }\n\n    if (schema.oneOf.length > 0) {\n      /*\n      oneOf:\n        - type: object\n          properties:\n            option1_PropA:\n              type: string\n            option1_PropB:\n              type: string\n        - type: object\n          properties:\n            option2_PropX:\n              type: string\n      properties:\n        prop1:\n          type: string\n        prop2:\n          type: string\n          minLength: 10\n\n      The aboove Schem should generate the following 2 examples\n\n      Example-1\n      {\n        prop1: 'string',\n        prop2: 'AAAAAAAAAA',       <-- min-length 10\n        option1_PropA: 'string',\n        option1_PropB: 'string'\n      }\n\n      Example-2\n      {\n        prop1: 'string',\n        prop2: 'AAAAAAAAAA',       <-- min-length 10\n        option2_PropX: 'string'\n      }\n      */\n      let i = 0;\n      // Merge all examples of each oneOf-schema\n      for (const key in schema.oneOf) {\n        const oneOfSamples = schemaToSampleObj(schema.oneOf[key], config);\n        for (const sampleKey in oneOfSamples) {\n          // 2. In the final example include a one-of item along with properties\n          let finalExample;\n          if (Object.keys(objWithSchemaProps).length > 0) {\n            if (oneOfSamples[sampleKey] === null || typeof oneOfSamples[sampleKey] !== 'object') {\n              // This doesn't really make sense since every oneOf schema _should_ be an object if there are common properties, so we'll skip this\n              continue;\n            } else {\n              finalExample = Object.assign(oneOfSamples[sampleKey], objWithSchemaProps);\n            }\n          } else {\n            finalExample = oneOfSamples[sampleKey];\n          }\n          obj[`example-${i}`] = finalExample;\n          addSchemaInfoToExample(schema.oneOf[key], obj[`example-${i}`]);\n          i++;\n        }\n      }\n    }\n  } else if (schema.anyOf) {\n    // First generate values for regular properties\n    let commonObj;\n    if (schema.type === 'object' || schema.properties) {\n      commonObj = { 'example-0': {} };\n      for (const propertyName in schema.properties) {\n        if (schema.example) {\n          commonObj = schema;\n          break;\n        }\n        if (schema.properties[propertyName].deprecated && !config.includeDeprecated) { continue; }\n        if (schema.properties[propertyName].readOnly && !config.includeReadOnly) { continue; }\n        if (schema.properties[propertyName].writeOnly && !config.includeWriteOnly) { continue; }\n        commonObj = mergePropertyExamples(commonObj, propertyName, schemaToSampleObj(schema.properties[propertyName], config));\n      }\n    }\n\n    // Combine every variant of the regular properties with every variant of the anyOf samples\n    let i = 0;\n    for (const key in schema.anyOf) {\n      const anyOfSamples = schemaToSampleObj(schema.anyOf[key], config);\n      for (const sampleKey in anyOfSamples) {\n        if (typeof commonObj !== 'undefined') {\n          for (const commonKey in commonObj) {\n            obj[`example-${i}`] = { ...commonObj[commonKey], ...anyOfSamples[sampleKey] };\n          }\n        } else {\n          obj[`example-${i}`] = anyOfSamples[sampleKey];\n        }\n        addSchemaInfoToExample(schema.anyOf[key], obj[`example-${i}`]);\n        i++;\n      }\n    }\n  } else if (schema.type === 'object' || schema.properties) {\n    obj['example-0'] = {};\n    addSchemaInfoToExample(schema, obj['example-0']);\n    if (schema.example) {\n      obj['example-0'] = schema.example;\n    } else {\n      for (const propertyName in schema.properties) {\n        if (schema.properties[propertyName]?.deprecated && !config.includeDeprecated) { continue; }\n        if (schema.properties[propertyName]?.readOnly && !config.includeReadOnly) { continue; }\n        if (schema.properties[propertyName]?.writeOnly && !config.includeWriteOnly) { continue; }\n        if (schema.properties[propertyName]?.type === 'array' || schema.properties[propertyName]?.items) {\n          if (schema.properties[propertyName].example) {\n            addPropertyExampleToObjectExamples(schema.properties[propertyName].example, obj, propertyName);\n          } else if (schema.properties[propertyName]?.items?.example) { // schemas and properties support single example but not multiple examples.\n            addPropertyExampleToObjectExamples([schema.properties[propertyName].items.example], obj, propertyName);\n          } else {\n            const itemSamples = schemaToSampleObj(schema.properties[propertyName].items, config);\n            if (config.useXmlTagForProp) {\n              const xmlTagName = schema.properties[propertyName].xml?.name || propertyName;\n              if (schema.properties[propertyName].xml?.wrapped) {\n                const wrappedItemSample = JSON.parse(`{ \"${xmlTagName}\" : { \"${xmlTagName}\" : ${JSON.stringify(itemSamples['example-0'])} } }`);\n                obj = mergePropertyExamples(obj, xmlTagName, wrappedItemSample);\n              } else {\n                obj = mergePropertyExamples(obj, xmlTagName, itemSamples);\n              }\n            } else {\n              const arraySamples = [];\n              for (const key in itemSamples) {\n                arraySamples[key] = [itemSamples[key]];\n              }\n              obj = mergePropertyExamples(obj, propertyName, arraySamples);\n            }\n          }\n          continue;\n        }\n        obj = mergePropertyExamples(obj, propertyName, schemaToSampleObj(schema.properties[propertyName], config));\n      }\n    }\n  } else if (schema.type === 'array' || schema.items) {\n    if (schema.items || schema.example) {\n      if (schema.example) {\n        obj['example-0'] = schema.example;\n      } else if (schema.items?.example) { // schemas and properties support single example but not multiple examples.\n        obj['example-0'] = [schema.items.example];\n      } else {\n        const samples = schemaToSampleObj(schema.items, config);\n        let i = 0;\n        for (const key in samples) {\n          obj[`example-${i}`] = [samples[key]];\n          addSchemaInfoToExample(schema.items, obj[`example-${i}`]);\n          i++;\n        }\n      }\n    } else {\n      obj['example-0'] = [];\n    }\n  } else {\n    return { 'example-0': getSampleValueByType(schema) };\n  }\n  return obj;\n}\n\nfunction generateMarkdownForArrayAndObjectDescription(schema, level = 0) {\n  let markdown = ((schema.description || schema.title) && (schema.minItems || schema.maxItems)) ? '<span class=\"descr-expand-toggle\">➔</span>' : '';\n  if (schema.title) {\n    if (schema.description) {\n      markdown = `${markdown} <b>${schema.title}:</b> ${schema.description}<br/>`;\n    } else {\n      markdown = `${markdown} ${schema.title}<br/>`;\n    }\n  } else if (schema.description) {\n    markdown = `${markdown} ${schema.description}<br/>`;\n  }\n  if (schema.minItems) {\n    markdown = `${markdown} <b>Min Items:</b> ${schema.minItems}`;\n  }\n  if (schema.maxItems) {\n    markdown = `${markdown} <b>Max Items:</b> ${schema.maxItems}`;\n  }\n  if (level > 0 && schema.items?.description) {\n    let itemsMarkdown = '';\n    if (schema.items.minProperties) {\n      itemsMarkdown = `<b>Min Properties:</b> ${schema.items.minProperties}`;\n    }\n    if (schema.items.maxProperties) {\n      itemsMarkdown = `${itemsMarkdown} <b>Max Properties:</b> ${schema.items.maxProperties}`;\n    }\n    markdown = `${markdown} ⮕ ${itemsMarkdown} [ ${schema.items.description} ] `;\n  }\n  return markdown;\n}\n/**\n * For changing OpenAPI-Schema to an Object Notation,\n * This Object would further be an input to UI Components to generate an Object-Tree\n * @param {object} schema - Schema object from OpenAPI spec\n * @param {object} obj - recursivly pass this object to generate object notation\n * @param {number} level - recursion level\n * @param {string} suffix - used for suffixing property names to avoid duplicate props during object composion\n */\nexport function schemaInObjectNotation(schema, obj, level = 0, suffix = '') {\n  if (!schema) {\n    return;\n  }\n  if (schema.allOf) {\n    const objWithAllProps = {};\n    if (schema.allOf.length === 1 && !schema.allOf[0].properties && !schema.allOf[0].items) {\n      // If allOf has single item and the type is not an object or array, then its a primitive\n      const tempSchema = schema.allOf[0];\n      return `${getTypeInfo(tempSchema).html}`;\n    }\n    // If allOf is an array of multiple elements, then all the keys makes a single object\n    schema.allOf.map((v, i) => {\n      if (v.type === 'object' || v.properties || v.allOf || v.anyOf || v.oneOf) {\n        const propSuffix = (v.anyOf || v.oneOf) && i > 0 ? i : '';\n        const partialObj = schemaInObjectNotation(v, {}, (level + 1), propSuffix);\n        Object.assign(objWithAllProps, partialObj);\n      } else if (v.type === 'array' || v.items) {\n        const partialObj = schemaInObjectNotation(v, {}, (level + 1));\n        Object.assign(objWithAllProps, partialObj);\n      } else if (v.type) {\n        const prop = `prop${Object.keys(objWithAllProps).length}`;\n        const typeObj = getTypeInfo(v);\n        objWithAllProps[prop] = `${typeObj.html}`;\n      } else {\n        return '';\n      }\n    });\n    obj = objWithAllProps;\n  } else if (schema.anyOf || schema.oneOf) {\n    obj['::description'] = schema.description || '';\n    // 1. First iterate the regular properties\n    if (schema.type === 'object' || schema.properties) {\n      obj['::description'] = schema.description || '';\n      obj['::type'] = 'object';\n      // obj['::deprecated'] = schema.deprecated || false;\n      for (const key in schema.properties) {\n        if (schema.required && schema.required.includes(key)) {\n          obj[`${key}*`] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n        } else {\n          obj[key] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n        }\n      }\n    }\n    // 2. Then show allof/anyof objects\n    const objWithAnyOfProps = {};\n    const xxxOf = schema.anyOf ? 'anyOf' : 'oneOf';\n    schema[xxxOf].forEach((v, index) => {\n      if (v.type === 'object' || v.properties || v.allOf || v.anyOf || v.oneOf) {\n        const partialObj = schemaInObjectNotation(v, {});\n        objWithAnyOfProps[`::OPTION~${index + 1}${v.title ? `~${v.title}` : ''}`] = partialObj;\n        objWithAnyOfProps[`::OPTION~${index + 1}${v.title ? `~${v.title}` : ''}`]['::readwrite'] = ''; // xxx-options cannot be read or write only\n        objWithAnyOfProps['::type'] = 'xxx-of-option';\n      } else if (v.type === 'array' || v.items) {\n        // This else-if block never seems to get executed\n        const partialObj = schemaInObjectNotation(v, {});\n        objWithAnyOfProps[`::OPTION~${index + 1}${v.title ? `~${v.title}` : ''}`] = partialObj;\n        objWithAnyOfProps[`::OPTION~${index + 1}${v.title ? `~${v.title}` : ''}`]['::readwrite'] = ''; // xxx-options cannot be read or write only\n        objWithAnyOfProps['::type'] = 'xxx-of-array';\n      } else {\n        const prop = `::OPTION~${index + 1}${v.title ? `~${v.title}` : ''}`;\n        objWithAnyOfProps[prop] = `${getTypeInfo(v).html}`;\n        objWithAnyOfProps['::type'] = 'xxx-of-option';\n      }\n    });\n    obj[(schema.anyOf ? `::ANY~OF ${suffix}` : `::ONE~OF ${suffix}`)] = objWithAnyOfProps;\n    // obj['::type'] = 'object';\n    obj['::type'] = 'object';\n  } else if (Array.isArray(schema.type)) {\n    // When a property has multiple types, then check further if any of the types are array or object, if yes then modify the schema using one-of\n    // Clone the schema - as it will be modified to replace multi-data-types with one-of;\n    const subSchema = JSON.parse(JSON.stringify(schema));\n    const primitiveType = [];\n    const complexTypes = [];\n    subSchema.type.forEach((v) => {\n      if (v.match(/integer|number|string|null|boolean/g)) {\n        primitiveType.push(v);\n      } else if (v === 'array' && typeof subSchema.items?.type === 'string' && subSchema.items?.type.match(/integer|number|string|null|boolean/g)) {\n        // Array with primitive types should also be treated as primitive type\n        if (subSchema.items.type === 'string' && subSchema.items.format) {\n          primitiveType.push(`[${subSchema.items.format}]`);\n        } else {\n          primitiveType.push(`[${subSchema.items.type}]`);\n        }\n      } else {\n        complexTypes.push(v);\n      }\n    });\n    let multiPrimitiveTypes;\n    if (primitiveType.length > 0) {\n      subSchema.type = primitiveType.join(primitiveType.length === 2 ? ' or ' : '┃');\n      multiPrimitiveTypes = getTypeInfo(subSchema);\n      if (complexTypes.length === 0) {\n        return `${multiPrimitiveTypes?.html || ''}`;\n      }\n    }\n    if (complexTypes.length > 0) {\n      obj['::type'] = 'object';\n      const multiTypeOptions = {\n        '::type': 'xxx-of-option',\n      };\n\n      // Generate ONE-OF options for complexTypes\n      complexTypes.forEach((v, i) => {\n        if (v === 'null') {\n          multiTypeOptions[`::OPTION~${i + 1}`] = 'NULL~|~~|~~|~~|~~|~~|~~|~~|~';\n        } else if ('integer, number, string, boolean,'.includes(`${v},`)) {\n          subSchema.type = Array.isArray(v) ? v.join('┃') : v;\n          const primitiveTypeInfo = getTypeInfo(subSchema);\n          multiTypeOptions[`::OPTION~${i + 1}`] = primitiveTypeInfo.html;\n        } else if (v === 'object') {\n          // If object type iterate all the properties and create an object-type-option\n          const objTypeOption = {\n            '::title': schema.title || '',\n            '::description': schema.description || '',\n            '::type': 'object',\n            '::deprecated': schema.deprecated || false,\n          };\n          for (const key in schema.properties) {\n            if (schema.required && schema.required.includes(key)) {\n              objTypeOption[`${key}*`] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n            } else {\n              objTypeOption[key] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n            }\n          }\n          multiTypeOptions[`::OPTION~${i + 1}`] = objTypeOption;\n        } else if (v === 'array') {\n          multiTypeOptions[`::OPTION~${i + 1}`] = {\n            '::title': schema.title || '',\n            '::description': schema.description || '',\n            '::type': 'array',\n            '::props': schemaInObjectNotation(schema.items, {}, (level + 1)),\n          };\n        }\n      });\n      multiTypeOptions[`::OPTION~${complexTypes.length + 1}`] = multiPrimitiveTypes?.html || '';\n      obj['::ONE~OF'] = multiTypeOptions;\n    }\n  } else if (schema.type === 'object' || schema.properties) { // If Object\n    obj['::title'] = schema.title || '';\n    obj['::description'] = generateMarkdownForArrayAndObjectDescription(schema, level);\n    obj['::type'] = 'object';\n    if ((Array.isArray(schema.type) && schema.type.includes('null')) || schema.nullable) {\n      obj['::dataTypeLabel'] = 'object or null';\n    }\n    obj['::deprecated'] = schema.deprecated || false;\n    obj['::readwrite'] = schema.readOnly ? 'readonly' : schema.writeOnly ? 'writeonly' : '';\n    for (const key in schema.properties) {\n      if (schema.required && schema.required.includes(key)) {\n        obj[`${key}*`] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n      } else {\n        obj[key] = schemaInObjectNotation(schema.properties[key], {}, (level + 1));\n      }\n    }\n    for (const key in schema.patternProperties) {\n      obj[`[pattern: ${key}]`] = schemaInObjectNotation(schema.patternProperties[key], obj, (level + 1));\n    }\n    if (schema.additionalProperties) {\n      obj['[any-key]'] = schemaInObjectNotation(schema.additionalProperties, {});\n    }\n  } else if (schema.type === 'array' || schema.items) { // If Array\n    obj['::title'] = schema.title || '';\n    obj['::description'] = generateMarkdownForArrayAndObjectDescription(schema, level);\n    obj['::type'] = 'array';\n    if ((Array.isArray(schema.type) && schema.type.includes('null')) || schema.nullable) {\n      obj['::dataTypeLabel'] = 'array or null';\n    }\n    obj['::deprecated'] = schema.deprecated || false;\n    obj['::readwrite'] = schema.readOnly ? 'readonly' : schema.writeOnly ? 'writeonly' : '';\n    if (schema.items?.items) {\n      obj['::array-type'] = schema.items.items.type;\n    }\n    obj['::props'] = schemaInObjectNotation(schema.items, {}, (level + 1));\n  } else {\n    const typeObj = getTypeInfo(schema);\n    if (typeObj?.html) {\n      return `${typeObj.html}`;\n    }\n    return '';\n  }\n  return obj;\n}\n\n/* Create Example object */\nexport function generateExample(schema, mimeType, examples = '', example = '', includeReadOnly = true, includeWriteOnly = true, outputType = 'json', includeGeneratedExample = false) {\n  const finalExamples = [];\n  // First check if examples is provided\n  if (examples) {\n    for (const eg in examples) {\n      let egContent = '';\n      let egFormat = 'json';\n      if (mimeType?.toLowerCase().includes('json')) {\n        if (outputType === 'text') {\n          egContent = typeof examples[eg].value === 'string' ? examples[eg].value : JSON.stringify(examples[eg].value, undefined, 2);\n          egFormat = 'text';\n        } else {\n          egContent = examples[eg].value;\n          if (typeof examples[eg].value === 'string') {\n            try {\n              // const fixedJsonString = examples[eg].value.replace((/([\\w]+)(:)/g), '\"$1\"$2').replace((/'/g), '\"');\n              const fixedJsonString = examples[eg].value;\n              egContent = JSON.parse(fixedJsonString);\n              egFormat = 'json';\n            } catch (err) {\n              egFormat = 'text';\n              egContent = examples[eg].value;\n            }\n          }\n        }\n      } else {\n        egContent = examples[eg].value;\n        egFormat = 'text';\n      }\n\n      finalExamples.push({\n        exampleId: eg,\n        exampleSummary: examples[eg].summary || eg,\n        exampleDescription: examples[eg].description || '',\n        exampleType: mimeType,\n        exampleValue: egContent,\n        exampleFormat: egFormat,\n      });\n    }\n  } else if (example) {\n    let egContent = '';\n    let egFormat = 'json';\n    if (mimeType?.toLowerCase().includes('json')) {\n      if (outputType === 'text') {\n        egContent = typeof example === 'string' ? example : JSON.stringify(example, undefined, 2);\n        egFormat = 'text';\n      } else if (typeof example === 'object') {\n        egContent = example;\n        egFormat = 'json';\n      } else if (typeof example === 'string') {\n        try {\n          egContent = JSON.parse(example);\n          egFormat = 'json';\n        } catch (err) {\n          egFormat = 'text';\n          egContent = example;\n        }\n      }\n    } else {\n      egContent = example;\n      egFormat = 'text';\n    }\n    finalExamples.push({\n      exampleId: 'Example',\n      exampleSummary: '',\n      exampleDescription: '',\n      exampleType: mimeType,\n      exampleValue: egContent,\n      exampleFormat: egFormat,\n    });\n  }\n  // If schema-level examples are not provided or includeGeneratedExample === true then generate one based on the schema field types\n  if (finalExamples.length === 0 || includeGeneratedExample === true) {\n    if (schema) {\n      if (schema.example) {\n        // Note: Deprecated: The 'example' property has been deprecated in 3.1.0 in favor of the JSON Schema 'examples' keyword\n        finalExamples.push({\n          exampleId: 'Example',\n          exampleSummary: '',\n          exampleDescription: '',\n          exampleType: mimeType,\n          exampleValue: schema.example,\n          exampleFormat: ((mimeType?.toLowerCase().includes('json') && typeof schema.example === 'object') ? 'json' : 'text'),\n        });\n      } else if (mimeType?.toLowerCase().includes('json') || mimeType?.toLowerCase().includes('text') || mimeType?.toLowerCase().includes('*/*') || mimeType?.toLowerCase().includes('xml')) {\n        let xmlRootStart = '';\n        let xmlRootEnd = '';\n        let exampleFormat = '';\n        let exampleValue = '';\n        if (mimeType?.toLowerCase().includes('xml')) {\n          xmlRootStart = schema.xml?.name ? `<${schema.xml.name} ${schema.xml.namespace ? `xmlns=\"${schema.xml.namespace}\"` : ''}>` : '<root>';\n          xmlRootEnd = schema.xml?.name ? `</${schema.xml.name}>` : '</root>';\n          exampleFormat = 'text';\n        } else {\n          exampleFormat = outputType;\n        }\n        const samples = schemaToSampleObj(schema, { includeReadOnly, includeWriteOnly, deprecated: true, useXmlTagForProp: mimeType?.toLowerCase().includes('xml') });\n        let i = 0;\n        for (const samplesKey in samples) {\n          if (!samples[samplesKey]) {\n            continue;\n          }\n          const summary = samples[samplesKey]['::TITLE'] || `Example ${++i}`;\n          const description = samples[samplesKey]['::DESCRIPTION'] || '';\n          if (mimeType?.toLowerCase().includes('xml')) {\n            exampleValue = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\\n${xmlRootStart}${json2xml(samples[samplesKey], 1)}\\n${xmlRootEnd}`;\n          } else {\n            removeTitlesAndDescriptions(samples[samplesKey]);\n            exampleValue = outputType === 'text' ? JSON.stringify(samples[samplesKey], null, 2) : samples[samplesKey];\n          }\n\n          finalExamples.push({\n            exampleId: samplesKey,\n            exampleSummary: summary,\n            exampleDescription: description,\n            exampleType: mimeType,\n            exampleFormat,\n            exampleValue,\n          });\n        }\n      } else if (mimeType?.toLowerCase().includes('jose')) {\n        finalExamples.push({\n          exampleId: 'Example',\n          exampleSummary: 'Base64 Encoded',\n          exampleDescription: '',\n          exampleType: mimeType,\n          exampleValue: schema.pattern || 'bXJpbg==',\n          exampleFormat: 'text',\n        });\n      } else {\n        finalExamples.push({\n          exampleId: 'Example',\n          exampleSummary: '',\n          exampleDescription: '',\n          exampleType: mimeType,\n          exampleValue: '',\n          exampleFormat: 'text',\n        });\n      }\n    } else {\n      // No Example or Schema provided (should never reach here)\n      finalExamples.push({\n        exampleId: 'Example',\n        exampleSummary: '',\n        exampleDescription: '',\n        exampleType: mimeType,\n        exampleValue: '',\n        exampleFormat: 'text',\n      });\n    }\n  }\n  return finalExamples;\n}\n\nfunction getSerializeStyleForContentType(contentType) {\n  if (contentType === 'application/json') {\n    return 'json';\n  }\n  if (contentType === 'application/xml') {\n    return 'xml';\n  }\n  return null;\n}\n\nexport function getSchemaFromParam(param) {\n  if (param.schema) {\n    return [param.schema, null, null];\n  }\n  if (param.content) {\n    // we gonna use the first content-encoding\n    for (const contentType of Object.keys(param.content)) {\n      if (param.content[contentType].schema) {\n        return [param.content[contentType].schema, getSerializeStyleForContentType(contentType), param.content[contentType]];\n      }\n    }\n  }\n  return [null, null, null];\n}\n", "import { LitElement, html, css } from 'lit';\nimport { copyToClipboard } from '~/utils/common-utils';\nimport FontStyles from '~/styles/font-styles';\nimport BorderStyles from '~/styles/border-styles';\nimport InputStyles from '~/styles/input-styles';\nimport CustomStyles from '~/styles/custom-styles';\n\nexport default class JsonTree extends LitElement {\n  static get properties() {\n    return {\n      data: { type: Object },\n      renderStyle: { type: String, attribute: 'render-style' },\n    };\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      BorderStyles,\n      InputStyles,\n      css`\n      :host{\n        display:flex;\n      }\n      :where(button, input[type=\"checkbox\"], [tabindex=\"0\"]):focus-visible { box-shadow: var(--focus-shadow); }\n      :where(input[type=\"text\"], input[type=\"password\"], select, textarea):focus-visible { border-color: var(--primary-color); }\n      .json-tree {\n        position: relative;\n        font-family: var(--font-mono);\n        font-size: var(--font-size-small);\n        display:inline-block;\n        overflow:hidden;\n        word-break: break-all;\n        flex:1;\n        line-height: calc(var(--font-size-small) + 6px);\n        min-height: 40px;\n        direction: ltr; \n        text-align: left;\n      }\n\n      .open-bracket {\n        display:inline-block;\n        padding: 0 20px 0 0;\n        cursor:pointer;\n        border: 1px solid transparent;\n        border-radius:3px;\n      }\n      .close-bracket {\n        border: 1px solid transparent;\n        border-radius:3px;\n        display:inline-block;\n      }\n      .open-bracket:hover {\n        color:var(--primary-color);\n        background-color:var(--hover-color);\n        border: 1px solid var(--border-color);\n      }\n      .open-bracket.expanded:hover ~ .inside-bracket {\n        border-left: 1px solid var(--fg3);\n      }\n      .open-bracket.expanded:hover ~ .close-bracket {\n        color:var(--primary-color);\n      }\n      .inside-bracket {\n        padding-left:12px;\n        overflow: hidden;\n        border-left:1px dotted var(--border-color);\n      }\n      .open-bracket.collapsed + .inside-bracket,\n      .open-bracket.collapsed + .inside-bracket + .close-bracket {\n        display:none;\n      }\n\n      .string{color:var(--green);}\n      .number{color:var(--blue);}\n      .null{color:var(--red);}\n      .boolean{color:var(--purple);}\n      .object{color:var(--fg)}\n      .toolbar {\n        position: absolute;\n        top:5px;\n        right:6px;\n        display:flex;\n        padding:2px;\n        align-items: center;\n      }`,\n      CustomStyles,\n    ];\n  }\n\n  /* eslint-disable indent */\n  render() {\n    return html`\n      <div class = \"json-tree\"  @click='${(e) => { if (e.target.classList.contains('btn-copy')) { copyToClipboard(JSON.stringify(this.data, null, 2), e); } else { this.toggleExpand(e); } }}'>\n        <div class='toolbar'> \n          <button class=\"toolbar-btn btn-copy\" part=\"btn btn-fill btn-copy\"> Copy </button>\n        </div>\n          ${this.generateTree(this.data, true)}\n      </div>  \n    `;\n  }\n\n  generateTree(data, isLast = false) {\n    if (data === null) {\n      return html`<div class=\"null\" style=\"display:inline;\">null</div>`;\n    }\n    if (typeof data === 'object' && (data instanceof Date === false)) {\n      const detailType = Array.isArray(data) ? 'array' : 'pure_object';\n      if (Object.keys(data).length === 0) {\n        return html`${(Array.isArray(data) ? '[ ],' : '{ },')}`;\n      }\n      return html`\n      <div class=\"open-bracket expanded ${detailType === 'array' ? 'array' : 'object'}\" > ${detailType === 'array' ? '[' : '{'}</div>\n      <div class=\"inside-bracket\">\n        ${Object.keys(data).map((key, i, a) => html`\n          <div class=\"item\"> \n            ${detailType === 'pure_object' ? html`\"${key}\":` : ''}\n            ${this.generateTree(data[key], i === (a.length - 1))}\n          </div>`)\n        }\n      </div>\n      <div class=\"close-bracket\">${detailType === 'array' ? ']' : '}'}${isLast ? '' : ','}</div>\n      `;\n    }\n    return (typeof data === 'string' || data instanceof Date)\n      ? html`<span class=\"${typeof data}\">\"${data}\"</span>${isLast ? '' : ','}`\n      : html`<span class=\"${typeof data}\">${data}</span>${isLast ? '' : ','}`;\n  }\n  /* eslint-enable indent */\n\n  toggleExpand(e) {\n    const openBracketEl = e.target;\n    if (e.target.classList.contains('open-bracket')) {\n      if (openBracketEl.classList.contains('expanded')) {\n        openBracketEl.classList.replace('expanded', 'collapsed');\n        e.target.innerHTML = e.target.classList.contains('array') ? '[...]' : '{...}';\n      } else {\n        openBracketEl.classList.replace('collapsed', 'expanded');\n        e.target.innerHTML = e.target.classList.contains('array') ? '[' : '{';\n      }\n    }\n  }\n}\n// Register the element with the browser\ncustomElements.define('json-tree', JsonTree);\n", "import { LitElement, html, css } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport FontStyles from '~/styles/font-styles';\nimport SchemaStyles from '~/styles/schema-styles';\nimport BorderStyles from '~/styles/border-styles';\nimport CustomStyles from '~/styles/custom-styles';\n\nexport default class SchemaTree extends LitElement {\n  static get properties() {\n    return {\n      data: { type: Object },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n      schemaHideReadOnly: { type: String, attribute: 'schema-hide-read-only' },\n      schemaHideWriteOnly: { type: String, attribute: 'schema-hide-write-only' },\n    };\n  }\n\n  connectedCallback() {\n    super.connectedCallback();\n    if (!this.schemaExpandLevel || this.schemaExpandLevel < 1) { this.schemaExpandLevel = 99999; }\n    if (!this.schemaDescriptionExpanded || !'true false'.includes(this.schemaDescriptionExpanded)) { this.schemaDescriptionExpanded = 'false'; }\n    if (!this.schemaHideReadOnly || !'true false'.includes(this.schemaHideReadOnly)) { this.schemaHideReadOnly = 'true'; }\n    if (!this.schemaHideWriteOnly || !'true false'.includes(this.schemaHideWriteOnly)) { this.schemaHideWriteOnly = 'true'; }\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      SchemaStyles,\n      BorderStyles,\n      css`\n      .tree {\n        font-size:var(--font-size-small);\n        text-align: left;\n        direction: ltr;\n        line-height:calc(var(--font-size-small) + 6px);\n      }\n      .tree .tr:hover{\n        background-color:var(--hover-color);\n      }\n      .collapsed-all-descr .tr:not(.expanded-descr) {\n        overflow: hidden;\n        max-height:calc(var(--font-size-small) + 8px);\n      }\n      .tree .key {\n        max-width: 300px;\n      }\n      .key.deprecated .key-label {\n        color: var(--red);\n      }\n      .tr.expanded:hover > .td.key > .open-bracket {\n        color: var(--primary-color);\n      }\n      .tr.expanded:hover + .inside-bracket {\n        border-left: 1px solid var(--fg3);\n      }\n      .tr.expanded:hover + .inside-bracket + .close-bracket {\n        color: var(--primary-color);\n      }\n      .inside-bracket.xxx-of-option {\n        border-left: 1px solid transparent;\n      }\n      .open-bracket{\n        display:inline-block;\n        padding: 0 20px 0 0;\n        cursor:pointer;\n        border: 1px solid transparent;\n        border-radius:3px;\n      }\n      .open-bracket:hover {\n        color:var(--primary-color);\n        background-color:var(--hover-color);\n        border: 1px solid var(--border-color);\n      }\n      .close-bracket{\n        display:inline-block;\n        font-family: var(--font-mono);\n      }\n      .tr.collapsed + .inside-bracket,\n      .tr.collapsed + .inside-bracket + .close-bracket{\n        overflow: hidden;\n        display:none;\n      }\n      .inside-bracket.object,\n      .inside-bracket.array {\n        border-left: 1px dotted var(--border-color);\n      }`,\n      CustomStyles,\n    ];\n  }\n\n  /* eslint-disable indent */\n  render() {\n    return html`\n      <div class=\"tree ${this.schemaDescriptionExpanded === 'true' ? 'expanded-all-descr' : 'collapsed-all-descr'}\" @click=\"${(e) => this.handleAllEvents(e)}\">\n        <div class=\"toolbar\">\n          <div class=\"toolbar-item schema-root-type ${this.data?.['::type'] || ''} \"> ${this.data?.['::type'] || ''} </div>\n          ${this.allowSchemaDescriptionExpandToggle === 'true'\n            ? html`\n              <div style=\"flex:1\"></div>\n              <div part=\"schema-toolbar-item schema-multiline-toggle\" class='toolbar-item schema-multiline-toggle'> \n                ${this.schemaDescriptionExpanded === 'true' ? 'Single line description' : 'Multiline description'}\n              </div>`\n            : ''\n          }\n        </div>\n        <span part=\"schema-description\" class='m-markdown'> ${unsafeHTML(marked(this.data?.['::description'] || ''))}</span>\n        ${this.data\n          ? html`\n            ${this.generateTree(\n              this.data['::type'] === 'array' ? this.data['::props'] : this.data,\n              this.data['::type'],\n              this.data['::array-type'] || '',\n            )}`\n          : html`<span class='mono-font' style='color:var(--red)'> Schema not found </span>`\n        }\n      </div>  \n    `;\n  }\n\n  generateTree(data, dataType = 'object', arrayType = '', key = '', description = '', schemaLevel = 0, indentLevel = 0, readOrWrite = '') {\n    if (this.schemaHideReadOnly === 'true') {\n      if (dataType === 'array') {\n        if (readOrWrite === 'readonly') {\n          return;\n        }\n      }\n      if (data?.['::readwrite'] === 'readonly') {\n        return;\n      }\n    }\n    if (this.schemaHideWriteOnly === 'true') {\n      if (dataType === 'array') {\n        if (readOrWrite === 'writeonly') {\n          return;\n        }\n      }\n      if (data?.['::readwrite'] === 'writeonly') {\n        return;\n      }\n    }\n\n    if (!data) {\n      return html`<div class=\"null\" style=\"display:inline;\">\n        <span class=\"key-label xxx-of-key\"> ${key.replace('::OPTION~', '')}</span>\n        ${\n          dataType === 'array'\n            ? html`<span class='mono-font'> [ ] </span>`\n            : dataType === 'object'\n              ? html`<span class='mono-font'> { } </span>`\n              : html`<span class='mono-font'> schema undefined </span>`\n        }\n      </div>`;\n    }\n    if (Object.keys(data).length === 0) {\n      return html`<span class=\"key object\">${key}:{ }</span>`;\n    }\n    let keyLabel = '';\n    let keyDescr = '';\n    if (key.startsWith('::ONE~OF') || key.startsWith('::ANY~OF')) {\n      keyLabel = key.replace('::', '').replace('~', ' ');\n    } else if (key.startsWith('::OPTION')) {\n      const parts = key.split('~');\n      [, keyLabel, keyDescr] = parts;\n    } else {\n      keyLabel = key;\n    }\n\n    const leftPadding = 12;\n    const minFieldColWidth = 400 - (indentLevel * leftPadding);\n    let openBracket = '';\n    let closeBracket = '';\n    const newSchemaLevel = data['::type']?.startsWith('xxx-of') ? schemaLevel : (schemaLevel + 1);\n    // const newIndentLevel = dataType === 'xxx-of-option' || data['::type'] === 'xxx-of-option' ? indentLevel : (indentLevel + 1);\n    const newIndentLevel = dataType === 'xxx-of-option' || data['::type'] === 'xxx-of-option' || key.startsWith('::OPTION') ? indentLevel : (indentLevel + 1);\n    if (data['::type'] === 'object') {\n      if (dataType === 'array') {\n        if (schemaLevel < this.schemaExpandLevel) {\n          openBracket = html`<span class=\"open-bracket array-of-object\" >[{</span>`;\n        } else {\n          openBracket = html`<span class=\"open-bracket array-of-object\">[{...}]</span>`;\n        }\n        closeBracket = '}]';\n      } else {\n        if (schemaLevel < this.schemaExpandLevel) {\n          openBracket = html`<span class=\"open-bracket object\">{</span>`;\n        } else {\n          openBracket = html`<span class=\"open-bracket object\">{...}</span>`;\n        }\n        closeBracket = '}';\n      }\n    } else if (data['::type'] === 'array') {\n      if (dataType === 'array') {\n        const arrType = arrayType !== 'object' ? arrayType : '';\n        if (schemaLevel < this.schemaExpandLevel) {\n          openBracket = html`<span class=\"open-bracket array-of-array\" data-array-type=\"${arrType}\">[[ ${arrType} </span>`;\n        } else {\n          openBracket = html`<span class=\"open-bracket array-of-array\"  data-array-type=\"${arrType}\">[[...]]</span>`;\n        }\n        closeBracket = ']]';\n      } else {\n        if (schemaLevel < this.schemaExpandLevel) {\n          openBracket = html`<span class=\"open-bracket array\">[</span>`;\n        } else {\n          openBracket = html`<span class=\"open-bracket array\">[...]</span>`;\n        }\n        closeBracket = ']';\n      }\n    }\n    if (typeof data === 'object') {\n      return html`\n        <div class=\"tr ${schemaLevel < this.schemaExpandLevel || data['::type']?.startsWith('xxx-of') ? 'expanded' : 'collapsed'} ${data['::type'] || 'no-type-info'}\" title=\"${data['::deprecated'] ? 'Deprecated' : ''}\">\n          <div class=\"td key ${data['::deprecated'] ? 'deprecated' : ''}\" style='min-width:${minFieldColWidth}px'>\n            ${data['::type'] === 'xxx-of-option' || data['::type'] === 'xxx-of-array' || key.startsWith('::OPTION')\n              ? html`<span class='key-label xxx-of-key'> ${keyLabel}</span><span class=\"xxx-of-descr\">${keyDescr}</span>`\n              : keyLabel === '::props' || keyLabel === '::ARRAY~OF'\n                ? ''\n                : schemaLevel > 0\n                  ? html`<span class=\"key-label\" title=\"${readOrWrite === 'readonly' ? 'Read-Only' : readOrWrite === 'writeonly' ? 'Write-Only' : ''}\">\n                      ${data['::deprecated'] ? '✗' : ''}\n                      ${keyLabel.replace(/\\*$/, '')}${keyLabel.endsWith('*') ? html`<span style=\"color:var(--red)\">*</span>` : ''}${readOrWrite === 'readonly' ? html` 🆁` : readOrWrite === 'writeonly' ? html` 🆆` : readOrWrite}:\n                    </span>`\n                  : ''\n            }\n            ${openBracket}\n          </div>\n          <div class='td key-descr m-markdown-small'>${unsafeHTML(marked(description || ''))}</div>\n        </div>\n        <div class='inside-bracket ${data['::type'] || 'no-type-info'}' style='padding-left:${data['::type'] === 'xxx-of-option' || data['::type'] === 'xxx-of-array' ? 0 : leftPadding}px;'>\n          ${Array.isArray(data) && data[0]\n            ? html`${this.generateTree(data[0], 'xxx-of-option', '', '::ARRAY~OF', '', newSchemaLevel, newIndentLevel, data[0]['::readwrite'])}`\n            : html`\n              ${Object.keys(data).map((dataKey) => html`\n                ${['::title', '::description', '::type', '::props', '::deprecated', '::array-type', '::readwrite', '::dataTypeLabel'].includes(dataKey)\n                  ? data[dataKey]['::type'] === 'array' || data[dataKey]['::type'] === 'object'\n                    ? html`${this.generateTree(\n                      data[dataKey]['::type'] === 'array' ? data[dataKey]['::props'] : data[dataKey],\n                        data[dataKey]['::type'],\n                        data[dataKey]['::array-type'] || '',\n                        dataKey,\n                        data[dataKey]['::description'],\n                        newSchemaLevel,\n                        newIndentLevel,\n                        data[dataKey]['::readwrite'] ? data[dataKey]['::readwrite'] : '',\n                      )}`\n                    : ''\n                  : html`${this.generateTree(\n                    data[dataKey]['::type'] === 'array' ? data[dataKey]['::props'] : data[dataKey],\n                    data[dataKey]['::type'],\n                    data[dataKey]['::array-type'] || '',\n                    dataKey,\n                    data[dataKey]?.['::description'] || '',\n                    newSchemaLevel,\n                    newIndentLevel,\n                    data[dataKey]['::readwrite'] ? data[dataKey]['::readwrite'] : '',\n                  )}`\n                }\n              `)}\n            `\n          }\n        </div>\n        ${data['::type'] && data['::type'].includes('xxx-of')\n          ? ''\n          : html`<div class='close-bracket'> ${closeBracket} </div>`\n        }\n      `;\n    }\n\n    // For Primitive types and array of Primitives\n    // eslint-disable-next-line no-unused-vars\n    const [type, primitiveReadOrWrite, constraint, defaultValue, allowedValues, pattern, schemaDescription, schemaTitle, deprecated] = data.split('~|~');\n    if (primitiveReadOrWrite === '🆁' && this.schemaHideReadOnly === 'true') {\n      return;\n    }\n    if (primitiveReadOrWrite === '🆆' && this.schemaHideWriteOnly === 'true') {\n      return;\n    }\n    const dataTypeCss = type.replace(/┃.*/g, '').replace(/[^a-zA-Z0-9+]/g, '').substring(0, 4).toLowerCase();\n    const descrExpander = `${constraint || defaultValue || allowedValues || pattern ? `<span class=\"descr-expand-toggle ${this.schemaDescriptionExpanded === 'true' ? 'expanded-descr' : ''}\">➔</span>` : ''}`;\n    let finalReadWriteText = '';\n    let finalReadWriteTip = '';\n    if (dataType === 'array') {\n      if (readOrWrite === 'readonly') {\n        finalReadWriteText = '🆁';\n        finalReadWriteTip = 'Read-Only';\n      } else if (readOrWrite === 'writeonly') {\n        finalReadWriteText = '🆆';\n        finalReadWriteTip = 'Write-Only';\n      }\n    } else if (primitiveReadOrWrite === '🆁') {\n      finalReadWriteText = '🆁';\n      finalReadWriteTip = 'Read-Only';\n    } else if (primitiveReadOrWrite === '🆆') {\n      finalReadWriteText = '🆆';\n      finalReadWriteTip = 'Write-Only';\n    }\n\n    return html`\n      <div class = \"tr primitive\" title=\"${deprecated ? 'Deprecated' : ''}\">\n        <div class=\"td key ${deprecated}\" style='min-width:${minFieldColWidth}px'>\n          ${deprecated ? html`<span style='color:var(--red);'>✗</span>` : ''}\n          ${keyLabel.endsWith('*')\n            ? html`<span class=\"key-label\">${keyLabel.substring(0, keyLabel.length - 1)}</span><span style='color:var(--red);'>*</span>:`\n            : key.startsWith('::OPTION')\n              ? html`<span class='key-label xxx-of-key'>${keyLabel}</span><span class=\"xxx-of-descr\">${keyDescr}</span>`\n              : html`<span class=\"key-label\">${keyLabel}:</span>`\n          }\n          <span class=\"${dataTypeCss}\" title=\"${finalReadWriteTip}\"> \n            ${dataType === 'array' ? `[${type}]` : `${type}`}\n            ${finalReadWriteText}\n          </span>\n        </div>\n        <div class='td key-descr'>\n          ${description || schemaTitle || schemaDescription\n            ? html`${html`<span class=\"m-markdown-small\">\n                ${unsafeHTML(marked(dataType === 'array'\n                  ? `${descrExpander} ${description}`\n                  : schemaTitle\n                    ? `${descrExpander} <b>${schemaTitle}:</b> ${schemaDescription}`\n                    : `${descrExpander} ${schemaDescription}`))}\n              </span>`\n              }`\n            : ''\n          }  \n          ${constraint ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px'><span class='bold-text'>Constraints: </span>${constraint}</div>` : ''}\n          ${defaultValue ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px'><span class='bold-text'>Default: </span>${defaultValue}</div>` : ''}\n          ${allowedValues ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px'><span class='bold-text'>${type === 'const' ? 'Value' : 'Allowed'}: </span>${allowedValues}</div>` : ''}\n          ${pattern ? html`<div style='display:inline-block; line-break: anywhere; margin-right:8px'><span class='bold-text'>Pattern: </span>${pattern}</div>` : ''}\n        </div>\n      </div>\n    `;\n  }\n  /* eslint-enable indent */\n\n  handleAllEvents(e) {\n    if (e.target.classList.contains('open-bracket')) {\n      this.toggleObjectExpand(e);\n    } else if (e.target.classList.contains('schema-multiline-toggle')) {\n      this.schemaDescriptionExpanded = (this.schemaDescriptionExpanded === 'true' ? 'false' : 'true');\n    } else if (e.target.classList.contains('descr-expand-toggle')) {\n      const trEl = e.target.closest('.tr');\n      if (trEl) {\n        trEl.classList.toggle('expanded-descr');\n        trEl.style.maxHeight = trEl.scrollHeight;\n      }\n    }\n  }\n\n  toggleObjectExpand(e) {\n    const rowEl = e.target.closest('.tr');\n    if (rowEl.classList.contains('expanded')) {\n      rowEl.classList.replace('expanded', 'collapsed');\n      e.target.innerHTML = e.target.classList.contains('array-of-object')\n        ? '[{...}]'\n        : e.target.classList.contains('array-of-array')\n          ? '[[...]]'\n          : e.target.classList.contains('array')\n            ? '[...]'\n            : '{...}';\n    } else {\n      rowEl.classList.replace('collapsed', 'expanded');\n      e.target.innerHTML = e.target.classList.contains('array-of-object')\n        ? '[{'\n        : e.target.classList.contains('array-of-array')\n          ? `[[ ${e.target.dataset.arrayType}`\n          : e.target.classList.contains('object')\n            ? '{'\n            : '[';\n    }\n  }\n}\ncustomElements.define('schema-tree', SchemaTree);\n", "import { LitElement, html, css } from 'lit';\n\nexport default class TagInput extends LitElement {\n  /* eslint-disable indent */\n  render() {\n    let tagItemTmpl = '';\n    if (Array.isArray(this.value)) {\n      tagItemTmpl = html`${this.value\n        .filter((v) => typeof v === 'string' && v.trim() !== '')\n        .map((v) => html`<span class='tag'>${v}</span>`)\n      }`;\n    }\n    return html`\n      <div class='tags'>\n        ${tagItemTmpl}\n        <input type=\"text\" class='editor' @paste=\"${(e) => this.afterPaste(e)}\" @keydown=\"${this.afterKeyDown}\" @blur=\"${this.onBlur}\" placeholder=\"${this.placeholder || ''}\">\n      </div>\n    `;\n  }\n  /* eslint-enable indent */\n\n  static get properties() {\n    return {\n      placeholder: { type: String },\n      value: { type: Array, attribute: 'value' },\n    };\n  }\n\n  attributeChangedCallback(name, oldVal, newVal) {\n    if (name === 'value') {\n      if (newVal && oldVal !== newVal) {\n        this.value = newVal.split(',').filter((v) => v.trim() !== '');\n      }\n    }\n    super.attributeChangedCallback(name, oldVal, newVal);\n  }\n\n  afterPaste(e) {\n    const clipboardData = e.clipboardData || window.clipboardData;\n    const pastedData = clipboardData.getData('Text');\n    const pastedArray = pastedData ? pastedData.split(',').filter((v) => v.trim() !== '') : '';\n    if (pastedArray) {\n      if (Array.isArray(this.value)) {\n        this.value = [...this.value, ...pastedArray];\n      } else {\n        this.value = pastedArray;\n      }\n    }\n    e.preventDefault();\n  }\n\n  afterKeyDown(e) {\n    if (e.keyCode === 13) {\n      e.stopPropagation();\n      e.preventDefault();\n      if (e.target.value) {\n        if (Array.isArray(this.value)) {\n          this.value = [...this.value, e.target.value];\n        } else {\n          this.value = [e.target.value];\n        }\n        e.target.value = '';\n      }\n    } else if (e.keyCode === 8) {\n      if (e.target.value.length === 0) {\n        if (Array.isArray(this.value) && this.value.length > 0) {\n          this.value.splice(-1);\n          this.value = [...this.value];\n        }\n      }\n    }\n  }\n\n  onBlur(e) {\n    if (e.target.value) {\n      if (Array.isArray(this.value)) {\n        this.value = [...this.value, e.target.value];\n      } else {\n        this.value = [e.target.value];\n      }\n      e.target.value = '';\n    }\n  }\n\n  static get styles() {\n    return [css`\n      .tags {\n        display:flex;\n        flex-wrap: wrap;\n        outline: none;\n        padding:0;\n        border-radius:var(--border-radius);\n        border:1px solid var(--border-color);\n        cursor:text;\n        overflow:hidden;\n        background:var(--input-bg);\n      }\n      .tag, .editor {\n        padding:3px;\n        margin:2px;\n      }\n      .tag{\n        border:1px solid var(--border-color);\n        background-color:var(--bg3);\n        color:var(--fg3);\n        border-radius:var(--border-radius);\n        word-break: break-all;\n        font-size: var(--font-size-small);\n      }\n      .tag:hover ~ #cursor {\n        display: block;\n      }\n      .editor {\n        flex:1;\n        border:1px solid transparent;\n        color:var(--fg);\n        min-width:60px;\n        outline: none;\n        line-height: inherit;\n        font-family:inherit;\n        background:transparent;\n        font-size: calc(var(--font-size-small) + 1px);\n      }\n      .editor:focus-visible {\n        outline: 1px solid;\n      }\n      .editor::placeholder {\n        color: var(--placeholder-color);\n        opacity:1;\n      }\n    `];\n  }\n}\n// Register the element with the browser\ncustomElements.define('tag-input', TagInput);\n", "import { LitElement, html, css } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { guard } from 'lit/directives/guard.js'; // eslint-disable-line import/extensions\nimport { live } from 'lit/directives/live.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport formatXml from 'xml-but-prettier';\nimport Prism from 'prismjs';\nimport TableStyles from '~/styles/table-styles';\nimport FlexStyles from '~/styles/flex-styles';\nimport InputStyles from '~/styles/input-styles';\nimport FontStyles from '~/styles/font-styles';\nimport BorderStyles from '~/styles/border-styles';\nimport TabStyles from '~/styles/tab-styles';\nimport PrismStyles from '~/styles/prism-styles';\nimport CustomStyles from '~/styles/custom-styles';\nimport { copyToClipboard, downloadResource, viewResource } from '~/utils/common-utils';\nimport { schemaInObjectNotation,\n  getTypeInfo,\n  generateExample,\n  normalizeExamples,\n  getSchemaFromParam,\n  json2xml,\n  nestExampleIfPresent,\n  anyExampleWithSummaryOrDescription } from '~/utils/schema-utils';\nimport '~/components/json-tree';\nimport '~/components/schema-tree';\nimport '~/components/tag-input';\n\nexport default class ApiRequest extends LitElement {\n  constructor() {\n    super();\n    this.responseMessage = '';\n    this.responseStatus = 'success';\n    this.responseHeaders = '';\n    this.responseText = '';\n    this.responseUrl = '';\n    this.curlSyntax = '';\n    this.activeResponseTab = 'response'; // allowed values: response, headers, curl\n    this.selectedRequestBodyType = '';\n    this.selectedRequestBodyExample = '';\n    this.activeParameterSchemaTabs = {};\n  }\n\n  static get properties() {\n    return {\n      serverUrl: { type: String, attribute: 'server-url' },\n      servers: { type: Array },\n      method: { type: String },\n      path: { type: String },\n      security: { type: Array },\n      parameters: { type: Array },\n      request_body: { type: Object },\n      api_keys: { type: Array },\n      parser: { type: Object },\n      accept: { type: String },\n      callback: { type: String },\n      webhook: { type: String },\n      responseMessage: { type: String, attribute: false },\n      responseText: { type: String, attribute: false },\n      responseHeaders: { type: String, attribute: false },\n      responseStatus: { type: String, attribute: false },\n      responseUrl: { type: String, attribute: false },\n      curlSyntax: { type: String, attribute: false },\n      fillRequestFieldsWithExample: { type: String, attribute: 'fill-request-fields-with-example' },\n      allowTry: { type: String, attribute: 'allow-try' },\n      showCurlBeforeTry: { type: String, attribute: 'show-curl-before-try' },\n      renderStyle: { type: String, attribute: 'render-style' },\n      schemaStyle: { type: String, attribute: 'schema-style' },\n      activeSchemaTab: { type: String, attribute: 'active-schema-tab' },\n      activeParameterSchemaTabs: {\n        type: Object,\n        converter: {\n          fromAttribute: (attr) => JSON.parse(attr),\n          toAttribute: (prop) => JSON.stringify(prop),\n        },\n        attribute: 'active-parameter-schema-tabs',\n      },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n      schemaHideReadOnly: { type: String, attribute: 'schema-hide-read-only' },\n      schemaHideWriteOnly: { type: String, attribute: 'schema-hide-write-only' },\n      fetchCredentials: { type: String, attribute: 'fetch-credentials' },\n\n      // properties for internal tracking\n      activeResponseTab: { type: String }, // internal tracking of response-tab not exposed as a attribute\n      selectedRequestBodyType: { type: String, attribute: 'selected-request-body-type' }, // internal tracking of selected request-body type\n      selectedRequestBodyExample: { type: String, attribute: 'selected-request-body-example' }, // internal tracking of selected request-body example\n    };\n  }\n\n  static get styles() {\n    return [\n      TableStyles,\n      InputStyles,\n      FontStyles,\n      FlexStyles,\n      BorderStyles,\n      TabStyles,\n      PrismStyles,\n      css`\n        *, *:before, *:after { box-sizing: border-box; }\n        :where(button, input[type=\"checkbox\"], [tabindex=\"0\"]):focus-visible { box-shadow: var(--focus-shadow); }\n        :where(input[type=\"text\"], input[type=\"password\"], select, textarea):focus-visible { border-color: var(--primary-color); }\n        tag-input:focus-within { outline: 1px solid;}\n        .read-mode {\n          margin-top: 24px;\n        }\n        .param-name,\n        .param-type {\n          margin: 1px 0;\n          text-align: right;\n          line-height: var(--font-size-small);\n        }\n        .param-name {\n          color: var(--fg); \n          font-family: var(--font-mono);\n        }\n        .param-name.deprecated { \n          color: var(--red);\n        }\n        .param-type{\n          color: var(--light-fg); \n          font-family: var(--font-regular);\n        }\n        .param-constraint{\n          min-width:100px;\n        }\n        .param-constraint:empty{\n          display:none;\n        }\n        .top-gap{margin-top:24px;}\n\n        .textarea {\n          min-height:220px; \n          padding:5px;\n          resize:vertical;\n          direction: ltr;\n        }\n        .example:first-child {\n          margin-top: -9px;\n        }\n\n        .response-message{\n          font-weight:bold;\n          text-overflow: ellipsis;\n        }\n        .response-message.error {\n          color:var(--red);\n        }\n        .response-message.success {\n          color:var(--blue);\n        }\n\n        .file-input-container {\n          align-items:flex-end;\n        }\n        .file-input-container .input-set:first-child .file-input-remove-btn{\n          visibility:hidden;\n        }\n\n        .file-input-remove-btn{\n          font-size:16px;\n          color:var(--red);\n          outline: none;\n          border: none;\n          background:none;\n          cursor:pointer;\n        }\n\n        .v-tab-btn {\n          font-size: var(--smal-font-size);\n          height:24px; \n          border:none; \n          background:none; \n          opacity: 0.3;\n          cursor: pointer;\n          padding: 4px 8px;\n        }\n        .v-tab-btn.active {\n          font-weight: bold;\n          background: var(--bg);\n          opacity: 1;\n        }\n\n        @media only screen and (min-width: 768px) {\n          .textarea {\n            padding:8px;\n          }\n        }\n\n        @media only screen and (max-width: 470px) {\n          .hide-in-small-screen {\n            display:none;\n          }\n        }\n      `,\n      CustomStyles,\n    ];\n  }\n\n  render() {\n    return html`\n    <div class=\"col regular-font request-panel ${'read focused'.includes(this.renderStyle) || this.callback === 'true' ? 'read-mode' : 'view-mode'}\">\n      <div class=\" ${this.callback === 'true' ? 'tiny-title' : 'req-res-title'} \"> \n        ${this.callback === 'true' ? 'CALLBACK REQUEST' : 'REQUEST'}\n      </div>\n      <div>\n        ${guard([this.method, this.path, this.allowTry, this.parameters, this.activeParameterSchemaTabs], () => this.inputParametersTemplate('path'))}\n        ${guard([this.method, this.path, this.allowTry, this.parameters, this.activeParameterSchemaTabs], () => this.inputParametersTemplate('query'))}\n        ${this.requestBodyTemplate()}\n        ${guard([this.method, this.path, this.allowTry, this.parameters, this.activeParameterSchemaTabs], () => this.inputParametersTemplate('header'))}\n        ${guard([this.method, this.path, this.allowTry, this.parameters, this.activeParameterSchemaTabs], () => this.inputParametersTemplate('cookie'))}\n        ${this.allowTry === 'false' ? '' : html`${this.apiCallTemplate()}`}\n      </div>  \n    </div>\n    `;\n  }\n\n  async updated() {\n    if (this.showCurlBeforeTry === 'true') {\n      this.applyCURLSyntax(this.shadowRoot);\n    }\n\n    // In focused mode after rendering the request component, update the text-areas(which contains examples) using\n    // the original values from hidden textareas\n    // This is done coz, user may update the dom by editing the textarea's and once the DOM is updated externally change detection wont happen, therefore update the values manually\n\n    // if (this.renderStyle === 'focused') {\n    //   if (changedProperties.size === 1 && changedProperties.has('activeSchemaTab')) {\n    //     // dont update example as only tabs is switched\n    //   } else {\n    //     this.requestUpdate();\n    //   }\n    // }\n\n    if (this.webhook === 'true') {\n      this.allowTry = 'false';\n    }\n  }\n\n  async saveExampleState() {\n    if (this.renderStyle === 'focused') {\n      const reqBodyTextAreaEls = [...this.shadowRoot.querySelectorAll('textarea.request-body-param-user-input')];\n      reqBodyTextAreaEls.forEach((el) => {\n        el.dataset.user_example = el.value;\n      });\n      const exampleTextAreaEls = [...this.shadowRoot.querySelectorAll('textarea[data-ptype=\"form-data\"]')];\n      exampleTextAreaEls.forEach((el) => {\n        el.dataset.user_example = el.value;\n      });\n      this.requestUpdate();\n    }\n  }\n\n  async updateExamplesFromDataAttr() {\n    // In focused mode after rendering the request component, update the text-areas(which contains examples) using\n    // the original values from hidden textareas\n    // This is done coz, user may update the dom by editing the textarea's and once the DOM is updated externally change detection wont happen, therefore update the values manually\n    if (this.renderStyle === 'focused') {\n      const reqBodyTextAreaEls = [...this.shadowRoot.querySelectorAll('textarea.request-body-param-user-input')];\n      reqBodyTextAreaEls.forEach((el) => {\n        el.value = el.dataset.user_example || el.dataset.example;\n      });\n      const exampleTextAreaEls = [...this.shadowRoot.querySelectorAll('textarea[data-ptype=\"form-data\"]')];\n      exampleTextAreaEls.forEach((el) => {\n        el.value = el.dataset.user_example || el.dataset.example;\n      });\n      this.requestUpdate();\n    }\n  }\n\n  /* eslint-disable indent */\n  renderExample(example, paramType, paramName) {\n    return html`\n      ${paramType === 'array' ? '[' : ''}\n      <a\n        part=\"anchor anchor-param-example\"\n        style=\"display:inline-block; min-width:24px; text-align:center\"\n        class=\"${this.allowTry === 'true' ? '' : 'inactive-link'}\"\n        data-example-type=\"${paramType === 'array' ? paramType : 'string'}\"\n        data-example=\"${example.value && Array.isArray(example.value) ? example.value?.join('~|~') : example.value || ''}\"\n        @click=\"${(e) => {\n          const inputEl = e.target.closest('table').querySelector(`[data-pname=\"${paramName}\"]`);\n          if (inputEl) {\n            inputEl.value = e.target.dataset.exampleType === 'array' ? e.target.dataset.example.split('~|~') : e.target.dataset.example;\n          }\n        }}\"\n      > ${example.printableValue || example.value} </a>\n      ${paramType === 'array' ? '] ' : ''}\n    `;\n  }\n\n  renderShortFormatExamples(examples, paramType, paramName) {\n    return html`${examples.map((x, i) => html`\n      ${i === 0 ? '' : '┃'}\n      ${this.renderExample(x, paramType, paramName)}`)}`;\n  }\n\n  renderLongFormatExamples(exampleList, paramType, paramName) {\n    return html` <ul style=\"list-style-type: disclosure-closed;\">\n      ${exampleList.map((v) => html`\n          <li>\n            ${this.renderExample(v, paramType, paramName)}\n            ${v.summary?.length > 0 ? html`<span>&lpar;${v.summary}&rpar;</span>` : ''}\n            ${v.description?.length > 0 ? html`<p>${unsafeHTML(marked(v.description))}</p>` : ''}\n          </li>\n        `)}\n    </ul>`;\n  }\n\n  exampleListTemplate(paramName, paramType, exampleList = []) {\n    return html` ${\n      exampleList.length > 0\n        ? html`<span style=\"font-weight:bold\">Examples: </span>\n          ${anyExampleWithSummaryOrDescription(exampleList)\n            ? this.renderLongFormatExamples(exampleList, paramType, paramName)\n            : this.renderShortFormatExamples(exampleList, paramType, paramName)}`\n        : ''\n      }`;\n  }\n\n  inputParametersTemplate(paramType) {\n    const filteredParams = this.parameters ? this.parameters.filter((param) => param.in === paramType) : [];\n    if (filteredParams.length === 0) {\n      return '';\n    }\n    let title = '';\n    if (paramType === 'path') {\n      title = 'PATH PARAMETERS';\n    } else if (paramType === 'query') {\n      title = 'QUERY-STRING PARAMETERS';\n    } else if (paramType === 'header') {\n      title = 'REQUEST HEADERS';\n    } else if (paramType === 'cookie') {\n      title = 'COOKIES';\n    }\n\n    const tableRows = [];\n    for (const param of filteredParams) {\n      const [declaredParamSchema, serializeStyle, mimeTypeElem] = getSchemaFromParam(param);\n      if (!declaredParamSchema) {\n        continue;\n      }\n      const paramSchema = getTypeInfo(declaredParamSchema);\n      if (!paramSchema) {\n        continue; // eslint-disable-line no-continue\n      }\n      const schemaAsObj = schemaInObjectNotation(declaredParamSchema, {});\n      // let exampleVal = '';\n      // let exampleList = [];\n      let paramStyle = 'form';\n      let paramExplode = true;\n      let paramAllowReserved = false;\n      if (paramType === 'query') {\n        if (param.style && 'form spaceDelimited pipeDelimited'.includes(param.style)) {\n          paramStyle = param.style;\n        } else if (serializeStyle) {\n          paramStyle = serializeStyle;\n        }\n        if (typeof param.explode === 'boolean') {\n          paramExplode = param.explode;\n        }\n        if (typeof param.allowReserved === 'boolean') {\n          paramAllowReserved = param.allowReserved;\n        }\n      }\n      // openapi 3.1.0 spec based examples (which must be Object(string : { value:any, summary?: string, description?: string})\n      const example = normalizeExamples(\n        (param.examples\n          || nestExampleIfPresent(param.example)\n          || nestExampleIfPresent(mimeTypeElem?.example)\n          || mimeTypeElem?.examples\n          || nestExampleIfPresent(paramSchema.examples)\n          || nestExampleIfPresent(paramSchema.example)\n        ),\n        paramSchema.type,\n      );\n      if (!example.exampleVal && paramSchema.type === 'object') {\n        example.exampleVal = generateExample(\n          declaredParamSchema,\n          serializeStyle || 'json',\n          '',\n          '',\n          this.callback === 'true' || this.webhook === 'true' ? true : false, // eslint-disable-line no-unneeded-ternary\n          this.callback === 'true' || this.webhook === 'true' ? false : true, // eslint-disable-line no-unneeded-ternary\n          true,\n          'text',\n          false,\n        )[0].exampleValue;\n      }\n      const labelColWidth = 'read focused'.includes(this.renderStyle) ? '200px' : '160px';\n      tableRows.push(html`\n      <tr title=\"${param.deprecated ? 'Deprecated' : ''}\"> \n        <td rowspan=\"${this.allowTry === 'true' ? '1' : '2'}\" style=\"width:${labelColWidth}; min-width:100px;\">\n          <div class=\"param-name ${param.deprecated ? 'deprecated' : ''}\" >\n            ${param.deprecated ? html`<span style='color:var(--red);'>✗</span>` : ''}\n            ${param.required ? html`<span style='color:var(--red)'>*</span>` : ''}\n            ${param.name}\n          </div>\n          <div class=\"param-type\">\n            ${paramSchema.type === 'array'\n              ? `${paramSchema.arrayType}`\n              : `${paramSchema.format ? paramSchema.format : paramSchema.type}`\n            }\n          </div>\n        </td>  \n        ${this.allowTry === 'true'\n          ? html`\n            <td style=\"min-width:100px;\" colspan=\"${paramSchema.default || paramSchema.constrain || paramSchema.allowedValues || paramSchema.pattern ? '1' : '2'}\">\n              ${paramSchema.type === 'array'\n                ? html`\n                  <tag-input class=\"request-param\" \n                    style = \"width:100%\" \n                    data-ptype = \"${paramType}\"\n                    data-pname = \"${param.name}\"\n                    data-example = \"${Array.isArray(example.exampleVal) ? example.exampleVal.join('~|~') : example.exampleVal}\"\n                    data-param-serialize-style = \"${paramStyle}\"\n                    data-param-serialize-explode = \"${paramExplode}\"\n                    data-param-allow-reserved = \"${paramAllowReserved}\"\n                    data-x-fill-example = \"${param['x-fill-example'] || 'yes'}\"\n                    data-array = \"true\"\n                    placeholder = \"add-multiple &#x21a9;\"\n                    .value=\"${param['x-fill-example'] === 'no'\n                      ? []\n                      : live(this.fillRequestFieldsWithExample === 'true' ? Array.isArray(example.exampleVal) ? example.exampleVal : [example.exampleVal] : [])\n                    }\"\n                  >\n                  </tag-input>`\n                : paramSchema.type === 'object'\n                  ? html`\n                    <div class=\"tab-panel col\" style=\"border-width:0 0 1px 0;\">\n                      <div class=\"tab-buttons row\" @click=\"${(e) => {\n                        if (e.target.tagName.toLowerCase() === 'button') {\n                          const newState = { ...this.activeParameterSchemaTabs };\n                          newState[param.name] = e.target.dataset.tab;\n                          this.activeParameterSchemaTabs = newState;\n                        }\n                      }}\">\n                        <button class=\"tab-btn ${this.activeParameterSchemaTabs[param.name] === 'example' ? 'active' : ''}\" data-tab = 'example'>EXAMPLE </button>\n                        <button class=\"tab-btn ${this.activeParameterSchemaTabs[param.name] !== 'example' ? 'active' : ''}\" data-tab = 'schema'>SCHEMA</button>\n                      </div>\n                      ${this.activeParameterSchemaTabs[param.name] === 'example'\n                        ? html`<div class=\"tab-content col\">\n                          <textarea \n                            class = \"textarea request-param\"\n                            part = \"textarea textarea-param\"\n                            data-ptype = \"${paramType}-object\"\n                            data-pname = \"${param.name}\"\n                            data-example = \"${example.exampleVal}\"\n                            data-param-serialize-style = \"${paramStyle}\"\n                            data-param-serialize-explode = \"${paramExplode}\"\n                            data-param-allow-reserved = \"${paramAllowReserved}\"\n                            data-x-fill-example = \"${param['x-fill-example'] || 'yes'}\"\n                            spellcheck = \"false\"\n                            .textContent=\"${param['x-fill-example'] === 'no' ? '' : live(this.fillRequestFieldsWithExample === 'true' ? example.exampleVal : '')}\"\n                            style = \"resize:vertical; width:100%; height: ${'read focused'.includes(this.renderStyle) ? '180px' : '120px'};\"\n                            @input=${(e) => {\n                              const requestPanelEl = this.getRequestPanel(e);\n                              this.liveCURLSyntaxUpdate(requestPanelEl);\n                            }}\n                          ></textarea>\n                        </div>`\n                        : html`\n                          <div class=\"tab-content col\">\n                            <schema-tree\n                              class = 'json'\n                              style = 'display: block'\n                              .data = '${schemaAsObj}'\n                              schema-expand-level = \"${this.schemaExpandLevel}\"\n                              schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n                              allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n                              schema-hide-read-only = \"${this.schemaHideReadOnly.includes(this.method)}\"\n                              schema-hide-write-only = \"${this.schemaHideWriteOnly.includes(this.method)}\"\n                              exportparts = \"wrap-request-btn:wrap-request-btn, btn:btn, btn-fill:btn-fill, btn-outline:btn-outline, btn-try:btn-try, btn-clear:btn-clear, btn-clear-resp:btn-clear-resp,\n                                file-input:file-input, textbox:textbox, textbox-param:textbox-param, textarea:textarea, textarea-param:textarea-param, \n                                anchor:anchor, anchor-param-example:anchor-param-example\"\n                            > </schema-tree>\n                          </div>`\n                        }\n                    </div>`\n                  : html`\n                    <input type=\"${paramSchema.format === 'password' ? 'password' : 'text'}\" spellcheck=\"false\" style=\"width:100%\" \n                      class=\"request-param\"\n                      part=\"textbox textbox-param\"\n                      data-ptype=\"${paramType}\"\n                      data-pname=\"${param.name}\" \n                      data-example=\"${Array.isArray(example.exampleVal) ? example.exampleVal.join('~|~') : example.exampleVal}\"\n                      data-param-allow-reserved = \"${paramAllowReserved}\"\n                      data-x-fill-example = \"${param['x-fill-example'] || 'yes'}\"\n                      data-array=\"false\"\n                      .value=\"${param['x-fill-example'] === 'no' ? '' : live(this.fillRequestFieldsWithExample === 'true' ? example.exampleVal : '')}\"\n                      @input=${(e) => {\n                        const requestPanelEl = this.getRequestPanel(e);\n                        this.liveCURLSyntaxUpdate(requestPanelEl);\n                      }}\n                    />`\n                }\n            </td>`\n          : ''\n        }\n        ${paramSchema.default || paramSchema.constrain || paramSchema.allowedValues || paramSchema.pattern\n          ? html`\n            <td colspan=\"${(this.allowTry === 'true') ? '1' : '2'}\">\n              <div class=\"param-constraint\">\n                ${paramSchema.default ? html`<span style=\"font-weight:bold\">Default: </span>${paramSchema.default}<br/>` : ''}\n                ${paramSchema.pattern ? html`<span style=\"font-weight:bold\">Pattern: </span>${paramSchema.pattern}<br/>` : ''}\n                ${paramSchema.constrain ? html`${paramSchema.constrain}<br/>` : ''}\n                ${paramSchema.allowedValues && paramSchema.allowedValues.split('┃').map((v, i) => html`\n                  ${i > 0 ? '┃' : html`<span style=\"font-weight:bold\">Allowed: </span>`}\n                  ${html`\n                    <a part=\"anchor anchor-param-constraint\" class = \"${this.allowTry === 'true' ? '' : 'inactive-link'}\"\n                      data-type=\"${paramSchema.type === 'array' ? paramSchema.type : 'string'}\"\n                      data-enum=\"${v.trim()}\"\n                      @click=\"${(e) => {\n                        const inputEl = e.target.closest('table').querySelector(`[data-pname=\"${param.name}\"]`);\n                        if (inputEl) {\n                          if (e.target.dataset.type === 'array') {\n                            inputEl.value = [e.target.dataset.enum];\n                          } else {\n                            inputEl.value = e.target.dataset.enum;\n                          }\n                        }\n                      }}\"\n                    >${v}</a>`\n                  }`)}\n              </div>\n            </td>`\n          : html`<td></td>`\n        }\n      </tr>\n      <tr>\n        ${this.allowTry === 'true' ? html`<td style=\"border:none\"> </td>` : ''}\n        <td colspan=\"2\" style=\"border:none\">\n          <span class=\"m-markdown-small\">${unsafeHTML(marked(param.description || ''))}</span>\n          ${this.exampleListTemplate.call(this, param.name, paramSchema.type, example.exampleList)}\n        </td>\n      </tr>\n    `);\n    }\n\n    return html`\n    <div class=\"table-title top-gap\">${title}</div>\n    <div style=\"display:block; overflow-x:auto; max-width:100%;\">\n      <table role=\"presentation\" class=\"m-table\" style=\"width:100%; word-break:break-word;\">\n        ${tableRows}\n      </table>\n    </div>`;\n  }\n\n  // This method is called before navigation change in focused mode\n  async beforeNavigationFocusedMode() {\n    // this.saveExampleState();\n  }\n\n  // This method is called after navigation change in focused mode\n  async afterNavigationFocusedMode() {\n    this.selectedRequestBodyType = '';\n    this.selectedRequestBodyExample = '';\n    this.updateExamplesFromDataAttr();\n    this.clearResponseData();\n  }\n\n  // Request-Body Event Handlers\n  onSelectExample(e) {\n    this.selectedRequestBodyExample = e.target.value;\n    const exampleDropdownEl = e.target;\n    window.setTimeout((selectEl) => {\n      const readOnlyExampleEl = selectEl.closest('.example-panel').querySelector('.request-body-param');\n      const userInputExampleTextareaEl = selectEl.closest('.example-panel').querySelector('.request-body-param-user-input');\n      userInputExampleTextareaEl.value = readOnlyExampleEl.innerText;\n\n      const requestPanelEl = this.getRequestPanel({ target: selectEl });\n      this.liveCURLSyntaxUpdate(requestPanelEl);\n    }, 0, exampleDropdownEl);\n  }\n\n  onMimeTypeChange(e) {\n    this.selectedRequestBodyType = e.target.value;\n    const mimeDropdownEl = e.target;\n    this.selectedRequestBodyExample = '';\n    window.setTimeout((selectEl) => {\n      const readOnlyExampleEl = selectEl.closest('.request-body-container').querySelector('.request-body-param');\n      if (readOnlyExampleEl) {\n        const userInputExampleTextareaEl = selectEl.closest('.request-body-container').querySelector('.request-body-param-user-input');\n        userInputExampleTextareaEl.value = readOnlyExampleEl.innerText;\n      }\n    }, 0, mimeDropdownEl);\n  }\n\n  requestBodyTemplate() {\n    if (!this.request_body) {\n      return '';\n    }\n    if (Object.keys(this.request_body).length === 0) {\n      return '';\n    }\n\n    // Variable to store partial HTMLs\n    let reqBodyTypeSelectorHtml = '';\n    let reqBodyFileInputHtml = '';\n    let reqBodyFormHtml = '';\n    let reqBodySchemaHtml = '';\n    let reqBodyExampleHtml = '';\n\n    const requestBodyTypes = [];\n    const { content } = this.request_body;\n    for (const mimeType in content) {\n      requestBodyTypes.push({\n        mimeType,\n        schema: content[mimeType].schema,\n        example: content[mimeType].example,\n        examples: content[mimeType].examples,\n      });\n      if (!this.selectedRequestBodyType) {\n        this.selectedRequestBodyType = mimeType;\n      }\n    }\n    // MIME Type selector\n    reqBodyTypeSelectorHtml = requestBodyTypes.length === 1\n      ? ''\n      : html`\n        <select style=\"min-width:100px; max-width:100%;  margin-bottom:-1px;\" @change = '${(e) => this.onMimeTypeChange(e)}'>\n          ${requestBodyTypes.map((reqBody) => html`\n            <option value = '${reqBody.mimeType}' ?selected = '${reqBody.mimeType === this.selectedRequestBodyType}'>\n              ${reqBody.mimeType}\n            </option> `)\n          }\n        </select>\n      `;\n\n    // For Loop - Main\n    requestBodyTypes.forEach((reqBody) => {\n      let schemaAsObj;\n      let reqBodyExamples = [];\n\n      if (this.selectedRequestBodyType.includes('json') || this.selectedRequestBodyType.includes('xml') || this.selectedRequestBodyType.includes('text') || this.selectedRequestBodyType.includes('jose')) {\n        // Generate Example\n        if (reqBody.mimeType === this.selectedRequestBodyType) {\n          reqBodyExamples = generateExample(\n            reqBody.schema,\n            reqBody.mimeType,\n            reqBody.examples,\n            reqBody.example,\n            this.callback === 'true' || this.webhook === 'true' ? true : false, // eslint-disable-line no-unneeded-ternary\n            this.callback === 'true' || this.webhook === 'true' ? false : true, // eslint-disable-line no-unneeded-ternary\n            'text',\n            false,\n          );\n          if (!this.selectedRequestBodyExample) {\n            this.selectedRequestBodyExample = (reqBodyExamples.length > 0 ? reqBodyExamples[0].exampleId : '');\n          }\n          reqBodyExampleHtml = html`\n            ${reqBodyExampleHtml}\n            <div class = 'example-panel border-top pad-top-8'>\n              ${reqBodyExamples.length === 1\n                ? ''\n                : html`\n                  <select style=\"min-width:100px; max-width:100%;  margin-bottom:-1px;\" @change='${(e) => this.onSelectExample(e)}'>\n                    ${reqBodyExamples.map((v) => html`<option value=\"${v.exampleId}\" ?selected=${v.exampleId === this.selectedRequestBodyExample} > \n                      ${v.exampleSummary.length > 80 ? v.exampleId : v.exampleSummary ? v.exampleSummary : v.exampleId} \n                    </option>`)}\n                  </select>\n                `\n              }\n              ${reqBodyExamples\n                .filter((v) => v.exampleId === this.selectedRequestBodyExample)\n                .map((v) => html`\n                <div class=\"example ${v.exampleId === this.selectedRequestBodyExample ? 'example-selected' : ''}\" data-example = '${v.exampleId}'>\n                  ${v.exampleSummary && v.exampleSummary.length > 80 ? html`<div style=\"padding: 4px 0\"> ${v.exampleSummary} </div>` : ''}\n                  ${v.exampleDescription ? html`<div class=\"m-markdown-small\" style=\"padding: 4px 0\"> ${unsafeHTML(marked(v.exampleDescription || ''))} </div>` : ''}\n                  <!-- This pre(hidden) is to store the original example value, this will remain unchanged when users switches from one example to another, its is used to populate the editable textarea -->\n                  <pre \n                    class = \"textarea is-hidden request-body-param ${reqBody.mimeType.substring(reqBody.mimeType.indexOf('/') + 1)}\" \n                    spellcheck = \"false\"\n                    data-ptype = \"${reqBody.mimeType}\" \n                    style=\"width:100%; resize:vertical; display:none\"\n                  >${(v.exampleFormat === 'text' ? v.exampleValue : JSON.stringify(v.exampleValue, null, 2))}</pre>\n\n                  <!-- this textarea is for user to edit the example -->\n                  <textarea \n                    class = \"textarea request-body-param-user-input\"\n                    part = \"textarea textarea-param\"\n                    spellcheck = \"false\"\n                    data-ptype = \"${reqBody.mimeType}\" \n                    data-example = \"${v.exampleFormat === 'text' ? v.exampleValue : JSON.stringify(v.exampleValue, null, 2)}\"\n                    data-example-format = \"${v.exampleFormat}\"\n                    style=\"width:100%; resize:vertical;\"\n                    .textContent = \"${this.fillRequestFieldsWithExample === 'true' ? (v.exampleFormat === 'text' ? v.exampleValue : JSON.stringify(v.exampleValue, null, 2)) : ''}\"\n                    @input=${(e) => {\n                      const requestPanelEl = this.getRequestPanel(e);\n                      this.liveCURLSyntaxUpdate(requestPanelEl);\n                    }}\n                  ></textarea>\n                </div>  \n              `)}\n\n            </div>\n          `;\n        }\n      } else if (this.selectedRequestBodyType.includes('form-urlencoded') || this.selectedRequestBodyType.includes('form-data')) {\n        if (reqBody.mimeType === this.selectedRequestBodyType) {\n          const ex = generateExample(\n            reqBody.schema,\n            reqBody.mimeType,\n            reqBody.examples,\n            reqBody.example,\n            this.callback === 'true' || this.webhook === 'true' ? true : false, // eslint-disable-line no-unneeded-ternary\n            this.callback === 'true' || this.webhook === 'true' ? false : true, // eslint-disable-line no-unneeded-ternary\n            'text',\n            false,\n          );\n          if (reqBody.schema) {\n            reqBodyFormHtml = this.formDataTemplate(reqBody.schema, reqBody.mimeType, (ex[0] ? ex[0].exampleValue : ''));\n          }\n        }\n      } else if ((/^audio\\/|^image\\/|^video\\/|^font\\/|tar$|zip$|7z$|rtf$|msword$|excel$|\\/pdf$|\\/octet-stream$/.test(this.selectedRequestBodyType))) {\n        if (reqBody.mimeType === this.selectedRequestBodyType) {\n          reqBodyFileInputHtml = html`\n            <div class = \"small-font-size bold-text row\">\n              <input type=\"file\" part=\"file-input\" style=\"max-width:100%\" class=\"request-body-param-file\" data-ptype=\"${reqBody.mimeType}\" spellcheck=\"false\" />\n            </div>  \n          `;\n        }\n      }\n\n      // Generate Schema\n      if (reqBody.mimeType.includes('json') || reqBody.mimeType.includes('xml') || reqBody.mimeType.includes('text') || this.selectedRequestBodyType.includes('jose')) {\n        schemaAsObj = schemaInObjectNotation(reqBody.schema, {});\n        if (this.schemaStyle === 'table') {\n          reqBodySchemaHtml = html`\n            ${reqBodySchemaHtml}\n            <schema-table\n              class = '${reqBody.mimeType.substring(reqBody.mimeType.indexOf('/') + 1)}'\n              style = 'display: ${this.selectedRequestBodyType === reqBody.mimeType ? 'block' : 'none'};'\n              .data = '${schemaAsObj}'\n              schema-expand-level = \"${this.schemaExpandLevel}\"\n              schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n              allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n              schema-hide-read-only = \"${this.schemaHideReadOnly}\"\n              schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n              exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n            > </schema-table>\n          `;\n        } else if (this.schemaStyle === 'tree') {\n          reqBodySchemaHtml = html`\n            ${reqBodySchemaHtml}\n            <schema-tree\n              class = \"${reqBody.mimeType.substring(reqBody.mimeType.indexOf('/') + 1)}\"\n              style = \"display: ${this.selectedRequestBodyType === reqBody.mimeType ? 'block' : 'none'};\"\n              .data = \"${schemaAsObj}\"\n              schema-expand-level = \"${this.schemaExpandLevel}\"\n              schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n              allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n              schema-hide-read-only = \"${this.schemaHideReadOnly}\"\n              schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n              exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n            > </schema-tree>\n          `;\n        }\n      }\n    });\n\n    return html`\n      <div class='request-body-container' data-selected-request-body-type=\"${this.selectedRequestBodyType}\">\n        <div class=\"table-title top-gap row\">\n          REQUEST BODY ${this.request_body.required ? html`<span class=\"mono-font\" style='color:var(--red)'>*</span>` : ''} \n          <span style = \"font-weight:normal; margin-left:5px\"> ${this.selectedRequestBodyType}</span>\n          <span style=\"flex:1\"></span>\n          ${reqBodyTypeSelectorHtml}\n        </div>\n        ${this.request_body.description ? html`<div class=\"m-markdown\" style=\"margin-bottom:12px\">${unsafeHTML(marked(this.request_body.description))}</div>` : ''}\n        \n        ${(this.selectedRequestBodyType.includes('json') || this.selectedRequestBodyType.includes('xml') || this.selectedRequestBodyType.includes('text') || this.selectedRequestBodyType.includes('jose'))\n          ? html`\n            <div class=\"tab-panel col\" style=\"border-width:0 0 1px 0;\">\n              <div class=\"tab-buttons row\" @click=\"${(e) => { if (e.target.tagName.toLowerCase() === 'button') { this.activeSchemaTab = e.target.dataset.tab; } }}\">\n                <button class=\"tab-btn ${this.activeSchemaTab === 'example' ? 'active' : ''}\" data-tab = 'example'>EXAMPLE</button>\n                <button class=\"tab-btn ${this.activeSchemaTab !== 'example' ? 'active' : ''}\" data-tab = 'schema'>SCHEMA</button>\n              </div>\n              ${html`<div class=\"tab-content col\" style=\"display:${this.activeSchemaTab === 'example' ? 'block' : 'none'};\"> ${reqBodyExampleHtml}</div>`}\n              ${html`<div class=\"tab-content col\" style=\"display:${this.activeSchemaTab === 'example' ? 'none' : 'block'};\"> ${reqBodySchemaHtml}</div>`}\n            </div>`\n          : html`  \n            ${reqBodyFileInputHtml}\n            ${reqBodyFormHtml}`\n        }\n      </div>  \n    `;\n  }\n\n  formDataParamAsObjectTemplate(fieldName, fieldSchema, mimeType) {\n    // This template is used when form-data param should be send as a object (application/json, application/xml)\n    const formdataPartSchema = schemaInObjectNotation(fieldSchema, {});\n    const formdataPartExample = generateExample(\n      fieldSchema,\n      'json',\n      fieldSchema.examples,\n      fieldSchema.example,\n      this.callback === 'true' || this.webhook === 'true' ? true : false, // eslint-disable-line no-unneeded-ternary\n      this.callback === 'true' || this.webhook === 'true' ? false : true, // eslint-disable-line no-unneeded-ternary\n      'text',\n      false,\n    );\n\n    return html`\n      <div class=\"tab-panel row\" style=\"min-height:220px; border-left: 6px solid var(--light-border-color); align-items: stretch;\">\n        <div style=\"width:24px; background-color:var(--light-border-color)\">\n          <div class=\"row\" style=\"flex-direction:row-reverse; width:160px; height:24px; transform:rotate(270deg) translateX(-160px); transform-origin:top left; display:block;\" @click=\"${(e) => {\n          if (e.target.classList.contains('v-tab-btn')) {\n            const { tab } = e.target.dataset;\n            if (tab) {\n              const tabPanelEl = e.target.closest('.tab-panel');\n              const selectedTabBtnEl = tabPanelEl.querySelector(`.v-tab-btn[data-tab=\"${tab}\"]`);\n              const otherTabBtnEl = [...tabPanelEl.querySelectorAll(`.v-tab-btn:not([data-tab=\"${tab}\"])`)];\n              const selectedTabContentEl = tabPanelEl.querySelector(`.tab-content[data-tab=\"${tab}\"]`);\n              const otherTabContentEl = [...tabPanelEl.querySelectorAll(`.tab-content:not([data-tab=\"${tab}\"])`)];\n              selectedTabBtnEl.classList.add('active');\n              selectedTabContentEl.style.display = 'block';\n              otherTabBtnEl.forEach((el) => { el.classList.remove('active'); });\n              otherTabContentEl.forEach((el) => { el.style.display = 'none'; });\n            }\n          }\n          if (e.target.tagName.toLowerCase() === 'button') { this.activeSchemaTab = e.target.dataset.tab; }\n        }}\">\n          <button class=\"v-tab-btn ${this.activeSchemaTab === 'example' ? 'active' : ''}\" data-tab = 'example'>EXAMPLE</button>\n          <button class=\"v-tab-btn ${this.activeSchemaTab !== 'example' ? 'active' : ''}\" data-tab = 'schema'>SCHEMA</button>\n        </div>\n      </div>\n      ${html`\n        <div class=\"tab-content col\" data-tab = 'example' style=\"display:${this.activeSchemaTab === 'example' ? 'block' : 'none'}; padding-left:5px; width:100%\"> \n          <textarea \n            class = \"textarea\"\n            part = \"textarea textarea-param\"\n            style = \"width:100%; border:none; resize:vertical;\" \n            data-array = \"false\" \n            data-ptype = \"${mimeType.includes('form-urlencode') ? 'form-urlencode' : 'form-data'}\"\n            data-pname = \"${fieldName}\"\n            data-example = \"${formdataPartExample[0]?.exampleValue || ''}\"\n            .textContent = \"${this.fillRequestFieldsWithExample === 'true' ? formdataPartExample[0].exampleValue : ''}\"\n            spellcheck = \"false\"\n          ></textarea>\n        </div>`\n      }\n      ${html`\n        <div class=\"tab-content col\" data-tab = 'schema' style=\"display:${this.activeSchemaTab !== 'example' ? 'block' : 'none'}; padding-left:5px; width:100%;\"> \n          <schema-tree\n            .data = '${formdataPartSchema}'\n            schema-expand-level = \"${this.schemaExpandLevel}\"\n            schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n            allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\",\n          > </schema-tree>\n        </div>`\n      }\n      </div>\n    `;\n  }\n\n  formDataTemplate(schema, mimeType, exampleValue = '') {\n    const formDataTableRows = [];\n    if (schema.properties) {\n      for (const fieldName in schema.properties) {\n        const fieldSchema = schema.properties[fieldName];\n        if (fieldSchema.readOnly) {\n          continue;\n        }\n        const fieldExamples = fieldSchema.examples || fieldSchema.example || '';\n        const fieldType = fieldSchema.type;\n        const paramSchema = getTypeInfo(fieldSchema);\n        const labelColWidth = 'read focused'.includes(this.renderStyle) ? '200px' : '160px';\n        const example = normalizeExamples((paramSchema.examples || paramSchema.example), paramSchema.type);\n        formDataTableRows.push(html`\n        <tr title=\"${fieldSchema.deprecated ? 'Deprecated' : ''}\"> \n          <td style=\"width:${labelColWidth}; min-width:100px;\">\n            <div class=\"param-name ${fieldSchema.deprecated ? 'deprecated' : ''}\">\n              ${fieldName}${(schema.required?.includes(fieldName) || fieldSchema.required) ? html`<span style='color:var(--red);'>*</span>` : ''}\n            </div>\n            <div class=\"param-type\">${paramSchema.type}</div>\n          </td>  \n          <td \n            style=\"${fieldType === 'object' ? 'width:100%; padding:0;' : this.allowTry === 'true' ? '' : 'display:none;'} min-width:100px;\" \n            colspan=\"${fieldType === 'object' ? 2 : 1}\">\n            ${fieldType === 'array'\n              ? fieldSchema.items?.format === 'binary'\n                ? html`\n                <div class=\"file-input-container col\" style='align-items:flex-end;' @click=\"${(e) => this.onAddRemoveFileInput(e, fieldName, mimeType)}\">\n                  <div class='input-set row'>\n                    <input \n                      type = \"file\"\n                      part = \"file-input\"\n                      style = \"width:100%\" \n                      data-pname = \"${fieldName}\" \n                      data-ptype = \"${mimeType.includes('form-urlencode') ? 'form-urlencode' : 'form-data'}\"\n                      data-array = \"false\" \n                      data-file-array = \"true\" \n                    />\n                    <button class=\"file-input-remove-btn\"> &#x2715; </button>\n                  </div>  \n                  <button class=\"m-btn primary file-input-add-btn\" part=\"btn btn-fill\" style=\"margin:2px 25px 0 0; padding:2px 6px;\">ADD</button>\n                </div>  \n                `\n                : html`\n                  <tag-input\n                    style = \"width:100%\" \n                    data-ptype = \"${mimeType.includes('form-urlencode') ? 'form-urlencode' : 'form-data'}\"\n                    data-pname = \"${fieldName}\"\n                    data-example = \"${Array.isArray(fieldExamples) ? fieldExamples.join('~|~') : fieldExamples}\"\n                    data-array = \"true\"\n                    placeholder = \"add-multiple &#x21a9;\"\n                    .value = \"${Array.isArray(fieldExamples) ? Array.isArray(fieldExamples[0]) ? fieldExamples[0] : [fieldExamples[0]] : [fieldExamples]}\"\n                  >\n                  </tag-input>\n                `\n              : html`\n                ${fieldType === 'object'\n                  ? this.formDataParamAsObjectTemplate.call(this, fieldName, fieldSchema, mimeType)\n                  : html`\n                    ${this.allowTry === 'true'\n                      ? html`<input\n                          .value = \"${this.fillRequestFieldsWithExample === 'true' ? example.exampleVal : ''}\"\n                          spellcheck = \"false\"\n                          type = \"${fieldSchema.format === 'binary' ? 'file' : fieldSchema.format === 'password' ? 'password' : 'text'}\"\n                          part = \"textbox textbox-param\"\n                          style = \"width:100%\"\n                          data-ptype = \"${mimeType.includes('form-urlencode') ? 'form-urlencode' : 'form-data'}\"\n                          data-pname = \"${fieldName}\"\n                          data-example = \"${Array.isArray(fieldExamples) ? fieldExamples[0] : fieldExamples}\"\n                          data-array = \"false\"\n                        />`\n                      : ''\n                    }\n                    `\n                  }`\n              }\n          </td>\n          ${fieldType === 'object'\n            ? ''\n            : html`\n              <td>\n                ${paramSchema.default || paramSchema.constrain || paramSchema.allowedValues || paramSchema.pattern\n                  ? html`\n                    <div class=\"param-constraint\">\n                      ${paramSchema.default ? html`<span style=\"font-weight:bold\">Default: </span>${paramSchema.default}<br/>` : ''}\n                      ${paramSchema.pattern ? html`<span style=\"font-weight:bold\">Pattern: </span>${paramSchema.pattern}<br/>` : ''}\n                      ${paramSchema.constrain ? html`${paramSchema.constrain}<br/>` : ''}\n                      ${paramSchema.allowedValues && paramSchema.allowedValues.split('┃').map((v, i) => html`\n                        ${i > 0 ? '┃' : html`<span style=\"font-weight:bold\">Allowed: </span>`}\n                        ${html`\n                          <a part=\"anchor anchor-param-constraint\" class = \"${this.allowTry === 'true' ? '' : 'inactive-link'}\"\n                            data-type=\"${paramSchema.type === 'array' ? paramSchema.type : 'string'}\"\n                            data-enum=\"${v.trim()}\"\n                            @click=\"${(e) => {\n                              const inputEl = e.target.closest('table').querySelector(`[data-pname=\"${fieldName}\"]`);\n                              if (inputEl) {\n                                if (e.target.dataset.type === 'array') {\n                                  inputEl.value = [e.target.dataset.enum];\n                                } else {\n                                  inputEl.value = e.target.dataset.enum;\n                                }\n                              }\n                            }}\"\n                          > \n                            ${v} \n                          </a>`\n                        }`)\n                      }\n                    </div>`\n                  : ''\n                }\n              </td>`\n          }\n        </tr>\n        ${fieldType === 'object'\n          ? ''\n          : html`\n            <tr>\n              <td style=\"border:none\"> </td>\n              <td colspan=\"2\" style=\"border:none; margin-top:0; padding:0 5px 8px 5px;\"> \n                <span class=\"m-markdown-small\">${unsafeHTML(marked(fieldSchema.description || ''))}</span>\n                ${this.exampleListTemplate.call(this, fieldName, paramSchema.type, example.exampleList)}\n              </td>\n            </tr>\n          `\n        }`);\n      }\n      return html`\n        <table role=\"presentation\" style=\"width:100%;\" class=\"m-table\">\n          ${formDataTableRows}\n        </table>\n      `;\n    }\n\n    return html`\n      <textarea\n        class = \"textarea dynamic-form-param ${mimeType}\"\n        part = \"textarea textarea-param\"\n        spellcheck = \"false\"\n        data-pname=\"dynamic-form\" \n        data-ptype=\"${mimeType}\"\n        .textContent = \"${exampleValue}\"\n        style=\"width:100%\"\n      ></textarea>\n      ${schema.description ? html`<span class=\"m-markdown-small\">${unsafeHTML(marked(schema.description))}</span>` : ''}\n    `;\n  }\n\n  curlSyntaxTemplate(display = 'flex') {\n    return html`\n      <div class=\"col m-markdown\" style=\"flex:1; display:${display}; position:relative; max-width: 100%;\">\n        <button  class=\"toolbar-btn\" style = \"position:absolute; top:12px; right:8px\" @click='${(e) => { copyToClipboard(this.curlSyntax.replace(/\\\\$/, ''), e); }}' part=\"btn btn-fill\"> Copy </button>\n        <pre style=\"white-space:pre\"><code>${unsafeHTML(Prism.highlight(this.curlSyntax.trim().replace(/\\\\$/, ''), Prism.languages.shell, 'shell'))}</code></pre>\n      </div>\n      `;\n  }\n\n  apiResponseTabTemplate() {\n    let responseFormat = '';\n    let responseContent = '';\n    if (!this.responseIsBlob) {\n      if (this.responseHeaders.includes('application/x-ndjson')) {\n        responseFormat = 'json';\n        const prismLines = this.responseText.split('\\n').map((q) => Prism.highlight(q, Prism.languages[responseFormat], responseFormat)).join('\\n');\n        responseContent = html`<code>${unsafeHTML(prismLines)}</code>`;\n      } else if (this.responseHeaders.includes('json')) {\n        responseFormat = 'json';\n        responseContent = html`<code>${unsafeHTML(Prism.highlight(this.responseText, Prism.languages[responseFormat], responseFormat))}</code>`;\n      } else if (this.responseHeaders.includes('html') || this.responseHeaders.includes('xml')) {\n        responseFormat = 'html';\n        responseContent = html`<code>${unsafeHTML(Prism.highlight(this.responseText, Prism.languages[responseFormat], responseFormat))}</code>`;\n      } else {\n        responseFormat = 'text';\n        responseContent = html`<code>${this.responseText}</code>`;\n      }\n    }\n    return html`\n      <div class=\"row\" style=\"font-size:var(--font-size-small); margin:5px 0\">\n        <div class=\"response-message ${this.responseStatus}\">Response Status: ${this.responseMessage}</div>\n        <div style=\"flex:1\"></div>\n        <button class=\"m-btn\" part=\"btn btn-outline btn-clear-response\" @click=\"${this.clearResponseData}\">CLEAR RESPONSE</button>\n      </div>\n      <div class=\"tab-panel col\" style=\"border-width:0 0 1px 0;\">\n        <div id=\"tab_buttons\" class=\"tab-buttons row\" @click=\"${(e) => {\n            if (e.target.classList.contains('tab-btn') === false) { return; }\n            this.activeResponseTab = e.target.dataset.tab;\n        }}\">\n          <button class=\"tab-btn ${this.activeResponseTab === 'response' ? 'active' : ''}\" data-tab = 'response' > RESPONSE</button>\n          <button class=\"tab-btn ${this.activeResponseTab === 'headers' ? 'active' : ''}\"  data-tab = 'headers' > RESPONSE HEADERS</button>\n          ${this.showCurlBeforeTry === 'true'\n            ? ''\n            : html`<button class=\"tab-btn ${this.activeResponseTab === 'curl' ? 'active' : ''}\" data-tab = 'curl'>CURL</button>`}\n        </div>\n        ${this.responseIsBlob\n          ? html`\n            <div class=\"tab-content col\" style=\"flex:1; display:${this.activeResponseTab === 'response' ? 'flex' : 'none'};\">\n              <button class=\"m-btn thin-border mar-top-8\" style=\"width:135px\" @click='${(e) => { downloadResource(this.responseBlobUrl, this.respContentDisposition, e); }}' part=\"btn btn-outline\">\n                DOWNLOAD\n              </button>\n              ${this.responseBlobType === 'view'\n                ? html`<button class=\"m-btn thin-border mar-top-8\" style=\"width:135px\"  @click='${(e) => { viewResource(this.responseBlobUrl, e); }}' part=\"btn btn-outline\">VIEW (NEW TAB)</button>`\n                : ''\n              }\n            </div>`\n          : html`\n            <div class=\"tab-content col m-markdown\" style=\"flex:1; display:${this.activeResponseTab === 'response' ? 'flex' : 'none'};\" >\n              <button class=\"toolbar-btn\" style=\"position:absolute; top:12px; right:8px\" @click='${(e) => { copyToClipboard(this.responseText, e); }}' part=\"btn btn-fill\"> Copy </button>\n              <pre style=\"white-space:pre; min-height:50px; height:var(--resp-area-height, 400px); resize:vertical; overflow:auto\">${responseContent}</pre>\n            </div>`\n        }\n        <div class=\"tab-content col m-markdown\" style=\"flex:1; display:${this.activeResponseTab === 'headers' ? 'flex' : 'none'};\" >\n          <button  class=\"toolbar-btn\" style = \"position:absolute; top:12px; right:8px\" @click='${(e) => { copyToClipboard(this.responseHeaders, e); }}' part=\"btn btn-fill\"> Copy </button>\n          <pre style=\"white-space:pre\"><code>${unsafeHTML(Prism.highlight(this.responseHeaders, Prism.languages.css, 'css'))}</code></pre>\n        </div>\n        ${this.showCurlBeforeTry === 'true' ? '' : this.curlSyntaxTemplate(this.activeResponseTab === 'curl' ? 'flex' : 'none')}\n      </div>`;\n  }\n\n  apiCallTemplate() {\n    let selectServerDropdownHtml = '';\n\n    if (this.servers && this.servers.length > 0) {\n      selectServerDropdownHtml = html`\n        <select style=\"min-width:100px;\" @change='${(e) => { this.serverUrl = e.target.value; }}'>\n          ${this.servers.map((v) => html`<option value = \"${v.url}\"> ${v.url} - ${v.description} </option>`)}\n        </select>\n      `;\n    }\n    const selectedServerHtml = html`\n      <div style=\"display:flex; flex-direction:column;\">\n        ${selectServerDropdownHtml}\n        ${this.serverUrl\n          ? html`\n            <div style=\"display:flex; align-items:baseline;\">\n              <div style=\"font-weight:bold; padding-right:5px;\">API Server</div> \n              <span class = \"gray-text\"> ${this.serverUrl} </span>\n            </div>\n          `\n          : ''\n        }\n      </div>  \n    `;\n\n    return html`\n    <div style=\"display:flex; align-items:flex-end; margin:16px 0; font-size:var(--font-size-small);\" part=\"wrap-request-btn\">\n      <div class=\"hide-in-small-screen\" style=\"flex-direction:column; margin:0; width:calc(100% - 60px);\">\n        <div style=\"display:flex; flex-direction:row; align-items:center; overflow:hidden;\"> \n          ${selectedServerHtml}\n        </div>\n        <div style=\"display:flex;\">\n          <div style=\"font-weight:bold; padding-right:5px;\">Authentication</div>\n          ${this.security?.length > 0\n            ? html`\n              ${this.api_keys.length > 0\n                ? html`<div style=\"color:var(--blue); overflow:hidden;\"> \n                    ${this.api_keys.length === 1\n                      ? `${this.api_keys[0]?.typeDisplay} in ${this.api_keys[0].in}`\n                      : `${this.api_keys.length} API keys applied`\n                    } \n                  </div>`\n                : html`<div class=\"gray-text\">Required  <span style=\"color:var(--red)\">(None Applied)</span>`\n              }`\n            : html`<span class=\"gray-text\"> Not Required </span>`\n          }\n        </div>\n      </div>\n      ${\n        this.parameters.length > 0 || this.request_body\n          ? html`\n            <button class=\"m-btn thin-border\" part=\"btn btn-outline btn-fill\" style=\"margin-right:5px;\" @click=\"${this.onFillRequestData}\" title=\"Fills with example data (if provided)\">\n              FILL EXAMPLE\n            </button>\n            <button class=\"m-btn thin-border\" part=\"btn btn-outline btn-clear\" style=\"margin-right:5px;\" @click=\"${this.onClearRequestData}\">\n              CLEAR\n            </button>`\n          : ''\n      }\n      <button class=\"m-btn primary thin-border\" part=\"btn btn-try\" @click=\"${this.onTryClick}\">TRY</button>\n    </div>\n    <div class=\"row\" style=\"font-size:var(--font-size-small); margin:5px 0\">\n      ${this.showCurlBeforeTry === 'true' ? this.curlSyntaxTemplate() : ''}\n    </div>\n    ${this.responseMessage === '' ? '' : this.apiResponseTabTemplate()}\n    `;\n  }\n  /* eslint-enable indent */\n\n  async onFillRequestData(e) {\n    const requestPanelEl = e.target.closest('.request-panel');\n    const requestPanelInputEls = [...requestPanelEl.querySelectorAll('input, tag-input, textarea:not(.is-hidden)')];\n    requestPanelInputEls.forEach((el) => {\n      if (el.dataset.example) {\n        if (el.tagName.toUpperCase() === 'TAG-INPUT') {\n          el.value = el.dataset.example.split('~|~');\n        } else {\n          el.value = el.dataset.example;\n        }\n      }\n    });\n  }\n\n  async onClearRequestData(e) {\n    const requestPanelEl = e.target.closest('.request-panel');\n    const requestPanelInputEls = [...requestPanelEl.querySelectorAll('input, tag-input, textarea:not(.is-hidden)')];\n    requestPanelInputEls.forEach((el) => { el.value = ''; });\n  }\n\n  buildFetchURL(requestPanelEl) {\n    let fetchUrl;\n    const pathParamEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='path']\")];\n    const queryParamEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='query']\")];\n    const queryParamObjTypeEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='query-object']\")];\n    fetchUrl = this.path;\n    // Generate URL using Path Params\n    pathParamEls.map((el) => {\n      fetchUrl = fetchUrl.replace(`{${el.dataset.pname}}`, encodeURIComponent(el.value));\n    });\n\n    // Query Params\n    const urlQueryParamsMap = new Map();\n    const queryParamsWithReservedCharsAllowed = [];\n    if (queryParamEls.length > 0) {\n      queryParamEls.forEach((el) => {\n        const queryParam = new URLSearchParams();\n        if (el.dataset.paramAllowReserved === 'true') {\n          queryParamsWithReservedCharsAllowed.push(el.dataset.pname);\n        }\n        if (el.dataset.array === 'false') {\n          if (el.value !== '') {\n            queryParam.append(el.dataset.pname, el.value);\n          }\n        } else {\n          const { paramSerializeStyle, paramSerializeExplode } = el.dataset;\n          let vals = ((el.value && Array.isArray(el.value)) ? el.value : []);\n          vals = Array.isArray(vals) ? vals.filter((v) => v !== '') : [];\n          if (vals.length > 0) {\n            if (paramSerializeStyle === 'spaceDelimited') {\n              queryParam.append(el.dataset.pname, vals.join(' ').replace(/^\\s|\\s$/g, ''));\n            } else if (paramSerializeStyle === 'pipeDelimited') {\n              queryParam.append(el.dataset.pname, vals.join('|').replace(/^\\||\\|$/g, ''));\n            } else {\n              if (paramSerializeExplode === 'true') { // eslint-disable-line no-lonely-if\n                vals.forEach((v) => { queryParam.append(el.dataset.pname, v); });\n              } else {\n                queryParam.append(el.dataset.pname, vals.join(',').replace(/^,|,$/g, ''));\n              }\n            }\n          }\n        }\n        if (queryParam.toString()) {\n          urlQueryParamsMap.set(el.dataset.pname, queryParam);\n        }\n      });\n    }\n\n    // Query Params (Dynamic - create from JSON)\n    if (queryParamObjTypeEls.length > 0) {\n      queryParamObjTypeEls.map((el) => {\n        const queryParam = new URLSearchParams();\n        try {\n          let queryParamObj = {};\n          const { paramSerializeStyle, paramSerializeExplode } = el.dataset;\n          queryParamObj = Object.assign(queryParamObj, JSON.parse(el.value.replace(/\\s+/g, ' ')));\n          if (el.dataset.paramAllowReserved === 'true') {\n            queryParamsWithReservedCharsAllowed.push(el.dataset.pname);\n          }\n          if ('json xml'.includes(paramSerializeStyle)) {\n            if (paramSerializeStyle === 'json') {\n              queryParam.append(el.dataset.pname, JSON.stringify(queryParamObj));\n            } else if (paramSerializeStyle === 'xml') {\n              queryParam.append(el.dataset.pname, json2xml(queryParamObj));\n            }\n          } else {\n            for (const key in queryParamObj) {\n              if (typeof queryParamObj[key] === 'object') {\n                if (Array.isArray(queryParamObj[key])) {\n                  if (paramSerializeStyle === 'spaceDelimited') {\n                    queryParam.append(key, queryParamObj[key].join(' '));\n                  } else if (paramSerializeStyle === 'pipeDelimited') {\n                    queryParam.append(key, queryParamObj[key].join('|'));\n                  } else {\n                    if (paramSerializeExplode === 'true') { // eslint-disable-line no-lonely-if\n                      queryParamObj[key].forEach((v) => {\n                        queryParam.append(key, v);\n                      });\n                    } else {\n                      queryParam.append(key, queryParamObj[key]);\n                    }\n                  }\n                }\n              } else {\n                queryParam.append(key, queryParamObj[key]);\n              }\n            }\n          }\n        } catch (err) {\n          console.error('RapiDoc: unable to parse %s into object', el.value); // eslint-disable-line no-console\n        }\n        if (queryParam.toString()) {\n          urlQueryParamsMap.set(el.dataset.pname, queryParam);\n        }\n      });\n    }\n    let urlQueryParamString = '';\n    if (urlQueryParamsMap.size) {\n      urlQueryParamsMap.forEach((val, pname) => {\n        if (queryParamsWithReservedCharsAllowed.includes(pname)) {\n          urlQueryParamString += `${pname}=`;\n          urlQueryParamString += val.getAll(pname).join(`&${pname}=`);\n          urlQueryParamString += '&';\n        } else {\n          urlQueryParamString += `${val.toString()}&`;\n        }\n      });\n      urlQueryParamString = urlQueryParamString.slice(0, -1);\n    }\n    if (urlQueryParamString.length !== 0) {\n      fetchUrl = `${fetchUrl}${fetchUrl.includes('?') ? '&' : '?'}${urlQueryParamString}`;\n    }\n\n    // Add authentication Query-Param if provided\n    this.api_keys\n      .filter((v) => (v.in === 'query'))\n      .forEach((v) => {\n        fetchUrl = `${fetchUrl}${fetchUrl.includes('?') ? '&' : '?'}${v.name}=${encodeURIComponent(v.finalKeyValue)}`;\n      });\n\n    fetchUrl = `${this.serverUrl.replace(/\\/$/, '')}${fetchUrl}`;\n    return fetchUrl;\n  }\n\n  buildFetchHeaders(requestPanelEl) {\n    const respEl = this.closest('.expanded-req-resp-container, .req-resp-container')?.getElementsByTagName('api-response')[0];\n    const headerParamEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='header']\")];\n    const requestBodyContainerEl = requestPanelEl.querySelector('.request-body-container');\n    const acceptHeader = respEl?.selectedMimeType;\n    const reqHeaders = new Headers();\n    if (acceptHeader) {\n      // Uses the acceptHeader from Response panel\n      reqHeaders.append('Accept', acceptHeader);\n    } else if (this.accept) {\n      reqHeaders.append('Accept', this.accept);\n    }\n\n    // Add Authentication Header if provided\n    this.api_keys\n      .filter((v) => (v.in === 'header'))\n      .forEach((v) => {\n        reqHeaders.append(v.name, v.finalKeyValue);\n      });\n\n    // Add Header Params\n    headerParamEls.map((el) => {\n      if (el.value) {\n        reqHeaders.append(el.dataset.pname, el.value);\n      }\n    });\n\n    if (requestBodyContainerEl) {\n      const requestBodyType = requestBodyContainerEl.dataset.selectedRequestBodyType;\n      // Common for all request-body\n      if (!requestBodyType.includes('form-data')) {\n        // For multipart/form-data dont set the content-type to allow creation of browser generated part boundaries\n        reqHeaders.append('Content-Type', requestBodyType);\n      }\n    }\n\n    return reqHeaders;\n  }\n\n  buildFetchBodyOptions(requestPanelEl) {\n    const requestBodyContainerEl = requestPanelEl.querySelector('.request-body-container');\n    const fetchOptions = {\n      method: this.method.toUpperCase(),\n    };\n    if (requestBodyContainerEl) {\n      const requestBodyType = requestBodyContainerEl.dataset.selectedRequestBodyType;\n      if (requestBodyType.includes('form-urlencoded')) {\n        // url-encoded Form Params (dynamic) - Parse JSON and generate Params\n        const formUrlDynamicTextAreaEl = requestPanelEl.querySelector(\"[data-ptype='dynamic-form']\");\n        if (formUrlDynamicTextAreaEl) {\n          const val = formUrlDynamicTextAreaEl.value;\n          const formUrlDynParams = new URLSearchParams();\n          let proceed = true;\n          let tmpObj;\n          if (val) {\n            try {\n              tmpObj = JSON.parse(val);\n            } catch (err) {\n              proceed = false;\n              console.warn('RapiDoc: Invalid JSON provided', err); // eslint-disable-line no-console\n            }\n          } else {\n            proceed = false;\n          }\n          if (proceed) {\n            for (const prop in tmpObj) {\n              formUrlDynParams.append(prop, JSON.stringify(tmpObj[prop]));\n            }\n            fetchOptions.body = formUrlDynParams;\n          }\n        } else {\n          // url-encoded Form Params (regular)\n          const formUrlEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='form-urlencode']\")];\n          const formUrlParams = new URLSearchParams();\n          formUrlEls\n            .filter((v) => (v.type !== 'file'))\n            .forEach((el) => {\n              if (el.dataset.array === 'false') {\n                if (el.value) {\n                  formUrlParams.append(el.dataset.pname, el.value);\n                }\n              } else {\n                const vals = (el.value && Array.isArray(el.value)) ? el.value.join(',') : '';\n                formUrlParams.append(el.dataset.pname, vals);\n              }\n            });\n          fetchOptions.body = formUrlParams;\n        }\n      } else if (requestBodyType.includes('form-data')) {\n        const formDataParams = new FormData();\n        const formDataEls = [...requestPanelEl.querySelectorAll(\"[data-ptype='form-data']\")];\n        formDataEls.forEach((el) => {\n          if (el.dataset.array === 'false') {\n            if (el.type === 'file' && el.files[0]) {\n              formDataParams.append(el.dataset.pname, el.files[0], el.files[0].name);\n            } else if (el.value) {\n              formDataParams.append(el.dataset.pname, el.value);\n            }\n          } else if (el.value && Array.isArray(el.value)) {\n            formDataParams.append(el.dataset.pname, el.value.join(','));\n          }\n        });\n        fetchOptions.body = formDataParams;\n      } else if (/^audio\\/|^image\\/|^video\\/|^font\\/|tar$|zip$|7z$|rtf$|msword$|excel$|\\/pdf$|\\/octet-stream$/.test(requestBodyType)) {\n        const bodyParamFileEl = requestPanelEl.querySelector('.request-body-param-file');\n        if (bodyParamFileEl?.files[0]) {\n          fetchOptions.body = bodyParamFileEl.files[0]; // eslint-disable-line prefer-destructuring\n        }\n      } else if (requestBodyType.includes('json') || requestBodyType.includes('xml') || requestBodyType.includes('text')) {\n        const exampleTextAreaEl = requestPanelEl.querySelector('.request-body-param-user-input');\n        if (exampleTextAreaEl?.value) {\n          fetchOptions.body = exampleTextAreaEl.value;\n        }\n      }\n    }\n\n    return fetchOptions;\n  }\n\n  async onTryClick(e) {\n    const tryBtnEl = e.target;\n    const requestPanelEl = tryBtnEl.closest('.request-panel');\n    const fetchUrl = this.buildFetchURL(requestPanelEl);\n    const fetchOptions = this.buildFetchBodyOptions(requestPanelEl);\n    const reqHeaders = this.buildFetchHeaders(requestPanelEl);\n    this.responseUrl = '';\n    this.responseHeaders = [];\n    this.curlSyntax = this.generateCURLSyntax(fetchUrl, reqHeaders, fetchOptions, requestPanelEl);\n    this.responseStatus = 'success';\n    this.responseIsBlob = false;\n\n    this.respContentDisposition = '';\n    if (this.responseBlobUrl) {\n      URL.revokeObjectURL(this.responseBlobUrl);\n      this.responseBlobUrl = '';\n    }\n    if (this.fetchCredentials) {\n      fetchOptions.credentials = this.fetchCredentials;\n    }\n    const controller = new AbortController();\n    const { signal } = controller;\n    fetchOptions.headers = reqHeaders;\n    const tempRequest = { url: fetchUrl, ...fetchOptions };\n    this.dispatchEvent(new CustomEvent('before-try', {\n      bubbles: true,\n      composed: true,\n      detail: {\n        request: tempRequest,\n        controller,\n      },\n    }));\n    const updatedFetchOptions = {\n      method: tempRequest.method,\n      headers: tempRequest.headers,\n      credentials: tempRequest.credentials,\n      body: tempRequest.body,\n    };\n    const fetchRequest = new Request(tempRequest.url, updatedFetchOptions);\n\n    let fetchResponse;\n    let responseClone;\n    try {\n      let respBlob;\n      let respJson;\n      let respText;\n      tryBtnEl.disabled = true;\n      this.responseText = '⌛';\n      this.responseMessage = '';\n      this.requestUpdate();\n      const startTime = performance.now();\n      fetchResponse = await fetch(fetchRequest, { signal });\n      const endTime = performance.now();\n      responseClone = fetchResponse.clone(); // create a response clone to allow reading response body again (response.json, response.text etc)\n      tryBtnEl.disabled = false;\n      this.responseMessage = html`${fetchResponse.statusText ? `${fetchResponse.statusText}:${fetchResponse.status}` : fetchResponse.status} <div style=\"color:var(--light-fg)\"> Took ${Math.round(endTime - startTime)} milliseconds </div>`;\n      this.responseUrl = fetchResponse.url;\n      const respHeadersObj = {};\n      fetchResponse.headers.forEach((hdrVal, hdr) => {\n        respHeadersObj[hdr] = hdrVal;\n        this.responseHeaders = `${this.responseHeaders}${hdr}: ${hdrVal}\\n`;\n      });\n      const contentType = fetchResponse.headers.get('content-type');\n      const respEmpty = (await fetchResponse.clone().text()).length === 0;\n      if (respEmpty) {\n        this.responseText = '';\n      } else if (contentType) {\n        if (contentType === 'application/x-ndjson') {\n          this.responseText = await fetchResponse.text();\n        } else if (contentType.includes('json')) {\n          if ((/charset=[^\"']+/).test(contentType)) {\n            const encoding = contentType.split('charset=')[1];\n            const buffer = await fetchResponse.arrayBuffer();\n            try {\n              respText = new TextDecoder(encoding).decode(buffer);\n            } catch {\n              respText = new TextDecoder('utf-8').decode(buffer);\n            }\n            try {\n              respJson = JSON.parse(respText);\n              this.responseText = JSON.stringify(respJson, null, 2);\n            } catch {\n              this.responseText = respText;\n            }\n          } else {\n            respJson = await fetchResponse.json();\n            this.responseText = JSON.stringify(respJson, null, 2);\n          }\n        // eslint-disable-next-line no-useless-escape\n        } else if (/^font\\/|tar$|zip$|7z$|rtf$|msword$|excel$|\\/pdf$|\\/octet-stream$|^application\\/vnd\\./.test(contentType)) {\n          this.responseIsBlob = true;\n          this.responseBlobType = 'download';\n        } else if (/^audio|^image|^video/.test(contentType)) {\n          this.responseIsBlob = true;\n          this.responseBlobType = 'view';\n        } else {\n          respText = await fetchResponse.text();\n          if (contentType.includes('xml')) {\n            this.responseText = formatXml(respText, { textNodesOnSameLine: true, indentor: '  ' });\n          } else {\n            this.responseText = respText;\n          }\n        }\n        if (this.responseIsBlob) {\n          const contentDisposition = fetchResponse.headers.get('content-disposition');\n          this.respContentDisposition = contentDisposition ? contentDisposition.split('filename=')[1].replace(/\"|'/g, '') : 'filename';\n          respBlob = await fetchResponse.blob();\n          this.responseBlobUrl = URL.createObjectURL(respBlob);\n        }\n      } else {\n        respText = await fetchResponse.text();\n        this.responseText = respText;\n      }\n      this.dispatchEvent(new CustomEvent('after-try', {\n        bubbles: true,\n        composed: true,\n        detail: {\n          request: fetchRequest,\n          response: responseClone,\n          responseHeaders: respHeadersObj,\n          responseBody: respJson || respText || respBlob,\n          responseStatus: responseClone.ok,\n        },\n      }));\n    } catch (err) {\n      tryBtnEl.disabled = false;\n      if (err.name === 'AbortError') {\n        this.dispatchEvent(new CustomEvent('request-aborted', {\n          bubbles: true,\n          composed: true,\n          detail: {\n            err,\n            request: fetchRequest,\n          },\n        }));\n        this.responseMessage = 'Request Aborted';\n        this.responseText = 'Request Aborted';\n      } else {\n        this.dispatchEvent(new CustomEvent('after-try', {\n          bubbles: true,\n          composed: true,\n          detail: {\n            err,\n            request: fetchRequest,\n          },\n        }));\n        this.responseMessage = `${err.message} (CORS or Network Issue)`;\n      }\n    }\n    this.requestUpdate();\n  }\n\n  liveCURLSyntaxUpdate(requestPanelEl) {\n    this.applyCURLSyntax(requestPanelEl);\n    this.requestUpdate();\n  }\n\n  onGenerateCURLClick(e) {\n    const requestPanelEl = this.getRequestPanel(e);\n    this.applyCURLSyntax(requestPanelEl);\n  }\n\n  getRequestPanel(e) {\n    return e.target.closest('.request-panel');\n  }\n\n  applyCURLSyntax(requestPanelEl) {\n    const fetchUrl = this.buildFetchURL(requestPanelEl);\n    const fetchOptions = this.buildFetchBodyOptions(requestPanelEl);\n    const fetchHeaders = this.buildFetchHeaders(requestPanelEl);\n\n    this.curlSyntax = this.generateCURLSyntax(fetchUrl, fetchHeaders, fetchOptions, requestPanelEl);\n  }\n\n  generateCURLSyntax(fetchUrl, fetchHeaders, fetchOptions, requestPanelEl) {\n    let curlUrl;\n    let curl = '';\n    let curlHeaders = '';\n    let curlData = '';\n    let curlForm = '';\n    const requestBodyContainerEl = requestPanelEl.querySelector('.request-body-container');\n\n    if (fetchUrl.startsWith('http') === false) {\n      const url = new URL(fetchUrl, window.location.href);\n      curlUrl = url.href;\n    } else {\n      curlUrl = fetchUrl;\n    }\n\n    curl = `curl -X ${this.method.toUpperCase()} \"${curlUrl}\" \\\\\\n`;\n\n    curlHeaders = Array.from(fetchHeaders).map(([key, value]) => ` -H \"${key}: ${value}\"`).join('\\\\\\n');\n    if (curlHeaders) {\n      curlHeaders = `${curlHeaders} \\\\\\n`;\n    }\n    if (fetchOptions.body instanceof URLSearchParams) {\n      curlData = ` -d ${fetchOptions.body.toString()} \\\\\\n`;\n    } else if (fetchOptions.body instanceof File) {\n      curlData = ` --data-binary @${fetchOptions.body.name} \\\\\\n`;\n    } else if (fetchOptions.body instanceof FormData) {\n      curlForm = Array.from(fetchOptions.body).reduce((aggregator, [key, value]) => {\n        if (value instanceof File) {\n          return [...aggregator, ` -F \"${key}=@${value.name}\"`];\n        }\n\n        const multiple = value.match(/([^,],)/gm);\n\n        if (multiple) {\n          const multipleResults = multiple.map((one) => `-F \"${key}[]=${one}\"`);\n\n          return [...aggregator, ...multipleResults];\n        }\n\n        return [...aggregator, ` -F \"${key}=${value}\"`];\n      }, []).join('\\\\\\n');\n    } else if (requestBodyContainerEl && requestBodyContainerEl.dataset.selectedRequestBodyType) {\n      const requestBodyType = requestBodyContainerEl.dataset.selectedRequestBodyType;\n      const exampleTextAreaEl = requestPanelEl.querySelector('.request-body-param-user-input');\n      if (exampleTextAreaEl?.value) {\n        fetchOptions.body = exampleTextAreaEl.value;\n        if (requestBodyType.includes('json')) {\n          try {\n            curlData = ` -d '${JSON.stringify(JSON.parse(exampleTextAreaEl.value))}' \\\\\\n`;\n          } catch (err) {\n            // Ignore.\n          }\n        }\n        if (!curlData) {\n          curlData = ` -d '${exampleTextAreaEl.value.replace(/'/g, '\\'\"\\'\"\\'')}' \\\\\\n`;\n        }\n      }\n    }\n\n    return `${curl}${curlHeaders}${curlData}${curlForm}`;\n  }\n\n  onAddRemoveFileInput(e, pname, ptype) {\n    if (e.target.tagName.toLowerCase() !== 'button') {\n      return;\n    }\n\n    if (e.target.classList.contains('file-input-remove-btn')) {\n      // Remove File Input Set\n      const el = e.target.closest('.input-set');\n      el.remove();\n      return;\n    }\n    const el = e.target.closest('.file-input-container');\n\n    // Add File Input Set\n\n    // Container\n    const newInputContainerEl = document.createElement('div');\n    newInputContainerEl.setAttribute('class', 'input-set row');\n\n    // File Input\n    const newInputEl = document.createElement('input');\n    newInputEl.type = 'file';\n    newInputEl.style = 'width:200px; margin-top:2px;';\n    newInputEl.setAttribute('data-pname', pname);\n    newInputEl.setAttribute('data-ptype', ptype.includes('form-urlencode') ? 'form-urlencode' : 'form-data');\n    newInputEl.setAttribute('data-array', 'false');\n    newInputEl.setAttribute('data-file-array', 'true');\n\n    // Remover Button\n    const newRemoveBtnEl = document.createElement('button');\n    newRemoveBtnEl.setAttribute('class', 'file-input-remove-btn');\n    newRemoveBtnEl.innerHTML = '&#x2715;';\n\n    newInputContainerEl.appendChild(newInputEl);\n    newInputContainerEl.appendChild(newRemoveBtnEl);\n    el.insertBefore(newInputContainerEl, e.target);\n    // el.appendChild(newInputContainerEl);\n  }\n\n  clearResponseData() {\n    this.responseUrl = '';\n    this.responseHeaders = '';\n    this.responseText = '';\n    this.responseStatus = 'success';\n    this.responseMessage = '';\n    this.responseIsBlob = false;\n    this.responseBlobType = '';\n    this.respContentDisposition = '';\n    if (this.responseBlobUrl) {\n      URL.revokeObjectURL(this.responseBlobUrl);\n      this.responseBlobUrl = '';\n    }\n  }\n\n  disconnectedCallback() {\n    this.curlSyntax = '';\n    // Cleanup ObjectURL for the blob data if this component created one\n    if (this.responseBlobUrl) {\n      URL.revokeObjectURL(this.responseBlobUrl);\n      this.responseBlobUrl = '';\n    }\n    super.disconnectedCallback();\n  }\n}\n\n// Register the element with the browser\ncustomElements.define('api-request', ApiRequest);\n", "import { LitElement, html, css } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport FontStyles from '~/styles/font-styles';\nimport SchemaStyles from '~/styles/schema-styles';\nimport CustomStyles from '~/styles/custom-styles';\n\nexport default class SchemaTable extends LitElement {\n  static get properties() {\n    return {\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n      schemaHideReadOnly: { type: String, attribute: 'schema-hide-read-only' },\n      schemaHideWriteOnly: { type: String, attribute: 'schema-hide-write-only' },\n      data: { type: Object },\n    };\n  }\n\n  connectedCallback() {\n    super.connectedCallback();\n    if (!this.schemaExpandLevel || this.schemaExpandLevel < 1) { this.schemaExpandLevel = 99999; }\n    if (!this.schemaDescriptionExpanded || !'true false'.includes(this.schemaDescriptionExpanded)) { this.schemaDescriptionExpanded = 'false'; }\n    if (!this.schemaHideReadOnly || !'true false'.includes(this.schemaHideReadOnly)) { this.schemaHideReadOnly = 'true'; }\n    if (!this.schemaHideWriteOnly || !'true false'.includes(this.schemaHideWriteOnly)) { this.schemaHideWriteOnly = 'true'; }\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      SchemaStyles,\n      css`\n      .table {\n        font-size: var(--font-size-small);\n        text-align: left;\n        line-height: calc(var(--font-size-small) + 6px);\n      }\n      .table .tr {\n        width: calc(100% - 5px);\n        padding: 0 0 0 5px;\n        border-bottom: 1px dotted var(--light-border-color);\n      }\n      .table .td {\n        padding: 4px 0;\n      }\n      .table .key {\n        width: 240px;\n      }\n      .key .key-label {\n        font-size: var(--font-size-mono);\n      }\n      .key.deprecated .key-label {\n        color: var(--red);\n      }\n\n      .table .key-type {\n        white-space: normal;\n        width: 150px;\n      }\n      .collapsed-all-descr .tr:not(.expanded-descr) {\n        max-height: calc(var(--font-size-small) + var(--font-size-small));\n      }\n\n      .obj-toggle {\n        padding: 0 2px;\n        border-radius:2px;\n        border: 1px solid transparent;\n        display: inline-block;\n        margin-left: -16px;\n        color:var(--primary-color);\n        cursor:pointer;\n        font-size: calc(var(--font-size-small) + 4px);\n        font-family: var(--font-mono);\n        background-clip: border-box;\n      }\n      .obj-toggle:hover {\n        border-color: var(--primary-color);\n      }\n      .tr.expanded + .object-body {\n        display:block;\n      }\n      .tr.collapsed + .object-body {\n        display:none;\n      }`,\n      CustomStyles,\n    ];\n  }\n\n  /* eslint-disable indent */\n  render() {\n    return html`\n      <div class=\"table ${this.schemaDescriptionExpanded === 'true' ? 'expanded-all-descr' : 'collapsed-all-descr'}\" @click=\"${(e) => this.handleAllEvents(e)}\">\n        <div class='toolbar'>\n          <div class=\"toolbar-item schema-root-type ${this.data?.['::type'] || ''} \"> ${this.data?.['::type'] || ''} </div>\n          ${this.allowSchemaDescriptionExpandToggle === 'true'\n            ? html`\n              <div style=\"flex:1\"></div>\n              <div part=\"schema-multiline-toggle\" class='toolbar-item schema-multiline-toggle' > \n                ${this.schemaDescriptionExpanded === 'true' ? 'Single line description' : 'Multiline description'}\n              </div>\n            `\n            : ''\n          }\n        </div>\n        <span part=\"schema-description\" class='m-markdown'> ${unsafeHTML(marked(this.data?.['::description'] || ''))} </span>\n        <div style = 'border:1px solid var(--light-border-color)'>\n          <div style='display:flex; background-color: var(--bg2); padding:8px 4px; border-bottom:1px solid var(--light-border-color);'>\n            <div class='key' style='font-family:var(--font-regular); font-weight:bold; color:var(--fg);'> Field </div>\n            <div class='key-type' style='font-family:var(--font-regular); font-weight:bold; color:var(--fg);'> Type </div>\n            <div class='key-descr' style='font-family:var(--font-regular); font-weight:bold; color:var(--fg);'> Description </div>\n          </div>\n          ${this.data\n            ? html`\n              ${this.generateTree(\n                this.data['::type'] === 'array' ? this.data['::props'] : this.data,\n                this.data['::type'],\n                this.data['::array-type'],\n              )}`\n            : ''\n          }  \n        </div>\n      </div>  \n    `;\n  }\n\n  generateTree(data, dataType = 'object', arrayType = '', key = '', description = '', schemaLevel = 0, indentLevel = 0, readOrWrite = '') {\n    if (this.schemaHideReadOnly === 'true') {\n      if (dataType === 'array') {\n        if (readOrWrite === 'readonly') {\n          return;\n        }\n      }\n      if (data && data['::readwrite'] === 'readonly') {\n        return;\n      }\n    }\n    if (this.schemaHideWriteOnly === 'true') {\n      if (dataType === 'array') {\n        if (readOrWrite === 'writeonly') {\n          return;\n        }\n      }\n      if (data && data['::readwrite'] === 'writeonly') {\n        return;\n      }\n    }\n    if (!data) {\n      return html`<div class=\"null\" style=\"display:inline;\">\n        <span style='margin-left:${(schemaLevel + 1) * 16}px'> &nbsp; </span>\n        <span class=\"key-label xxx-of-key\"> ${key.replace('::OPTION~', '')}</span>\n        ${\n          dataType === 'array'\n            ? html`<span class='mono-font'> [ ] </span>`\n            : dataType === 'object'\n              ? html`<span class='mono-font'> { } </span>`\n              : html`<span class='mono-font'> schema undefined </span>`\n        }\n      </div>`;\n    }\n\n    const newSchemaLevel = data['::type']?.startsWith('xxx-of') ? schemaLevel : (schemaLevel + 1);\n    const newIndentLevel = dataType === 'xxx-of-option' || data['::type'] === 'xxx-of-option' || key.startsWith('::OPTION') ? indentLevel : (indentLevel + 1);\n    const leftPadding = 16 * newIndentLevel; // 2 space indentation at each level\n    if (Object.keys(data).length === 0) {\n      return html`<span class=\"td key object\" style='padding-left:${leftPadding}px'>${key}</span>`;\n    }\n    let keyLabel = '';\n    let keyDescr = '';\n    let isOneOfLabel = false;\n    if (key.startsWith('::ONE~OF') || key.startsWith('::ANY~OF')) {\n      keyLabel = key.replace('::', '').replace('~', ' ');\n      isOneOfLabel = true;\n    } else if (key.startsWith('::OPTION')) {\n      const parts = key.split('~');\n      keyLabel = parts[1]; // eslint-disable-line prefer-destructuring\n      keyDescr = parts[2]; // eslint-disable-line prefer-destructuring\n    } else {\n      keyLabel = key;\n    }\n\n    let detailObjType = '';\n    if (data['::type'] === 'object') {\n      if (dataType === 'array') {\n        detailObjType = 'array of object'; // Array of Object\n      } else {\n        detailObjType = data['::dataTypeLabel'] || data['::type'];\n      }\n    } else if (data['::type'] === 'array') {\n      if (dataType === 'array') {\n        // detailObjType = 'array of array'; // Array of array\n        detailObjType = `array of array ${arrayType !== 'object' ? `of ${arrayType}` : ''}`; // Array of array\n      } else {\n        detailObjType = data['::dataTypeLabel'] || data['::type'];\n      }\n    }\n\n    if (typeof data === 'object') {\n      return html`\n        ${newSchemaLevel >= 0 && key\n          ? html`\n            <div class='tr ${newSchemaLevel <= this.schemaExpandLevel ? 'expanded' : 'collapsed'} ${data['::type']}' data-obj='${keyLabel}' title=\"${data['::deprecated'] ? 'Deprecated' : ''}\">\n              <div class=\"td key ${data['::deprecated'] ? 'deprecated' : ''}\" style='padding-left:${leftPadding}px'>\n                ${(keyLabel || keyDescr)\n                  ? html`\n                    <span class='obj-toggle ${newSchemaLevel < this.schemaExpandLevel ? 'expanded' : 'collapsed'}' data-obj='${keyLabel}'>\n                      ${schemaLevel < this.schemaExpandLevel ? '-' : '+'}\n                    </span>`\n                  : ''\n                }\n                ${data['::type'] === 'xxx-of-option' || data['::type'] === 'xxx-of-array' || key.startsWith('::OPTION')\n                  ? html`<span class=\"xxx-of-key\" style=\"margin-left:-6px\">${keyLabel}</span><span class=\"${isOneOfLabel ? 'xxx-of-key' : 'xxx-of-descr'}\">${keyDescr}</span>`\n                  : keyLabel.endsWith('*')\n                    ? html`<span class=\"key-label\" style=\"display:inline-block; margin-left:-6px;\">${data['::deprecated'] ? '✗' : ''} ${keyLabel.substring(0, keyLabel.length - 1)}</span><span style='color:var(--red);'>*</span>`\n                    : html`<span class=\"key-label\" style=\"display:inline-block; margin-left:-6px;\">${data['::deprecated'] ? '✗' : ''} ${keyLabel === '::props' ? '' : keyLabel}</span>`\n                }\n                ${data['::type'] === 'xxx-of' && dataType === 'array' ? html`<span style=\"color:var(--primary-color)\">ARRAY</span>` : ''} \n              </div>\n              <div class='td key-type' title=\"${data['::readwrite'] === 'readonly' ? 'Read-Only' : data['::readwrite'] === 'writeonly' ? 'Write-Only' : ''}\">\n                ${(data['::type'] || '').includes('xxx-of') ? '' : detailObjType}\n                ${data['::readwrite'] === 'readonly' ? ' 🆁' : data['::readwrite'] === 'writeonly' ? ' 🆆' : ''}\n              </div>\n              <div class='td key-descr m-markdown-small' style='line-height:1.7'>${unsafeHTML(marked(description || ''))}</div>\n            </div>`\n          : html`\n            ${data['::type'] === 'array' && dataType === 'array'\n              ? html`\n                <div class='tr'> \n                  <div class='td key'></div> \n                  <div class='td key-type'>\n                    ${arrayType && arrayType !== 'object' ? `${dataType} of ${arrayType}` : dataType}\n                  </div> \n                  <div class='td key-descr'></div> \n                </div>`\n              : ''\n            }`\n        }\n        <div class='object-body'>\n        ${Array.isArray(data) && data[0]\n          ? html`${this.generateTree(data[0], 'xxx-of-option', '', '::ARRAY~OF', '', newSchemaLevel, newIndentLevel, '')}`\n          : html`\n            ${Object.keys(data).map((dataKey) => html`\n              ${['::title', '::description', '::type', '::props', '::deprecated', '::array-type', '::readwrite', '::dataTypeLabel'].includes(dataKey)\n                ? data[dataKey]['::type'] === 'array' || data[dataKey]['::type'] === 'object'\n                  ? html`${this.generateTree(\n                    data[dataKey]['::type'] === 'array' ? data[dataKey]['::props'] : data[dataKey],\n                      data[dataKey]['::type'],\n                      data[dataKey]['::array-type'] || '',\n                      dataKey,\n                      data[dataKey]['::description'],\n                      newSchemaLevel,\n                      newIndentLevel,\n                      data[dataKey]['::readwrite'] ? data[dataKey]['::readwrite'] : '',\n                    )}`\n                  : ''\n                : html`${this.generateTree(\n                  data[dataKey]['::type'] === 'array' ? data[dataKey]['::props'] : data[dataKey],\n                  data[dataKey]['::type'],\n                  data[dataKey]['::array-type'] || '',\n                  dataKey,\n                  data[dataKey]?.['::description'] || '',\n                  newSchemaLevel,\n                  newIndentLevel,\n                  data[dataKey]['::readwrite'] ? data[dataKey]['::readwrite'] : '',\n                )}`\n              }\n            `)}\n          `\n        }\n        <div>\n      `;\n    }\n\n    // For Primitive Data types\n    // eslint-disable-next-line no-unused-vars\n    const [type, readOrWriteOnly, constraint, defaultValue, allowedValues, pattern, schemaDescription, schemaTitle, deprecated] = data.split('~|~');\n    if (readOrWriteOnly === '🆁' && this.schemaHideReadOnly === 'true') {\n      return;\n    }\n    if (readOrWriteOnly === '🆆' && this.schemaHideWriteOnly === 'true') {\n      return;\n    }\n    const dataTypeCss = type.replace(/┃.*/g, '').replace(/[^a-zA-Z0-9+]/g, '').substring(0, 4).toLowerCase();\n    const descrExpander = `${constraint || defaultValue || allowedValues || pattern ? '<span class=\"descr-expand-toggle\">➔</span>' : ''}`;\n    let dataTypeHtml = '';\n    if (dataType === 'array') {\n      dataTypeHtml = html` \n        <div class='td key-type ${dataTypeCss}' title=\"${readOrWrite === 'readonly' ? 'Read-Only' : readOrWriteOnly === 'writeonly' ? 'Write-Only' : ''}\">\n          [${type}] ${readOrWrite === 'readonly' ? '🆁' : readOrWrite === 'writeonly' ? '🆆' : ''}\n        </div>`;\n    } else {\n      dataTypeHtml = html` \n        <div class='td key-type ${dataTypeCss}' title=\"${readOrWriteOnly === '🆁' ? 'Read-Only' : readOrWriteOnly === '🆆' ? 'Write-Only' : ''}\">\n          ${type} ${readOrWriteOnly}\n        </div>`;\n    }\n    return html`\n      <div class = \"tr primitive\" title=\"${deprecated ? 'Deprecated' : ''}\">\n        <div class=\"td key ${deprecated}\" style='padding-left:${leftPadding}px'>\n          ${deprecated ? html`<span style='color:var(--red);'>✗</span>` : ''}\n          ${keyLabel?.endsWith('*')\n            ? html`\n              <span class=\"key-label\">${keyLabel.substring(0, keyLabel.length - 1)}</span>\n              <span style='color:var(--red);'>*</span>`\n            : key.startsWith('::OPTION')\n              ? html`<span class='xxx-of-key'>${keyLabel}</span><span class=\"xxx-of-descr\">${keyDescr}</span>`\n              : html`${keyLabel ? html`<span class=\"key-label\"> ${keyLabel}</span>` : html`<span class=\"xxx-of-descr\">${schemaTitle}</span>`}`\n          }\n        </div>\n        ${dataTypeHtml}\n        <div class='td key-descr' style='font-size: var(--font-size-small)'>\n          ${html`<span class=\"m-markdown-small\">\n            ${unsafeHTML(marked(dataType === 'array'\n              ? `${descrExpander} ${description}`\n              : schemaTitle\n                ? `${descrExpander} <b>${schemaTitle}:</b> ${schemaDescription}`\n                : `${descrExpander} ${schemaDescription}`))}\n          </span>`\n          }\n          ${constraint ? html`<div class='' style='display:inline-block; line-break:anywhere; margin-right:8px;'> <span class='bold-text'>Constraints: </span> ${constraint}</div>` : ''}\n          ${defaultValue ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px;'> <span class='bold-text'>Default: </span>${defaultValue}</div>` : ''}\n          ${allowedValues ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px;'> <span class='bold-text'>${type === 'const' ? 'Value' : 'Allowed'}: </span>${allowedValues}</div>` : ''}\n          ${pattern ? html`<div style='display:inline-block; line-break:anywhere; margin-right:8px;'> <span class='bold-text'>Pattern: </span>${pattern}</div>` : ''}\n        </div>\n      </div>\n    `;\n  }\n  /* eslint-enable indent */\n\n  handleAllEvents(e) {\n    if (e.target.classList.contains('obj-toggle')) {\n      this.toggleObjectExpand(e);\n    } else if (e.target.classList.contains('schema-multiline-toggle')) {\n      this.schemaDescriptionExpanded = (this.schemaDescriptionExpanded === 'true' ? 'false' : 'true');\n    } else if (e.target.classList.contains('descr-expand-toggle')) {\n      const trEl = e.target.closest('.tr');\n      if (trEl) {\n        trEl.classList.toggle('expanded-descr');\n        trEl.style.maxHeight = trEl.scrollHeight;\n      }\n    }\n  }\n\n  toggleObjectExpand(e) {\n    const rowEl = e.target.closest('.tr');\n    if (rowEl.classList.contains('expanded')) {\n      rowEl.classList.add('collapsed');\n      rowEl.classList.remove('expanded');\n      e.target.innerText = '+';\n    } else {\n      rowEl.classList.remove('collapsed');\n      rowEl.classList.add('expanded');\n      e.target.innerText = '-';\n    }\n  }\n}\ncustomElements.define('schema-table', SchemaTable);\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport { rapidocApiKey } from '~/utils/common-utils';\nimport { pathSecurityTemplate } from '~/templates/security-scheme-template';\nimport codeSamplesTemplate from '~/templates/code-samples-template';\nimport callbackTemplate from '~/templates/callback-template';\nimport '~/components/api-request';\nimport '~/components/api-response';\n\n/* eslint-disable indent */\nfunction headingRenderer(tagElementId) {\n  const renderer = new marked.Renderer();\n  renderer.heading = ((text, level, raw, slugger) => `<h${level} class=\"observe-me\" id=\"${tagElementId}--${slugger.slug(raw)}\">${text}</h${level}>`);\n  return renderer;\n}\n\nfunction expandCollapseTagDescription(e) {\n  const tagDescriptionEl = e.target.closest('.tag-container').querySelector('.tag-description');\n  const tagIconEl = e.target.closest('.tag-container').querySelector('.tag-icon');\n  if (tagDescriptionEl && tagIconEl) {\n    const isExpanded = tagDescriptionEl.classList.contains('expanded');\n    if (isExpanded) {\n      tagDescriptionEl.style.maxHeight = 0;\n      tagDescriptionEl.classList.replace('expanded', 'collapsed');\n      tagIconEl.classList.replace('expanded', 'collapsed');\n    } else {\n      tagDescriptionEl.style.maxHeight = `${tagDescriptionEl.scrollHeight}px`;\n      tagDescriptionEl.classList.replace('collapsed', 'expanded');\n      tagIconEl.classList.replace('collapsed', 'expanded');\n    }\n  }\n}\n\nexport function expandedEndpointBodyTemplate(path, tagName = '', tagDescription = '') {\n  const acceptContentTypes = new Set();\n  for (const respStatus in path.responses) {\n    for (const acceptContentType in (path.responses[respStatus]?.content)) {\n      acceptContentTypes.add(acceptContentType.trim());\n    }\n  }\n  const accept = [...acceptContentTypes].join(', ');\n\n  // Filter API Keys that are non-empty and are applicable to the the path\n  const nonEmptyApiKeys = this.resolvedSpec.securitySchemes.filter((v) => (v.finalKeyValue && path.security?.some((ps) => (v.securitySchemeId in ps)))) || [];\n\n  // If a RapiDoc API Key is specified on the element and its value is not hyphen(-) then include it for all paths\n  const rapiDocApiKey = this.resolvedSpec.securitySchemes.find((v) => (v.securitySchemeId === rapidocApiKey && v.value !== '-'));\n  if (rapiDocApiKey) {\n    nonEmptyApiKeys.push(rapiDocApiKey);\n  }\n\n  const codeSampleTabPanel = path.xCodeSamples ? codeSamplesTemplate.call(this, path.xCodeSamples) : '';\n  return html`\n    ${this.renderStyle === 'read' ? html`<div class='divider' part=\"operation-divider\"></div>` : ''}\n    <div class='expanded-endpoint-body observe-me ${path.method} ${path.deprecated ? 'deprecated' : ''} ' part=\"section-operation ${path.elementId}\" id='${path.elementId}'>\n      ${(this.renderStyle === 'focused' && tagName !== 'General ⦂')\n        ? html`\n          <div class=\"tag-container\" part=\"section-operation-tag\"> \n            <span class=\"upper\" style=\"font-weight:bold; font-size:18px;\"> ${tagName} </span>\n            ${tagDescription\n              ? html`\n                <svg class=\"tag-icon collapsed\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" stroke-width=\"2\" fill=\"none\" style=\"stroke:var(--primary-color); vertical-align:top; cursor:pointer\"\n                @click=\"${(e) => { expandCollapseTagDescription.call(this, e); }}\"\n                >\n                  <path d=\"M12 20h-6a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h8\"></path><path d=\"M18 4v17\"></path><path d=\"M15 18l3 3l3 -3\"></path>\n                </svg>\n                <div class=\"tag-description collapsed\" style=\"max-height:0px; overflow:hidden; margin-top:16px; border:1px solid var(--border-color)\"> \n                  <div class=\"m-markdown\" style=\"padding:8px\"> ${unsafeHTML(marked(tagDescription))}</div>  \n                </div>`\n              : ''\n            }  \n          </div>\n        `\n        : ''\n      }\n      ${path.deprecated ? html`<div class=\"bold-text red-text\"> DEPRECATED </div>` : ''}\n      ${html`\n        ${path.xBadges && path.xBadges?.length > 0\n          ? html`\n            <div style=\"display:flex; flex-wrap:wrap; margin-bottom: -24px; font-size: var(--font-size-small);\">\n              ${path.xBadges.map((v) => (\n                  html`<span style=\"margin:1px; margin-right:5px; padding:1px 8px; font-weight:bold; border-radius:12px;  background-color: var(--light-${v.color}, var(--input-bg)); color:var(--${v.color}); border:1px solid var(--${v.color})\">${v.label}</span>`\n                ))\n              }\n            </div>\n            `\n          : ''\n        }\n        <h2 part=\"section-operation-summary\"> ${path.shortSummary || `${path.method.toUpperCase()} ${path.path}`}</h2>\n        ${path.isWebhook\n          ? html`<span part=\"section-operation-webhook\" style=\"color:var(--primary-color); font-weight:bold; font-size: var(--font-size-regular);\"> WEBHOOK </span>`\n          : html`\n            <div part=\"section-operation-webhook-method\" class=\"mono-font regular-font-size\" style=\"text-align:left; direction:ltr; padding: 8px 0; color:var(--fg3)\"> \n              <span part=\"label-operation-method\" class=\"regular-font upper method-fg bold-text ${path.method}\">${path.method}</span> \n              <span part=\"label-operation-path\">${path.path}</span>\n            </div>\n          `\n        }\n        <slot name=\"${path.elementId}\"></slot>`\n      }\n      ${path.description ? html`<div class=\"m-markdown\"> ${unsafeHTML(marked(path.description))}</div>` : ''}\n      ${pathSecurityTemplate.call(this, path.security)}\n      ${path.externalDocs?.url || path.externalDocs?.description\n        ? html`<div style=\"background-color:var(--bg3); padding:2px 8px 8px 8px; margin:8px 0; border-radius:var(--border-radius)\"> \n            <div class=\"m-markdown\"> ${unsafeHTML(marked(path.externalDocs?.description || ''))} </div>\n            ${path.externalDocs?.url\n              ? html`<a style=\"font-family:var(--font-mono); font-size:var(--font-size-small)\" href=\"${path.externalDocs?.url}\" target=\"_blank\">\n                  ${path.externalDocs?.url} <div style=\"transform: rotate(270deg) scale(1.5); display: inline-block; margin-left:5px\">⇲</div>\n                </a>`\n              : ''\n            }\n          </div>`\n        : ''\n      }\n      ${codeSampleTabPanel}\n      <div class='expanded-req-resp-container'>\n        <api-request\n          class = \"${this.renderStyle}-mode\"\n          style = \"width:100%;\"\n          webhook = \"${path.isWebhook}\"\n          method = \"${path.method}\"\n          path = \"${path.path}\"\n          .security = \"${path.security}\"\n          .parameters = \"${path.parameters}\"\n          .request_body = \"${path.requestBody}\"\n          .api_keys = \"${nonEmptyApiKeys}\"\n          .servers = \"${path.servers}\"\n          server-url = \"${path.servers?.[0]?.url || this.selectedServer.computedUrl}\"\n          fill-request-fields-with-example = \"${this.fillRequestFieldsWithExample}\"\n          allow-try = \"${this.allowTry}\"\n          show-curl-before-try = \"${this.showCurlBeforeTry}\"\n          accept = \"${accept}\"\n          render-style=\"${this.renderStyle}\" \n          schema-style = \"${this.schemaStyle}\"\n          active-schema-tab = \"${this.defaultSchemaTab}\"\n          schema-expand-level = \"${this.schemaExpandLevel}\"\n          schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n          allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n          schema-hide-read-only = \"${this.schemaHideReadOnly === 'never' ? 'false' : path.isWebhook ? 'false' : 'true'}\"\n          schema-hide-write-only = \"${this.schemaHideWriteOnly === 'never' ? 'false' : path.isWebhook ? 'true' : 'false'}\"\n          fetch-credentials = \"${this.fetchCredentials}\"\n          exportparts = \"wrap-request-btn:wrap-request-btn, btn:btn, btn-fill:btn-fill, btn-outline:btn-outline, btn-try:btn-try, btn-clear:btn-clear, btn-clear-resp:btn-clear-resp,\n            file-input:file-input, textbox:textbox, textbox-param:textbox-param, textarea:textarea, textarea-param:textarea-param, \n            anchor:anchor, anchor-param-example:anchor-param-example, schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n        > </api-request>\n\n        ${path.callbacks ? callbackTemplate.call(this, path.callbacks) : ''}\n\n        <api-response\n          class = \"${this.renderStyle}-mode\"\n          style = \"width:100%;\"\n          webhook = \"${path.isWebhook}\"\n          .responses = \"${path.responses}\"\n          render-style = \"${this.renderStyle}\"\n          schema-style = \"${this.schemaStyle}\"\n          active-schema-tab = \"${this.defaultSchemaTab}\"\n          schema-expand-level = \"${this.schemaExpandLevel}\"\n          schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n          allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n          schema-hide-read-only = \"${this.schemaHideReadOnly === 'never' ? 'false' : path.isWebhook ? 'true' : 'false'}\"\n          schema-hide-write-only = \"${this.schemaHideWriteOnly === 'never' ? 'false' : path.isWebhook ? 'false' : 'true'}\"\n          selected-status = \"${Object.keys(path.responses || {})[0] || ''}\"\n          exportparts = \"btn:btn, btn-response-status:btn-response-status, btn-selected-response-status:btn-selected-response-status, btn-fill:btn-fill, btn-copy:btn-copy,\n          schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n        > </api-response>\n      </div>\n    </div>\n  `;\n}\n\nexport default function expandedEndpointTemplate() {\n  if (!this.resolvedSpec) { return ''; }\n  return html`\n  ${this.resolvedSpec.tags.map((tag) => html`\n    <section id=\"${tag.elementId}\" part=\"section-tag\" class=\"regular-font section-gap--read-mode observe-me\" style=\"border-top:1px solid var(--primary-color);\">\n      <div class=\"title tag\" part=\"section-tag-title label-tag-title\">${tag.name}</div>\n      <slot name=\"${tag.elementId}\"></slot>\n      <div class=\"regular-font-size\">\n      ${\n        unsafeHTML(`\n          <div class=\"m-markdown regular-font\">\n          ${marked(tag.description || '', this.infoDescriptionHeadingsInNavBar === 'true' ? { renderer: headingRenderer(tag.elementId) } : undefined)}\n        </div>`)\n      }\n      </div>\n    </section>\n    <section class=\"regular-font section-gap--read-mode\" part=\"section-operations-in-tag\">\n      ${tag.paths.map((path) => expandedEndpointBodyTemplate.call(this, path))}\n    </section>\n    `)\n  }\n`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport { schemaInObjectNotation } from '~/utils/schema-utils';\nimport '~/components/json-tree';\nimport '~/components/schema-tree';\nimport '~/components/schema-table';\n\nfunction schemaBodyTemplate(sComponent) {\n  return html`\n  <div class='divider'></div>\n  <div class='expanded-endpoint-body observe-me ${sComponent.name}' id='cmp--${sComponent.id}' >\n    <div style=\"font-weight:bold\"> ${sComponent.name} <span style=\"color:var(--light-fg); font-size:var(--font-size-small); font-weight:400;\"> Schema </span></div>\n  ${this.schemaStyle === 'table'\n    ? html`\n      <schema-table\n        .data = '${schemaInObjectNotation(sComponent.component, {})}'\n        schema-expand-level = \"${this.schemaExpandLevel}\"\n        schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n        allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n        schema-hide-read-only = \"false\"\n        schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n        exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n      > </schema-table>`\n    : html`\n      <schema-tree\n        .data = '${schemaInObjectNotation(sComponent.component, {})}'\n        schema-expand-level = \"${this.schemaExpandLevel}\"\n        schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n        allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n        schema-hide-read-only = \"false\"\n        schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n        exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n      > </schema-tree>`\n}\n  </div>`;\n}\n\nfunction componentBodyTemplate(sComponent, componentType) {\n  if (sComponent.id.indexOf('schemas-') !== -1) {\n    return schemaBodyTemplate.call(this, sComponent);\n  }\n  return html`\n  <div class='divider'></div>\n  <div class='expanded-endpoint-body observe-me ${sComponent.name}' id='cmp--${sComponent.id}' >\n    ${html`\n      <div style=\"font-weight:bold\"> ${sComponent.name} <span style=\"color:var(--light-fg); font-size:var(--font-size-small); font-weight:400\"> ${componentType} </span> </div>\n      ${sComponent.component\n    ? html`\n      <div class='mono-font regular-font-size' style='padding: 8px 0; color:var(--fg2)'> \n        <json-tree class=\"border tree\" render-style='${this.renderStyle}' .data=\"${sComponent.component}\"> </json-tree>\n      </div>`\n    : ''}\n    `}\n  </div>\n  `;\n}\n\nexport default function componentsTemplate() {\n  if (!this.resolvedSpec) { return ''; }\n  return html`\n  ${this.resolvedSpec.components.map((component) => html`\n    <div id=\"cmp--${component.name.toLowerCase()}\" class='regular-font section-gap--read-mode observe-me' style=\"border-top:1px solid var(--primary-color);\">\n      <div class=\"title tag\">${component.name}</div>\n      <div class=\"regular-font-size\">\n        ${unsafeHTML(`<div class='m-markdown regular-font'>${marked(component.description ? component.description : '')}</div>`)}\n      </div>\n    </div>\n    <div class='regular-font section-gap--read-mode'>\n      ${component.subComponents.filter((c) => c.expanded !== false).map((sComponent) => componentBodyTemplate.call(this, sComponent, component.name))}\n    </div>\n    `)\n}\n`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport { downloadResource, viewResource } from '~/utils/common-utils';\n\n/* eslint-disable indent */\nfunction headingRenderer() {\n  const renderer = new marked.Renderer();\n  renderer.heading = ((text, level, raw, slugger) => `<h${level} class=\"observe-me\" id=\"overview--${slugger.slug(raw)}\">${text}</h${level}>`);\n  return renderer;\n}\n\nexport default function overviewTemplate() {\n  return html`\n    <section id=\"overview\" part=\"section-overview\"\n      class=\"observe-me ${this.renderStyle === 'view' ? 'section-gap' : 'section-gap--read-mode'}\">\n      ${this.resolvedSpec?.info\n        ? html`\n          <div id=\"api-title\" part=\"section-overview-title\" style=\"font-size:32px\">\n            ${this.resolvedSpec.info.title}\n            ${!this.resolvedSpec.info.version ? '' : html`\n              <span style = 'font-size:var(--font-size-small);font-weight:bold'>\n                ${this.resolvedSpec.info.version}\n              </span>`\n            }\n          </div>\n          <div id=\"api-info\" style=\"font-size:calc(var(--font-size-regular) - 1px); margin-top:8px;\">\n            ${this.resolvedSpec.info.contact?.email\n              ? html`<span>${this.resolvedSpec.info.contact.name || 'Email'}: \n                <a href=\"mailto:${this.resolvedSpec.info.contact.email}\" part=\"anchor anchor-overview\">${this.resolvedSpec.info.contact.email}</a>\n              </span>`\n              : ''\n            }\n            ${this.resolvedSpec.info.contact?.url\n              ? html`<span>URL: <a href=\"${this.resolvedSpec.info.contact.url}\" part=\"anchor anchor-overview\">${this.resolvedSpec.info.contact.url}</a></span>`\n              : ''\n            }\n            ${this.resolvedSpec.info.license\n              ? html`<span>License: \n                ${this.resolvedSpec.info.license.url\n                ? html`<a href=\"${this.resolvedSpec.info.license.url}\" part=\"anchor anchor-overview\">${this.resolvedSpec.info.license.name}</a>`\n                : this.resolvedSpec.info.license.name\n              } </span>`\n              : ''\n            }\n            ${this.resolvedSpec.info.termsOfService\n              ? html`<span><a href=\"${this.resolvedSpec.info.termsOfService}\" part=\"anchor anchor-overview\">Terms of Service</a></span>`\n              : ''\n            }\n            ${this.specUrl && this.allowSpecFileDownload === 'true'\n              ? html`\n                <div style=\"display:flex; margin:12px 0; gap:8px; justify-content: start;\">\n                  <button class=\"m-btn thin-border\" style=\"min-width:170px\" part=\"btn btn-outline\" @click='${(e) => { downloadResource(this.specUrl, 'openapi-spec', e); }}'>Download OpenAPI spec</button>\n                  ${this.specUrl?.trim().toLowerCase().endsWith('json')\n                    ? html`<button class=\"m-btn thin-border\" style=\"width:200px\" part=\"btn btn-outline\" @click='${(e) => { viewResource(this.specUrl, e); }}'>View OpenAPI spec (New Tab)</button>`\n                    : ''\n                  }\n                </div>`\n              : ''\n            }\n          </div>\n          <slot name=\"overview\"></slot>\n          <div id=\"api-description\">\n          ${this.resolvedSpec.info.description\n            ? html`${\n              unsafeHTML(`\n                <div class=\"m-markdown regular-font\">\n                ${marked(this.resolvedSpec.info.description, this.infoDescriptionHeadingsInNavBar === 'true' ? { renderer: headingRenderer() } : undefined)}\n              </div>`)}`\n            : ''\n          }\n          </div>\n        `\n        : ''\n      }\n    </section>\n  `;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\n\nexport function setApiServer(serverUrl) {\n  const serverObj = this.resolvedSpec?.servers.find((s) => s.url === serverUrl);\n  if (!serverObj) {\n    return false;\n  }\n  this.selectedServer = serverObj;\n  this.requestUpdate();\n  this.dispatchEvent(new CustomEvent('api-server-change', {\n    bubbles: true,\n    composed: true,\n    detail: {\n      selectedServer: serverObj,\n    },\n  }));\n  return true;\n}\n\nfunction onApiServerVarChange(e, serverObj) {\n  const inputEls = [...e.currentTarget.closest('table').querySelectorAll('input, select')];\n  let tempUrl = serverObj.url;\n  inputEls.forEach((v) => {\n    const regex = new RegExp(`{${v.dataset.var}}`, 'g');\n    tempUrl = tempUrl.replace(regex, v.value);\n  });\n  serverObj.computedUrl = tempUrl;\n  this.requestUpdate();\n}\n\n/* eslint-disable indent */\nfunction serverVarsTemplate() {\n  // const selectedServerObj = this.resolvedSpec.servers.find((v) => (v.url === this.selectedServer));\n  return this.selectedServer && this.selectedServer.variables\n    ? html`\n    <div class=\"table-title\">SERVER VARIABLES</div>\n    <table class='m-table' role='presentation'>\n      ${Object.entries(this.selectedServer.variables).map((kv) => html`\n        <tr>\n          <td style=\"vertical-align: middle;\" >${kv[0]}</td>\n          <td>\n            ${kv[1].enum\n            ? html`\n            <select\n              data-var = \"${kv[0]}\"\n              @input = ${(e) => { onApiServerVarChange.call(this, e, this.selectedServer); }}\n            >\n            ${Object.entries(kv[1].enum).map((e) => (kv[1].default === e[1]\n              ? html`\n              <option\n                selected\n                label = ${e[1]}\n                value = ${e[1]}\n              />`\n              : html`\n              <option\n                label = ${e[1]}\n                value = ${e[1]}\n              />`\n            ))}\n            </select>`\n            : html`\n            <input\n              type = \"text\"\n              part=\"textbox textbox-server-var\"\n              spellcheck = \"false\"\n              data-var = \"${kv[0]}\"\n              value = \"${kv[1].default}\"\n              @input = ${(e) => { onApiServerVarChange.call(this, e, this.selectedServer); }}\n            />`}\n          </td>\n        </tr>\n        ${kv[1].description\n          ? html`<tr><td colspan=\"2\" style=\"border:none\"><span class=\"m-markdown-small\"> ${unsafeHTML(marked(kv[1].description))} </span></td></tr>`\n          : ''\n        }\n      `)}\n    </table>\n    `\n    : '';\n}\n\nexport default function serverTemplate() {\n  if (!this.resolvedSpec || this.resolvedSpec.specLoadError) { return ''; }\n  return html`\n  <section id = 'servers' part=\"section-servers\" style=\"text-align:left; direction:ltr; margin-top:24px; margin-bottom:24px;\" class='regular-font observe-me ${'read focused'.includes(this.renderStyle) ? 'section-gap--read-mode' : 'section-gap'}'>\n    <div part = \"section-servers-title\" class = \"sub-title\">API SERVER</div>\n    <div class = 'mono-font' style='margin: 12px 0; font-size:calc(var(--font-size-small) + 1px);'>\n      ${!this.resolvedSpec.servers || this.resolvedSpec.servers?.length === 0\n        ? ''\n        : html`\n          ${this.resolvedSpec?.servers.map((server, i) => html`\n            <input type = 'radio'\n              name = 'api_server'\n              id = 'srvr-opt-${i}'\n              value = '${server.url}'\n              @change = ${() => { setApiServer.call(this, server.url); }}\n              .checked = '${this.selectedServer.url === server.url}'\n              style = 'margin:4px 0; cursor:pointer'\n            />\n              <label style='cursor:pointer' for='srvr-opt-${i}'>\n                ${server.url} ${server.description ? html`- <span class='regular-font'>${server.description} </span>` : ''}\n              </label>\n            <br/>\n          `)}\n      `}\n      <div class=\"table-title primary-text\" part=\"label-selected-server\"> SELECTED: ${this.selectedServer?.computedUrl || 'none'}</div>\n    </div>\n    <slot name=\"servers\"></slot>\n    ${serverVarsTemplate.call(this)}\n  </section>`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { marked } from 'marked';\nimport { pathIsInSearch } from '~/utils/common-utils';\n\nexport function expandCollapseNavBarTag(navLinkEl, action = 'toggle') {\n  const tagAndPathEl = navLinkEl?.closest('.nav-bar-tag-and-paths');\n  const pathsUnderTagEl = tagAndPathEl?.querySelector('.nav-bar-paths-under-tag');\n  if (tagAndPathEl) {\n    const isExpanded = tagAndPathEl.classList.contains('expanded');\n    if (isExpanded && (action === 'toggle' || action === 'collapse')) {\n      pathsUnderTagEl.style.maxHeight = 0;\n      tagAndPathEl.classList.replace('expanded', 'collapsed');\n    } else if (!isExpanded && (action === 'toggle' || action === 'expand')) {\n      tagAndPathEl.classList.replace('collapsed', 'expanded');\n      pathsUnderTagEl.style.maxHeight = `${pathsUnderTagEl.scrollHeight}px`;\n    }\n  }\n}\n\nexport function expandCollapseAll(event, action = 'expand-all') {\n  if (!(event.type === 'click' || (event.type === 'keyup' && event.keyCode === 13))) {\n    return;\n  }\n  const navEl = event.target.closest('.nav-scroll');\n  const elList = [...navEl.querySelectorAll('.nav-bar-tag-and-paths')];\n  if (action === 'expand-all') {\n    elList.forEach((el) => {\n      const navBarPathsUnderTagEl = el.querySelector('.nav-bar-paths-under-tag');\n      el.classList.replace('collapsed', 'expanded');\n      navBarPathsUnderTagEl.style.maxHeight = `${navBarPathsUnderTagEl?.scrollHeight}px`;\n    });\n  } else {\n    elList.forEach((el) => {\n      el.classList.replace('expanded', 'collapsed');\n      el.querySelector('.nav-bar-paths-under-tag').style.maxHeight = 0;\n    });\n  }\n}\n\nexport function navBarClickAndEnterHandler(event) {\n  if (!(event.type === 'click' || (event.type === 'keyup' && event.keyCode === 13))) {\n    return;\n  }\n  const navEl = event.target;\n  event.stopPropagation();\n  if (navEl.dataset?.action === 'navigate') {\n    this.scrollToEventTarget(event, false);\n  } else if (navEl.dataset?.action === 'expand-all' || (navEl.dataset?.action === 'collapse-all')) {\n    expandCollapseAll(event, navEl.dataset.action);\n  } else if (navEl.dataset?.action === 'expand-collapse-tag') {\n    expandCollapseNavBarTag(navEl, 'toggle');\n  }\n}\n\n/* eslint-disable indent */\nexport default function navbarTemplate() {\n  if (!this.resolvedSpec || this.resolvedSpec.specLoadError) {\n    return html`\n      <nav class='nav-bar' part='section-navbar'>\n        <slot name='nav-logo' class='logo'></slot>\n      </nav>\n    `;\n  }\n  return html`\n  <nav class='nav-bar ${this.renderStyle}' part='section-navbar'>\n    <slot name='nav-logo' class='logo'></slot>\n    ${(this.allowSearch === 'false' && this.allowAdvancedSearch === 'false')\n      ? ''\n      : html`\n        <div style='display:flex; flex-direction:row; justify-content:center; align-items:stretch; padding:8px 24px 12px 24px; ${this.allowAdvancedSearch === 'false' ? 'border-bottom: 1px solid var(--nav-hover-bg-color)' : ''}' part='section-navbar-search'>\n          ${this.allowSearch === 'false'\n            ? ''\n            : html`\n              <div style = 'display:flex; flex:1; line-height:22px;'>\n                <input id = 'nav-bar-search' \n                  part = 'textbox textbox-nav-filter'\n                  style = 'width:100%; padding-right:20px; color:var(--nav-hover-text-color); border-color:var(--nav-accent-color); background-color:var(--nav-hover-bg-color)'\n                  type = 'text'\n                  placeholder = 'Filter' \n                  @change = '${this.onSearchChange}'\n                  spellcheck = 'false'\n                >\n                <div style='margin: 6px 5px 0 -24px; font-size:var(--font-size-regular); cursor:pointer;'>&#x21a9;</div>\n              </div>  \n              ${this.matchPaths\n                ? html`\n                  <button @click = '${this.onClearSearch}' class='m-btn thin-border' style='margin-left:5px; color:var(--nav-text-color); width:75px; padding:6px 8px;' part='btn btn-outline btn-clear-filter'>\n                    CLEAR\n                  </button>`\n                : ''\n              }\n            `\n          }\n          ${this.allowAdvancedSearch === 'false' || this.matchPaths\n            ? ''\n            : html`\n              <button class='m-btn primary' part='btn btn-fill btn-search' style='margin-left:5px; padding:6px 8px; width:75px' @click='${this.onShowSearchModalClicked}'>\n                SEARCH\n              </button>\n            `\n          }\n        </div>\n      `\n    }\n    ${html`<nav class='nav-scroll' tabindex='-1' part='section-navbar-scroll' @click='${(e) => navBarClickAndEnterHandler.call(this, e)}' @keyup='${(e) => navBarClickAndEnterHandler.call(this, e)}' >\n      ${(this.showInfo === 'false' || !this.resolvedSpec.info)\n        ? ''\n        : html`\n          ${(this.infoDescriptionHeadingsInNavBar === 'true')\n            ? html`\n              ${this.resolvedSpec.infoDescriptionHeaders.length > 0\n                ? html`<div class='nav-bar-info ${this.navActiveItemMarker}' id='link-overview' data-content-id='overview' data-action='navigate' tabindex='0' part='section-navbar-item section-navbar-overview'> \n                    ${this.resolvedSpec.info?.title?.trim() || 'Overview'}\n                  </div>`\n                : ''\n              }\n              <div class='overview-headers'>\n                ${this.resolvedSpec.infoDescriptionHeaders.map((header) => html`\n                  <div\n                    class='nav-bar-h${header.depth} ${this.navActiveItemMarker}' \n                    id='link-overview--${new marked.Slugger().slug(header.text)}'\n                    data-action='navigate' \n                    data-content-id='overview--${new marked.Slugger().slug(header.text)}' \n                  >\n                    ${header.text}\n                  </div>`)\n                }\n              </div>\n              ${this.resolvedSpec.infoDescriptionHeaders.length > 0 ? html`<hr style='border-top: 1px solid var(--nav-hover-bg-color); border-width:1px 0 0 0; margin: 15px 0 0 0'/>` : ''}\n            `\n            : html`<div class='nav-bar-info ${this.navActiveItemMarker}' id='link-overview' data-action='navigate' data-content-id='overview' tabindex='0'> \n              ${this.resolvedSpec.info?.title?.trim() || 'Overview'}\n            </div>`\n          }\n        `\n      }\n    \n      ${this.allowServerSelection === 'false'\n        ? ''\n        : html`<div class='nav-bar-info ${this.navActiveItemMarker}' id='link-servers' data-action='navigate' data-content-id='servers' tabindex='0' part='section-navbar-item section-navbar-servers'> API Servers </div>`\n      }\n      ${(this.allowAuthentication === 'false' || !this.resolvedSpec.securitySchemes)\n        ? ''\n        : html`<div class='nav-bar-info ${this.navActiveItemMarker}' id='link-auth' data-action='navigate' data-content-id='auth' tabindex='0' part='section-navbar-item section-navbar-auth'> Authentication </div>`\n      }\n\n      <div id='link-operations-top' class='nav-bar-section operations' data-action='navigate' data-content-id='${this.renderStyle === 'focused' ? '' : 'operations-top'}' part='section-navbar-item section-navbar-operations-top'>\n        <div style='font-size:16px; display:flex; margin-left:10px;'>\n          ${this.renderStyle === 'focused'\n            ? html`\n              <div class='nav-bar-expand-all'\n                data-action='expand-all'\n                tabindex='0' \n                title='Expand all'\n              >▸</div>\n              <div class='nav-bar-collapse-all'\n                data-action='collapse-all'\n                tabindex='0' \n                title='Collapse all'\n              >▸</div>`\n            : ''\n          }  \n        </div>\n        <div class='nav-bar-section-title'> OPERATIONS </div>\n      </div>\n\n      <!-- TAGS AND PATHS-->\n      ${this.resolvedSpec.tags\n        .filter((tag) => tag.paths.filter((path) => pathIsInSearch(this.matchPaths, path, this.matchType)).length)\n        .map((tag) => html`\n          <div class='nav-bar-tag-and-paths ${(this.renderStyle === 'read' ? 'expanded' : (tag.expanded ? 'expanded' : 'collapsed'))}' >\n            ${tag.name === 'General ⦂'\n              ? html`<hr style='border:none; border-top: 1px dotted var(--nav-text-color); opacity:0.3; margin:-1px 0 0 0;'/>`\n              : html`\n                <div \n                  class='nav-bar-tag ${this.navActiveItemMarker}'\n                  part='section-navbar-item section-navbar-tag'\n                  id='link-${tag.elementId}'\n                  data-action='${(this.renderStyle === 'read' ? 'navigate' : this.onNavTagClick === 'show-description') ? 'navigate' : 'expand-collapse-tag'}'\n                  data-content-id='${(this.renderStyle === 'read' ? `${tag.elementId}` : this.onNavTagClick === 'show-description') ? `${tag.elementId}` : ''}'\n                  data-first-path-id='${tag.firstPathId}'\n                  tabindex='0'\n                >\n                  <div style=\"pointer-events:none;\">${tag.name}</div>\n                  <div class='nav-bar-tag-icon' tabindex='0' data-action='expand-collapse-tag'></div>\n                </div>\n              `\n            }\n            ${(this.infoDescriptionHeadingsInNavBar === 'true')\n              ? html`\n                ${this.renderStyle === 'focused' && this.onNavTagClick === 'expand-collapse'\n                  ? ''\n                  : html`\n                    <div class='tag-headers'>\n                      ${tag.headers.map((header) => html`\n                      <div\n                        class='nav-bar-h${header.depth} ${this.navActiveItemMarker}'\n                        part='section-navbar-item section-navbar-h${header.depth}'\n                        id='link-${tag.elementId}--${new marked.Slugger().slug(header.text)}'\n                        data-action='navigate'\n                        data-content-id='${tag.elementId}--${new marked.Slugger().slug(header.text)}'\n                        tabindex='0'\n                      > ${header.text}</div>`)}\n                    </div>`\n                }`\n              : ''\n            }\n            <div class='nav-bar-paths-under-tag' style='max-height:${(tag.expanded || this.renderStyle === 'read') ? ((tag.paths?.length || 1) * 50) : 0}px;'>\n              <!-- Paths in each tag (endpoints) -->\n              ${tag.paths.filter((v) => {\n                if (this.matchPaths) {\n                  return pathIsInSearch(this.matchPaths, v, this.matchType);\n                }\n                return true;\n              }).map((p) => html`\n              <div \n                class='nav-bar-path ${this.navActiveItemMarker} ${this.usePathInNavBar === 'true' ? 'small-font' : ''}'\n                part='section-navbar-item section-navbar-path'\n                data-action='navigate'\n                data-content-id='${p.elementId}'\n                id='link-${p.elementId}'\n                tabindex='0'\n              >\n                <span style = 'display:flex; pointer-events: none; align-items:start; ${p.deprecated ? 'filter:opacity(0.5)' : ''}'>\n                  ${html`<span class='nav-method ${this.showMethodInNavBar} ${p.method}' style='pointer-events: none;'>\n                      ${this.showMethodInNavBar === 'as-colored-block' ? p.method.substring(0, 3).toUpperCase() : p.method.toUpperCase()}\n                    </span>`\n                  }\n                  ${p.isWebhook ? html`<span style='font-weight:bold; pointer-events: none; margin-right:8px; font-size: calc(var(--font-size-small) - 2px)'>WEBHOOK</span>` : ''}\n                  ${this.usePathInNavBar === 'true'\n                    ? html`<span style='pointer-events: none;' class='mono-font'>${p.path}</span>`\n                    : p.summary || p.shortSummary\n                  }\n                </span>\n              </div>`)}\n            </div>\n          </div>\n        `)\n      }\n\n      <!-- COMPONENTS -->\n      ${this.resolvedSpec.components && this.showComponents === 'true' && this.renderStyle === 'focused'\n        ? html`\n          <div id='link-components' class='nav-bar-section components'>\n            <div></div>\n            <div class='nav-bar-section-title'>COMPONENTS</div>\n          </div>\n          ${this.resolvedSpec.components.map((component) => (component.subComponents.length\n            ? html`\n              <div class='nav-bar-tag'\n                part='section-navbar-item section-navbar-tag'\n                data-action='navigate' \n                data-content-id='cmp--${component.name.toLowerCase()}' \n                id='link-cmp--${component.name.toLowerCase()}'\n              >\n                ${component.name}\n              </div>\n              ${component.subComponents.filter((p) => p.expanded !== false).map((p) => html`\n                <div class='nav-bar-path' data-action='navigate' data-content-id='cmp--${p.id}' id='link-cmp--${p.id}'>\n                  <span> ${p.name} </span>\n                </div>`)\n              }`\n            : ''))\n          }`\n        : ''\n      }\n    </nav>`\n  }\n</nav>\n`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport { expandedEndpointBodyTemplate } from '~/templates/expanded-endpoint-template';\nimport '~/components/api-request';\nimport '~/components/api-response';\nimport componentsTemplate from '~/templates/components-template';\nimport overviewTemplate from '~/templates/overview-template';\nimport serverTemplate from '~/templates/server-template';\nimport securitySchemeTemplate from '~/templates/security-scheme-template';\nimport { expandCollapseNavBarTag } from '~/templates/navbar-template';\n\nfunction headingRenderer(tagElementId) {\n  const renderer = new marked.Renderer();\n  renderer.heading = ((text, level, raw, slugger) => `<h${level} class=\"observe-me\" id=\"${tagElementId}--${slugger.slug(raw)}\">${text}</h${level}>`);\n  return renderer;\n}\n\nfunction wrapFocusedTemplate(templateToWrap) {\n  return html`\n    <div class='regular-font section-gap--focused-mode' part=\"section-operations-in-tag\">\n      ${templateToWrap}\n    </div>`;\n}\n\nfunction defaultContentTemplate() {\n  // In focused mode default content is overview or first path\n  if (this.showInfo === 'true') {\n    return wrapFocusedTemplate(overviewTemplate.call(this));\n  }\n  const selectedTagObj = this.resolvedSpec.tags[0];\n  const selectedPathObj = this.resolvedSpec.tags[0]?.paths[0];\n  return (selectedTagObj && selectedPathObj)\n    ? wrapFocusedTemplate(expandedEndpointBodyTemplate.call(this, selectedPathObj, selectedTagObj.name))\n    : wrapFocusedTemplate('');\n}\n\n/* eslint-disable indent */\nfunction focusedTagBodyTemplate(tag) {\n  return html`\n    <h1 id=\"${tag.elementId}\">${tag.name}</h1>\n    ${this.onNavTagClick === 'show-description' && tag.description\n      ? html`\n        <div class=\"m-markdown\">\n          ${\n            unsafeHTML(`\n            <div class=\"m-markdown regular-font\">\n              ${marked(tag.description || '', this.infoDescriptionHeadingsInNavBar === 'true' ? { renderer: headingRenderer(tag.elementId) } : undefined)}\n            </div>`)\n          }\n        </div>`\n      : ''\n    }\n  `;\n}\n\nexport default function focusedEndpointTemplate() {\n  if (!this.focusedElementId || !this.resolvedSpec) {\n    return;\n  }\n  const focusElId = this.focusedElementId;\n  let selectedPathObj = null;\n  let selectedTagObj = null;\n  let focusedTemplate;\n  let i = 0;\n  if (focusElId.startsWith('overview') && this.showInfo === 'true') {\n    focusedTemplate = overviewTemplate.call(this);\n  } else if (focusElId === 'auth' && this.allowAuthentication === 'true') {\n    focusedTemplate = securitySchemeTemplate.call(this);\n  } else if (focusElId === 'servers' && this.allowServerSelection === 'true') {\n    focusedTemplate = serverTemplate.call(this);\n  } else if (focusElId === 'operations-top') {\n    focusedTemplate = html`\n    <div id=\"operations-top\" class=\"observe-me\">\n      <slot name=\"operations-top\"></slot>\n    </div>`;\n  } else if (focusElId.startsWith('cmp--') && this.showComponents === 'true') {\n    focusedTemplate = componentsTemplate.call(this);\n  } else if (focusElId.startsWith('tag--')) {\n    const idToFocus = focusElId.indexOf('--', 4) > 0 ? focusElId.substring(0, focusElId.indexOf('--', 5)) : focusElId;\n    selectedTagObj = this.resolvedSpec.tags.find((v) => v.elementId === idToFocus);\n    if (selectedTagObj) {\n      focusedTemplate = wrapFocusedTemplate.call(this, focusedTagBodyTemplate.call(this, selectedTagObj));\n    } else {\n      focusedTemplate = defaultContentTemplate.call(this);\n    }\n  } else {\n    for (i = 0; i < this.resolvedSpec.tags.length; i += 1) {\n      selectedTagObj = this.resolvedSpec.tags[i];\n      selectedPathObj = this.resolvedSpec.tags[i].paths.find((v) => `${v.elementId}` === focusElId);\n      if (selectedPathObj) {\n        break;\n      }\n    }\n    if (selectedPathObj) {\n      // In focused mode we must expand the nav-bar tag element if it is collapsed\n      const newNavEl = this.shadowRoot.getElementById(`link-${focusElId}`);\n      expandCollapseNavBarTag(newNavEl, 'expand');\n      focusedTemplate = wrapFocusedTemplate.call(\n        this,\n        expandedEndpointBodyTemplate.call(this, selectedPathObj, (selectedTagObj.name || ''), (selectedTagObj.description || '')),\n      );\n    } else {\n      // if focusedElementId is not found then show the default content (overview or first-path)\n      focusedTemplate = defaultContentTemplate.call(this);\n    }\n  }\n  return focusedTemplate;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport '~/components/api-request';\nimport '~/components/api-response';\nimport codeSamplesTemplate from '~/templates/code-samples-template';\nimport callbackTemplate from '~/templates/callback-template';\nimport { pathSecurityTemplate } from '~/templates/security-scheme-template';\nimport { pathIsInSearch, rapidocApiKey } from '~/utils/common-utils';\n\nfunction toggleExpand(path) {\n  if (path.expanded) {\n    path.expanded = false; // collapse\n    if (this.updateRoute === 'true') {\n      this.replaceHistoryState('');\n    }\n  } else {\n    path.expanded = true; // Expand\n    if (this.updateRoute === 'true') {\n      const newHash = `${this.routePrefix || '#'}${path.elementId}`;\n      if (window.location.hash !== newHash) {\n        this.replaceHistoryState(path.elementId);\n      }\n    }\n  }\n  this.requestUpdate();\n}\n\nexport function expandCollapseAll(operationsRootEl, action = 'expand-all') {\n  const elList = [...operationsRootEl.querySelectorAll('.section-tag')];\n  if (action === 'expand-all') {\n    elList.map((el) => {\n      el.classList.replace('collapsed', 'expanded');\n    });\n  } else {\n    elList.map((el) => {\n      el.classList.replace('expanded', 'collapsed');\n    });\n  }\n}\n\nfunction onExpandCollapseAll(e, action = 'expand-all') {\n  expandCollapseAll.call(this, e.target.closest('.operations-root'), action);\n}\n\n/* eslint-disable indent */\nfunction endpointHeadTemplate(path, pathsExpanded = false) {\n  return html`\n  <summary @click=\"${(e) => { toggleExpand.call(this, path, e); }}\" part=\"section-endpoint-head-${path.expanded ? 'expanded' : 'collapsed'}\" class='endpoint-head ${path.method} ${path.deprecated ? 'deprecated' : ''} ${pathsExpanded || path.expanded ? 'expanded' : 'collapsed'}'>\n    <div part=\"section-endpoint-head-method\" class=\"method ${path.method} ${path.deprecated ? 'deprecated' : ''}\"> ${path.method} </div> \n    <div  part=\"section-endpoint-head-path\" class=\"path ${path.deprecated ? 'deprecated' : ''}\"> \n      ${path.path} \n      ${path.isWebhook ? html`<span style=\"font-family: var(--font-regular); font-size: var(--); font-size: var(--font-size-small); color:var(--primary-color); margin-left: 16px\"> Webhook</span>` : ''}\n    </div>\n    ${path.deprecated\n      ? html`\n        <span style=\"font-size:var(--font-size-small); text-transform:uppercase; font-weight:bold; color:var(--red); margin:2px 0 0 5px;\"> \n          deprecated \n        </span>`\n      : ''\n    }\n    ${this.showSummaryWhenCollapsed\n      ? html`\n        <div class=\"only-large-screen\" style=\"min-width:60px; flex:1\"></div>\n        <div part=\"section-endpoint-head-description\" class=\"descr\">${path.summary || path.shortSummary} </div>`\n      : ''\n    }\n  </summary>\n  `;\n}\n\nfunction endpointBodyTemplate(path) {\n  const acceptContentTypes = new Set();\n  for (const respStatus in path.responses) {\n    for (const acceptContentType in (path.responses[respStatus]?.content)) {\n      acceptContentTypes.add(acceptContentType.trim());\n    }\n  }\n  const accept = [...acceptContentTypes].join(', ');\n  // Filter API Keys that are non-empty and are applicable to the the path\n  const nonEmptyApiKeys = this.resolvedSpec.securitySchemes.filter((v) => (v.finalKeyValue && path.security?.some((ps) => (v.securitySchemeId in ps)))) || [];\n\n  // If a RapiDoc API Key is specified on the element and its value is not hyphen(-) then include it for all paths\n  const rapiDocApiKey = this.resolvedSpec.securitySchemes.find((v) => (v.securitySchemeId === rapidocApiKey && v.value !== '-'));\n  if (rapiDocApiKey) {\n    nonEmptyApiKeys.push(rapiDocApiKey);\n  }\n\n  const codeSampleTabPanel = path.xCodeSamples ? codeSamplesTemplate(path.xCodeSamples) : '';\n  return html`\n  <div part=\"section-endpoint-body-${path.expanded ? 'expanded' : 'collapsed'}\" class='endpoint-body ${path.method} ${path.deprecated ? 'deprecated' : ''}'>\n    <div class=\"summary\">\n      ${path.summary\n        ? html`<div class=\"title\" part=\"section-endpoint-body-title\">${path.summary}<div>`\n        : path.shortSummary !== path.description\n          ? html`<div class=\"title\" part=\"section-endpoint-body-title\">${path.shortSummary}</div>`\n          : ''\n      }\n      ${path.xBadges && path.xBadges?.length > 0\n        ? html`\n          <div style=\"display:flex; flex-wrap:wrap;font-size: var(--font-size-small);\">\n            ${path.xBadges.map((v) => (\n                html`<span part=\"endpoint-badge\" style=\"margin:1px; margin-right:5px; padding:1px 8px; font-weight:bold; border-radius:12px;  background-color: var(--light-${v.color}, var(--input-bg)); color:var(--${v.color}); border:1px solid var(--${v.color})\">${v.label}</span>`\n              ))\n            }\n          </div>\n          `\n        : ''\n      }\n\n      ${path.description ? html`<div part=\"section-endpoint-body-description\" class=\"m-markdown\"> ${unsafeHTML(marked(path.description))}</div>` : ''}\n      ${path.externalDocs?.url || path.externalDocs?.description\n        ? html`<div style=\"background-color:var(--bg3); padding:2px 8px 8px 8px; margin:8px 0; border-radius:var(--border-radius)\"> \n            <div class=\"m-markdown\"> ${unsafeHTML(marked(path.externalDocs?.description || ''))} </div>\n            ${path.externalDocs?.url\n              ? html`<a style=\"font-family:var(--font-mono); font-size:var(--font-size-small)\" href=\"${path.externalDocs?.url}\" target=\"_blank\"> \n                  ${path.externalDocs?.url} <div style=\"transform: rotate(270deg) scale(1.5); display: inline-block; margin-left:5px\">⇲</div>\n                </a>`\n              : ''\n            }\n          </div>`\n        : ''\n      }\n      <slot name=\"${path.elementId}\"></slot>\n      ${pathSecurityTemplate.call(this, path.security)}\n      ${codeSampleTabPanel}\n    </div>  \n    <div class='req-resp-container'> \n      <div style=\"display:flex; flex-direction:column\" class=\"view-mode-request ${this.layout}-layout\">\n        <api-request\n          class = \"${this.renderStyle}-mode ${this.layout}-layout\"\n          style = \"width:100%;\"\n          webhook = \"${path.isWebhook}\"\n          method = \"${path.method}\"\n          path = \"${path.path}\"\n          .security = \"${path.security}\"\n          .parameters = \"${path.parameters}\"\n          .request_body = \"${path.requestBody}\"\n          .api_keys = \"${nonEmptyApiKeys}\"\n          .servers = \"${path.servers}\" \n          server-url = \"${path.servers && path.servers.length > 0 ? path.servers[0].url : this.selectedServer.computedUrl}\" \n          active-schema-tab = \"${this.defaultSchemaTab}\"\n          fill-request-fields-with-example = \"${this.fillRequestFieldsWithExample}\"\n          allow-try = \"${this.allowTry}\"\n          show-curl-before-try = \"${this.showCurlBeforeTry}\"\n          accept = \"${accept}\"\n          render-style=\"${this.renderStyle}\" \n          schema-style = \"${this.schemaStyle}\" \n          schema-expand-level = \"${this.schemaExpandLevel}\"\n          schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n          allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n          schema-hide-read-only = \"${this.schemaHideReadOnly === 'never' ? 'false' : path.isWebhook ? 'false' : 'true'}\"\n          schema-hide-write-only = \"${this.schemaHideWriteOnly === 'never' ? 'false' : path.isWebhook ? 'true' : 'false'}\"\n          fetch-credentials = \"${this.fetchCredentials}\"\n          exportparts = \"wrap-request-btn:wrap-request-btn, btn:btn, btn-fill:btn-fill, btn-outline:btn-outline, btn-try:btn-try, btn-clear:btn-clear, btn-clear-resp:btn-clear-resp,\n            file-input:file-input, textbox:textbox, textbox-param:textbox-param, textarea:textarea, textarea-param:textarea-param, \n            anchor:anchor, anchor-param-example:anchor-param-example, schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n          > </api-request>\n\n          ${path.callbacks ? callbackTemplate.call(this, path.callbacks) : ''}\n        </div>  \n\n        <api-response\n          class = \"${this.renderStyle}-mode\"\n          style = \"width:100%;\"\n          webhook = \"${path.isWebhook}\"\n          .responses=\"${path.responses}\"\n          active-schema-tab = \"${this.defaultSchemaTab}\" \n          render-style=\"${this.renderStyle}\" \n          schema-style=\"${this.schemaStyle}\"\n          schema-expand-level = \"${this.schemaExpandLevel}\"\n          schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n          allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n          schema-hide-read-only = \"${this.schemaHideReadOnly === 'never' ? 'false' : path.isWebhook ? 'true' : 'false'}\"\n          schema-hide-write-only = \"${this.schemaHideWriteOnly === 'never' ? 'false' : path.isWebhook ? 'false' : 'true'}\"\n          selected-status = \"${Object.keys(path.responses || {})[0] || ''}\"\n          exportparts = \"btn:btn, btn-fill:btn-fill, btn-outline:btn-outline, btn-try:btn-try, file-input:file-input, \n          textbox:textbox, textbox-param:textbox-param, textarea:textarea, textarea-param:textarea-param, anchor:anchor, anchor-param-example:anchor-param-example, btn-clear-resp:btn-clear-resp,\n          schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n        > </api-response>\n      </div>\n  </div>`;\n}\n\nexport default function endpointTemplate(showExpandCollapse = true, showTags = true, pathsExpanded = false) {\n  if (!this.resolvedSpec) { return ''; }\n  return html`\n    ${showExpandCollapse\n      ? html`\n        <div style=\"display:flex; justify-content:flex-end;\"> \n          <span @click=\"${(e) => onExpandCollapseAll(e, 'expand-all')}\" style=\"color:var(--primary-color); cursor:pointer;\">\n            Expand all\n          </span> \n          &nbsp;|&nbsp; \n          <span @click=\"${(e) => onExpandCollapseAll(e, 'collapse-all')}\" style=\"color:var(--primary-color); cursor:pointer;\" >\n            Collapse all\n          </span> \n          &nbsp; sections\n        </div>`\n      : ''\n    }\n    ${this.resolvedSpec.tags.map((tag) => html`\n      ${showTags\n        ? html` \n          <div class='regular-font section-gap section-tag ${tag.expanded ? 'expanded' : 'collapsed'}'> \n            <div class='section-tag-header' @click=\"${() => { tag.expanded = !tag.expanded; this.requestUpdate(); }}\">\n              <div id='${tag.elementId}' class=\"sub-title tag\" style=\"color:var(--primary-color)\">${tag.name}</div>\n            </div>\n            <div class='section-tag-body'>\n              <slot name=\"${tag.elementId}\"></slot>\n              <div class=\"regular-font regular-font-size m-markdown\" style=\"padding-bottom:12px\">\n                ${unsafeHTML(marked(tag.description || ''))}\n              </div>\n              ${tag.paths.filter((v) => {\n                if (this.matchPaths) {\n                  return pathIsInSearch(this.matchPaths, v, this.matchType);\n                }\n                return true;\n                }).map((path) => html`\n                <section part=\"section-endpoint\" id='${path.elementId}' class='m-endpoint regular-font ${path.method} ${pathsExpanded || path.expanded ? 'expanded' : 'collapsed'}'>\n                  ${endpointHeadTemplate.call(this, path, pathsExpanded)}      \n                  ${pathsExpanded || path.expanded ? endpointBodyTemplate.call(this, path) : ''}\n                </section>`)\n              }\n            </div>\n          </div>`\n        : html`\n          <div class='section-tag-body'>\n          ${tag.paths.filter((v) => {\n            if (this.matchPaths) {\n              return pathIsInSearch(this.matchPaths, v, this.matchType);\n            }\n            return true;\n            }).map((path) => html`\n            <section id='${path.elementId}' class='m-endpoint regular-font ${path.method} ${pathsExpanded || path.expanded ? 'expanded' : 'collapsed'}'>\n              ${endpointHeadTemplate.call(this, path, pathsExpanded)}      \n              ${pathsExpanded || path.expanded ? endpointBodyTemplate.call(this, path) : ''}\n            </section>`)\n          }\n          </div>\n        `\n      }\n  `)\n  }`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\n\n/* eslint-disable indent */\nexport default function logoTemplate(style) {\nreturn html`\n  <div style=${style}>\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"1 0 511 512\">\n      <path d=\"M351 411a202 202 0 01-350 0 203 203 0 01333-24 203 203 0 0117 24zm0 0\" fill=\"#adc165\"/>\n      <path d=\"M334 387a202 202 0 01-216-69 202 202 0 01216 69zm78 32H85a8 8 0 01-8-8 8 8 0 018-8h327a8 8 0 017 8 8 8 0 01-7 8zm0 0\" fill=\"#99aa52\"/>\n      <path d=\"M374 338l-5 30a202 202 0 01-248-248 203 203 0 01253 218zm0 0\" fill=\"#ffc73b\"/>\n      <path d=\"M374 338a202 202 0 01-100-197 203 203 0 01100 197zm38 81l-6-2-231-231a8 8 0 0111-11l231 230a8 8 0 01-5 14zm0 0\" fill=\"#efb025\"/>\n      <path d=\"M311 175c0 75 40 140 101 175a202 202 0 000-350 202 202 0 00-101 175zm0 0\" fill=\"#ff903e\"/>\n      <path d=\"M412 419a8 8 0 01-8-8V85a8 8 0 0115 0v326a8 8 0 01-7 8zm0 0\" fill=\"#e87425\"/>\n    </svg>\n  </div>    \n`;\n}\n/* eslint-enable indent */\n", "import { html } from 'lit';\nimport logoTemplate from '~/templates/logo-template';\n\n/* eslint-disable indent */\nexport default function headerTemplate() {\n  return html`\n  <header class=\"row main-header regular-font\" part=\"section-header\" style=\"padding:8px 4px 8px 4px;min-height:48px;\">\n    <div class=\"only-large-screen-flex\" style=\"align-items: center;\">\n      <slot name=\"logo\" class=\"logo\" part=\"section-logo\">\n        ${logoTemplate('height:36px;width:36px;margin-left:5px')}\n        <!-- m-logo style=\"height:36px;width:36px;margin-left:5px\"></m-logo -->\n      </slot>  \n      <div class=\"header-title\" part=\"label-header-title\">${this.headingText}</div>\n    </div>  \n    <div style=\"margin: 0px 8px;display:flex;flex:1\">\n      ${(this.allowSpecUrlLoad === 'false')\n        ? ''\n        : html`\n          <input id=\"spec-url\" \n            type=\"text\" \n            style=\"font-size:var(--font-size-small)\" \n            class=\"header-input mono-font\"\n            part=\"textbox textbox-spec-url\" \n            placeholder=\"Spec URL\" \n            value=\"${this.specUrl || ''}\" \n            @change=\"${this.onSpecUrlChange}\" \n            spellcheck=\"false\"\n          >\n          <div style=\"margin: 6px 5px 0 -24px; font-size:var(--font-size-regular); cursor:pointer;\">&#x21a9;</div> \n        `\n      } \n      ${(this.allowSpecFileLoad === 'false')\n        ? ''\n        : html`\n          <input id=\"spec-file\" \n            part = \"file-input\"\n            type=\"file\" \n            style=\"display:none\" \n            value=\"${this.specFile || ''}\" \n            @change=\"${this.onSpecFileChange}\" \n            spellcheck=\"false\"\n           >\n          <button class=\"m-btn primary only-large-screen\" style=\"margin-left:10px;\" part=\"btn btn-fill\" @click=\"${this.onFileLoadClick}\"> LOCAL JSON FILE </button>\n        `\n      }\n      <slot name=\"header\"></slot>\n      ${(this.allowSearch === 'false' || 'read focused'.includes(this.renderStyle))\n        ? ''\n        : html`  \n          <input id=\"search\" class=\"header-input\" type=\"text\" part=\"textbox textbox-header-filter\" placeholder=\"Filter\" @change=\"${this.onSearchChange}\" style=\"max-width:130px;margin-left:10px;\" spellcheck=\"false\" >\n          <div style=\"margin: 6px 5px 0 -24px; font-size:var(--font-size-regular); cursor:pointer;\">&#x21a9;</div>\n        `\n      }\n      \n      ${(this.allowAdvancedSearch === 'false' || 'read focused'.includes(this.renderStyle))\n        ? ''\n        : html`\n          <button class=\"m-btn primary only-large-screen\" part=\"btn btn-fill btn-search\" style=\"margin-left:10px;\" @click=\"${this.onShowSearchModalClicked}\">\n            Search\n          </button>\n        `\n      }\n    </div>\n    </header>`;\n  }\n/* eslint-enable indent */\n", "import { LitElement, html, css } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\nimport { schemaInObjectNotation, generateExample } from '~/utils/schema-utils';\nimport FontStyles from '~/styles/font-styles';\nimport FlexStyles from '~/styles/flex-styles';\nimport TableStyles from '~/styles/table-styles';\nimport InputStyles from '~/styles/input-styles';\nimport TabStyles from '~/styles/tab-styles';\nimport BorderStyles from '~/styles/border-styles';\nimport CustomStyles from '~/styles/custom-styles';\nimport '~/components/json-tree';\nimport '~/components/schema-tree';\nimport '~/components/schema-table';\n\nexport default class ApiResponse extends LitElement {\n  constructor() {\n    super();\n    this.selectedStatus = '';\n    this.headersForEachRespStatus = {};\n    this.mimeResponsesForEachStatus = {};\n    this.activeSchemaTab = 'schema';\n  }\n\n  static get properties() {\n    return {\n      callback: { type: String },\n      webhook: { type: String },\n      responses: { type: Object },\n      parser: { type: Object },\n      schemaStyle: { type: String, attribute: 'schema-style' },\n      renderStyle: { type: String, attribute: 'render-style' },\n      selectedStatus: { type: String, attribute: 'selected-status' },\n      selectedMimeType: { type: String, attribute: 'selected-mime-type' },\n      activeSchemaTab: { type: String, attribute: 'active-schema-tab' },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n      schemaHideReadOnly: { type: String, attribute: 'schema-hide-read-only' },\n      schemaHideWriteOnly: { type: String, attribute: 'schema-hide-write-only' },\n    };\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      FlexStyles,\n      TabStyles,\n      TableStyles,\n      InputStyles,\n      BorderStyles,\n      css`\n      :where(button, input[type=\"checkbox\"], [tabindex=\"0\"]):focus-visible { box-shadow: var(--focus-shadow); }\n      :where(input[type=\"text\"], input[type=\"password\"], select, textarea):focus-visible { border-color: var(--primary-color); }\n      .resp-head{\n        vertical-align: middle;\n        padding:16px 0 8px;\n      }\n      .resp-head.divider{\n        border-top: 1px solid var(--border-color);\n        margin-top:10px;\n      }\n      .resp-status{ \n        font-weight:bold;\n        font-size:calc(var(--font-size-small) + 1px);\n      }\n      .resp-descr{\n        font-size:calc(var(--font-size-small) + 1px);\n        color:var(--light-fg);\n        text-align:left;\n      }\n      .top-gap{margin-top:16px;}\n      .example-panel{\n        font-size:var(--font-size-small);\n        margin:0;\n      }\n      .focused-mode,\n      .read-mode {\n        padding-top:24px;\n        margin-top:12px;\n        border-top: 1px dashed var(--border-color);\n      }`,\n      CustomStyles,\n    ];\n  }\n\n  render() {\n    return html`\n    <div class=\"col regular-font response-panel ${this.renderStyle}-mode\">\n      <div class=\" ${this.callback === 'true' ? 'tiny-title' : 'req-res-title'} \"> \n        ${this.callback === 'true' ? 'CALLBACK RESPONSE' : 'RESPONSE'}\n      </div>\n      <div>\n        ${this.responseTemplate()}\n      <div>  \n    </div>  \n    `;\n  }\n\n  resetSelection() {\n    this.selectedStatus = '';\n    this.selectedMimeType = '';\n  }\n\n  /* eslint-disable indent */\n  responseTemplate() {\n    if (!this.responses) { return ''; }\n    for (const statusCode in this.responses) {\n      if (!this.selectedStatus) {\n        this.selectedStatus = statusCode;\n      }\n      const allMimeResp = {};\n      for (const mimeResp in this.responses[statusCode]?.content) {\n        const mimeRespObj = this.responses[statusCode].content[mimeResp];\n        if (!this.selectedMimeType) {\n          this.selectedMimeType = mimeResp;\n        }\n        // Generate Schema\n        const schemaTree = schemaInObjectNotation(mimeRespObj.schema, {});\n        // Generate Example\n        const respExamples = generateExample(\n          mimeRespObj.schema,\n          mimeResp,\n          mimeRespObj.examples,\n          mimeRespObj.example,\n          this.callback === 'true' || this.webhook === 'true' ? false : true, // eslint-disable-line no-unneeded-ternary\n          this.callback === 'true' || this.webhook === 'true' ? true : false, // eslint-disable-line no-unneeded-ternary\n          mimeResp.includes('json') ? 'json' : 'text',\n        );\n        allMimeResp[mimeResp] = {\n          description: this.responses[statusCode].description,\n          examples: respExamples,\n          selectedExample: respExamples[0]?.exampleId || '',\n          schemaTree,\n        };\n      }\n      // Headers for each response status\n      const tempHeaders = [];\n      for (const key in this.responses[statusCode]?.headers) {\n        tempHeaders.push({ name: key, ...this.responses[statusCode].headers[key] });\n      }\n      this.headersForEachRespStatus[statusCode] = tempHeaders;\n      this.mimeResponsesForEachStatus[statusCode] = allMimeResp;\n    }\n    return html`\n      ${Object.keys(this.responses).length > 1\n        ? html`<div class='row' style='flex-wrap:wrap'>\n          ${Object.keys(this.responses).map((respStatus) => html`\n            ${respStatus === '$$ref' // Swagger-Client parser creates '$$ref' object if JSON references are used to create responses - this should be ignored\n              ? ''\n              : html`\n                <button \n                  @click=\"${() => {\n                    this.selectedStatus = respStatus;\n                    if (this.responses[respStatus].content && Object.keys(this.responses[respStatus].content)[0]) {\n                      this.selectedMimeType = Object.keys(this.responses[respStatus].content)[0]; // eslint-disable-line prefer-destructuring\n                    } else {\n                      this.selectedMimeType = undefined;\n                    }\n                  }}\"\n                  class='m-btn small ${this.selectedStatus === respStatus ? 'primary' : ''}'\n                  part=\"btn ${this.selectedStatus === respStatus ? 'btn-response-status btn-selected-response-status' : ' btn-response-status'}\"\n                  style='margin: 8px 4px 0 0'\n                > \n                  ${respStatus} \n                </button>`\n              }`)\n          }`\n        : html`<span>${Object.keys(this.responses)[0]}</span>`\n      }\n      </div>\n\n      ${Object.keys(this.responses).map((status) => html`\n        <div style = 'display: ${status === this.selectedStatus ? 'block' : 'none'}' >\n          <div class=\"top-gap\">\n            <span class=\"resp-descr m-markdown \">${unsafeHTML(marked(this.responses[status]?.description || ''))}</span>\n            ${(this.headersForEachRespStatus[status] && this.headersForEachRespStatus[status]?.length > 0)\n              ? html`${this.responseHeaderListTemplate(this.headersForEachRespStatus[status])}`\n              : ''\n            }\n          </div>\n          ${Object.keys(this.mimeResponsesForEachStatus[status]).length === 0\n            ? ''\n            : html`  \n              <div class=\"tab-panel col\">\n                <div class=\"tab-buttons row\" @click=\"${(e) => { if (e.target.tagName.toLowerCase() === 'button') { this.activeSchemaTab = e.target.dataset.tab; } }}\" >\n                  <button class=\"tab-btn ${this.activeSchemaTab === 'example' ? 'active' : ''}\" data-tab = 'example'>EXAMPLE </button>\n                  <button class=\"tab-btn ${this.activeSchemaTab !== 'example' ? 'active' : ''}\" data-tab = 'schema' >SCHEMA</button>\n                  <div style=\"flex:1\"></div>\n                  ${Object.keys(this.mimeResponsesForEachStatus[status]).length === 1\n                    ? html`<span class='small-font-size gray-text' style='align-self:center; margin-top:8px;'> ${Object.keys(this.mimeResponsesForEachStatus[status])[0]} </span>`\n                    : html`${this.mimeTypeDropdownTemplate(Object.keys(this.mimeResponsesForEachStatus[status]))}`\n                  }\n                </div>\n                ${this.activeSchemaTab === 'example'\n                  ? html`<div class ='tab-content col' style = 'flex:1;'>\n                      ${this.mimeExampleTemplate(this.mimeResponsesForEachStatus[status][this.selectedMimeType])}\n                    </div>`\n                  : html`<div class ='tab-content col' style = 'flex:1;'>\n                      ${this.mimeSchemaTemplate(this.mimeResponsesForEachStatus[status][this.selectedMimeType])}\n                    </div>`\n                }\n              </div>\n            `\n          }`)\n        }\n    `;\n  }\n\n  responseHeaderListTemplate(respHeaders) {\n    return html`\n      <div style=\"padding:16px 0 8px 0\" class=\"resp-headers small-font-size bold-text\">RESPONSE HEADERS</div> \n      <table role=\"presentation\" style=\"border-collapse: collapse; margin-bottom:16px; border:1px solid var(--border-color); border-radius: var(--border-radius)\" class=\"small-font-size mono-font\">\n        ${respHeaders.map((v) => html`\n          <tr>\n            <td style=\"padding:8px; vertical-align: baseline; min-width:120px; border-top: 1px solid var(--light-border-color); text-overflow: ellipsis;\">\n              ${v.name || ''}\n            </td> \n            <td style=\"padding:4px; vertical-align: baseline; padding:0 5px; border-top: 1px solid var(--light-border-color); text-overflow: ellipsis;\">\n              ${v.schema?.type || ''}\n            </td> \n            <td style=\"padding:8px; vertical-align: baseline; border-top: 1px solid var(--light-border-color);text-overflow: ellipsis;\">\n              <div class=\"m-markdown-small regular-font\" >${unsafeHTML(marked(v.description || ''))}</div>\n            </td>\n            <td style=\"padding:8px; vertical-align: baseline; border-top: 1px solid var(--light-border-color); text-overflow: ellipsis;\">\n              ${v.schema?.example || ''}\n            </td>\n          </tr>\n        `)}\n    </table>`;\n  }\n\n  mimeTypeDropdownTemplate(mimeTypes) {\n    return html`\n      <select aria-label='mime types' @change=\"${(e) => { this.selectedMimeType = e.target.value; }}\" style='margin-bottom: -1px; z-index:1'>\n        ${mimeTypes.map((mimeType) => html`<option value='${mimeType}' ?selected = '${mimeType === this.selectedMimeType}'> ${mimeType} </option>`)}\n      </select>`;\n  }\n\n  onSelectExample(e) {\n    const exampleContainerEl = e.target.closest('.example-panel');\n    const exampleEls = [...exampleContainerEl.querySelectorAll('.example')];\n\n    exampleEls.forEach((v) => {\n      v.style.display = v.dataset.example === e.target.value ? 'block' : 'none';\n    });\n  }\n\n  mimeExampleTemplate(mimeRespDetails) {\n    if (!mimeRespDetails) {\n      return html`\n        <pre style='color:var(--red)' class = '${this.renderStyle === 'read' ? 'read example-panel border pad-8-16' : 'example-panel border-top'}'> No example provided </pre>\n      `;\n    }\n    return html`\n      ${mimeRespDetails.examples.length === 1\n        ? html`\n          ${mimeRespDetails.examples[0].exampleFormat === 'json'\n            ? html`\n              ${mimeRespDetails.examples[0].exampleSummary && mimeRespDetails.examples[0].exampleSummary.length > 80 ? html`<div style=\"padding: 4px 0\"> ${mimeRespDetails.examples[0].exampleSummary} </div>` : ''}\n              ${mimeRespDetails.examples[0].exampleDescription ? html`<div class=\"m-markdown-small\" style=\"padding: 4px 0\"> ${unsafeHTML(marked(mimeRespDetails.examples[0].exampleDescription || ''))} </div>` : ''}\n              <json-tree \n                render-style = '${this.renderStyle}'\n                .data=\"${mimeRespDetails.examples[0].exampleValue}\"\n                class = 'example-panel ${this.renderStyle === 'read' ? 'border pad-8-16' : 'border-top pad-top-8'}'\n                exportparts = \"btn:btn, btn-fill:btn-fill, btn-copy:btn-copy\" \n              ></json-tree>`\n            : html`\n              ${mimeRespDetails.examples[0].exampleSummary && mimeRespDetails.examples[0].exampleSummary.length > 80 ? html`<div style=\"padding: 4px 0\"> ${mimeRespDetails.examples[0].exampleSummary} </div>` : ''}\n              ${mimeRespDetails.examples[0].exampleDescription ? html`<div class=\"m-markdown-small\" style=\"padding: 4px 0\"> ${unsafeHTML(marked(mimeRespDetails.examples[0].exampleDescription || ''))} </div>` : ''}\n              <pre class = 'example-panel ${this.renderStyle === 'read' ? 'border pad-8-16' : 'border-top pad-top-8'}'>${mimeRespDetails.examples[0].exampleValue}</pre>\n            `\n          }`\n        : html`\n          <span class = 'example-panel ${this.renderStyle === 'read' ? 'border pad-8-16' : 'border-top pad-top-8'}'>\n            <select aria-label='response examples' style=\"min-width:100px; max-width:100%\" @change='${(e) => this.onSelectExample(e)}'>\n              ${mimeRespDetails.examples.map((v) => html`<option value=\"${v.exampleId}\" ?selected=${v.exampleId === mimeRespDetails.selectedExample} > \n                ${v.exampleSummary.length > 80 ? v.exampleId : v.exampleSummary} \n              </option>`)}\n            </select>\n            ${mimeRespDetails.examples.map((v) => html`\n              <div class=\"example\" data-example = '${v.exampleId}' style = \"display: ${v.exampleId === mimeRespDetails.selectedExample ? 'block' : 'none'}\">\n                ${v.exampleSummary && v.exampleSummary.length > 80 ? html`<div style=\"padding: 4px 0\"> ${v.exampleSummary} </div>` : ''}\n                ${v.exampleDescription ? html`<div class=\"m-markdown-small\"  style=\"padding: 4px 0\"> ${unsafeHTML(marked(v.exampleDescription || ''))} </div>` : ''}\n                ${v.exampleFormat === 'json'\n                  ? html`\n                    <json-tree \n                      render-style = '${this.renderStyle}'\n                      .data = '${v.exampleValue}'\n                      exportparts = \"btn:btn, btn-fill:btn-fill, btn-copy:btn-copy\" \n                    ></json-tree>`\n                  : html`<pre>${v.exampleValue}</pre>`\n                }\n              </div>  \n            `)}\n          </span>  \n        `\n      }\n    `;\n  }\n\n  mimeSchemaTemplate(mimeRespDetails) {\n    if (!mimeRespDetails) {\n      return html`\n        <pre style='color:var(--red)' class = '${this.renderStyle === 'read' ? 'border pad-8-16' : 'border-top'}'> Schema not found</pre>\n      `;\n    }\n    return html`\n      ${this.schemaStyle === 'table'\n        ? html`\n          <schema-table\n            .data = \"${mimeRespDetails.schemaTree}\"\n            schema-expand-level = \"${this.schemaExpandLevel}\"\n            schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n            allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n            schema-hide-read-only = \"${this.schemaHideReadOnly}\"\n            schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n            exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n          > </schema-table> `\n        : html`\n          <schema-tree\n            .data = '${mimeRespDetails.schemaTree}'\n            schema-expand-level = \"${this.schemaExpandLevel}\"\n            schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n            allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n            schema-hide-read-only = \"${this.schemaHideReadOnly}\"\n            schema-hide-write-only = \"${this.schemaHideWriteOnly}\"\n            exportparts = \"schema-description:schema-description, schema-multiline-toggle:schema-multiline-toggle\"\n          > </schema-tree>`\n      }`;\n  }\n  /* eslint-enable indent */\n}\n\n// Register the element with the browser\ncustomElements.define('api-response', ApiResponse);\n", "import { html } from 'lit';\nimport '~/components/dialog-box';\n\n/* eslint-disable indent */\nexport default function searchByPropertiesModalTemplate() {\n  document.addEventListener('close', () => { this.showAdvancedSearchDialog = false; });\n  document.addEventListener('open', this.onOpenSearchDialog);\n\n  return html`\n    <dialog-box \n      heading=\"Search\" \n      show=\"${(!!this.showAdvancedSearchDialog)}\"\n    >\n      <span class=\"advanced-search-options\">\n        <input\n          style=\"width:100%; padding-right:20px;\"\n          type=\"text\"\n          part=\"textbox textbox-search-dialog\"\n          placeholder=\"search text...\"\n          spellcheck=\"false\"\n          @keyup = \"${(e) => this.onAdvancedSearch(e, 400)}\"\n        >\n        <div style=\"display:flex; margin:8px 0 24px;\">\n          <div>\n            <input style=\"cursor:pointer;\" type=\"checkbox\" part=\"checkbox checkbox-search-dialog\" id=\"search-api-path\" checked @change = \"${(e) => this.onAdvancedSearch(e, 0)}\">\n            <label for=\"search-api-path\" style=\"cursor:pointer;\"> API Path </label>\n            </div>\n          <div style=\"margin-left: 16px;\">\n            <input style=\"cursor:pointer;\" type=\"checkbox\" part=\"checkbox checkbox-search-dialog\" id=\"search-api-descr\" checked @change = \"${(e) => this.onAdvancedSearch(e, 0)}\">\n            <label style=\"cursor:pointer;\" for=\"search-api-descr\"> API Description </label>\n          </div>\n          <div style=\"margin-left: 16px;\">\n            <input style=\"cursor:pointer;\" type=\"checkbox\" part=\"checkbox checkbox-search-dialog\" id=\"search-api-params\" @change = \"${(e) => this.onAdvancedSearch(e, 0)}\">\n            <label style=\"cursor:pointer;\" for=\"search-api-params\"> API Parameters </label>\n          </div>\n          <div style=\"margin-left: 16px;\">\n            <input style=\"cursor:pointer;\" type=\"checkbox\" part=\"checkbox checkbox-search-dialog\" id=\"search-api-request-body\" @change = \"${(e) => this.onAdvancedSearch(e, 0)}\">\n            <label style=\"cursor:pointer;\" for=\"search-api-request-body\"> Request Body Parameters </label>\n          </div>\n          <div style=\"margin-left: 16px;\">\n            <input style=\"cursor:pointer;\" type=\"checkbox\" part=\"checkbox checkbox-search-dialog\" id=\"search-api-resp-descr\" @change = \"${(e) => this.onAdvancedSearch(e, 0)}\">\n            <label style=\"cursor:pointer;\" for=\"search-api-resp-descr\"> Response Description </label>\n          </div>\n        </div>\n      </span>\n      \n      ${this.advancedSearchMatches?.map((path) => html`\n      <div\n        class=\"mono-font small-font-size hover-bg\"\n        style='padding: 5px; cursor: pointer; border-bottom: 1px solid var(--light-border-color); ${path.deprecated ? 'filter:opacity(0.5);' : ''}' \n        data-content-id='${path.elementId}'\n        tabindex = '0'\n        @click=\"${\n          (e) => {\n            this.matchPaths = ''; // clear quick filter if applied\n            this.showAdvancedSearchDialog = false; // Hide Search Dialog\n            this.requestUpdate();\n            this.scrollToEventTarget(e, true);\n          }\n        }\"\n      > \n        <span class=\"upper bold-text method-fg ${path.method}\">${path.method}</span> \n        <span>${path.path}</span>\n        <span class=\"regular-font gray-text\">${path.summary}</span>\n      </div>\n    `)\n    }\n    </dialog-box>\n  `;\n}\n/* eslint-enable indent */\n", "import { LitElement, html } from 'lit';\nimport DialogBoxStyles from '~/styles/dialog-box-styles';\n\nexport default class DialogBox extends LitElement {\n  static get properties() {\n    return {\n      heading: { type: String, attribute: 'heading' },\n      show: { type: String, attribute: 'show' },\n    };\n  }\n\n  static get styles() {\n    return [DialogBoxStyles];\n  }\n\n  connectedCallback() {\n    super.connectedCallback();\n    document.addEventListener('keydown', (e) => {\n      if (e.code === 'Escape') {\n        this.onClose();\n      }\n    });\n  }\n\n  attributeChangedCallback(name, oldVal, newVal) {\n    if (oldVal !== newVal) {\n      if (name === 'heading') {\n        this.heading = newVal;\n      }\n      if (name === 'show') {\n        this.show = newVal;\n        if (newVal === 'true') {\n          document.dispatchEvent(new CustomEvent('open', {\n            bubbles: true,\n            composed: true,\n            detail: this,\n          }));\n        }\n      }\n    }\n    super.attributeChangedCallback(name, oldVal, newVal);\n  }\n\n  /* eslint-disable indent */\n  render() {\n    return html`\n    ${this.show === 'true'\n      ? html`\n        <div class=\"dialog-box-overlay\">\n          <div class=\"dialog-box\">\n            <header class=\"dialog-box-header\">\n              <span class=\"dialog-box-title\">${this.heading}</span>\n              <button type=\"button\" @click=\"${this.onClose}\">&times;</button>\n            </header>\n            <div class=\"dialog-box-content\">\n              <slot></slot>\n            </div>\n          </div>\n        </div>`\n      : ''\n    }`;\n  }\n  /* eslint-enable indent */\n\n  onClose() {\n    document.dispatchEvent(new CustomEvent('close', {\n      bubbles: true,\n      composed: true,\n    }));\n  }\n}\n\ncustomElements.define('dialog-box', DialogBox);\n", "import { html } from 'lit';\nimport ColorUtils from '~/utils/color-utils';\n/* Generates an schema object containing type and constraint info */\nexport default function setTheme(baseTheme, theme = {}) {\n  let newTheme = {};\n\n  // Common Theme colors\n  const primaryColor = theme.primaryColor ? theme.primaryColor : baseTheme === 'dark' ? '#f76b39' : '#ff591e';\n  const primaryColorInvert = ColorUtils.color.invert(primaryColor);\n  const primaryColorTrans = ColorUtils.color.opacity(primaryColor, '0.4');\n\n  // Dark and Light Theme colors\n  if (baseTheme === 'dark') {\n    const bg1 = theme.bg1 ? theme.bg1 : '#2a2b2c';\n    const fg1 = theme.fg1 ? theme.fg1 : '#bbb';\n\n    const bg2 = theme.bg2 ? theme.bg2 : ColorUtils.color.brightness(bg1, 5); // or #383838;\n    const bg3 = theme.bg3 ? theme.bg3 : ColorUtils.color.brightness(bg1, 17); // or #444;\n    const lightBg = theme.bg3 ? theme.bg3 : ColorUtils.color.brightness(bg1, 35);\n    const fg2 = theme.fg2 ? theme.fg2 : ColorUtils.color.brightness(fg1, -15); // or #ababab\n    const fg3 = theme.fg3 ? theme.fg3 : ColorUtils.color.brightness(fg1, -20); // or #aaa\n    const lightFg = theme.fg3 ? theme.fg3 : ColorUtils.color.brightness(fg1, -65); // or #777\n    const inlineCodeFg = theme.inlineCodeFg ? theme.inlineCodeFg : '#aaa';\n    const selectionBg = '#bbb';\n    const selectionFg = '#eee';\n\n    const headerColor = theme.headerColor ? theme.headerColor : ColorUtils.color.brightness(bg1, 10);\n\n    const navBgColor = theme.navBgColor ? theme.navBgColor : ColorUtils.color.brightness(bg1, 10);\n    const navTextColor = theme.navTextColor ? theme.navTextColor : ColorUtils.color.opacity(ColorUtils.color.invert(navBgColor), '0.50');\n    const navHoverBgColor = theme.navHoverBgColor ? theme.navHoverBgColor : ColorUtils.color.brightness(navBgColor, -15);\n    const navHoverTextColor = theme.navHoverTextColor ? theme.navHoverTextColor : ColorUtils.color.invert(navBgColor);\n    const navAccentColor = theme.navAccentColor ? theme.navAccentColor : ColorUtils.color.brightness(primaryColor, 25);\n    const navAccentTextColor = theme.navAccentTextColor ? theme.navAccenttextColor : ColorUtils.color.invert(navAccentColor);\n\n    const overlayBg = 'rgba(80, 80, 80, 0.4)';\n\n    newTheme = {\n      bg1,\n      bg2,\n      bg3,\n      lightBg,\n      fg1,\n      fg2,\n      fg3,\n      lightFg,\n      inlineCodeFg,\n      primaryColor,\n      primaryColorTrans,\n      primaryColorInvert,\n      selectionBg,\n      selectionFg,\n      overlayBg,\n      navBgColor,\n      navTextColor,\n      navHoverBgColor,\n      navHoverTextColor,\n      navAccentColor,\n      navAccentTextColor,\n      headerColor,\n      headerColorInvert: ColorUtils.color.invert(headerColor),\n      headerColorDarker: ColorUtils.color.brightness(headerColor, -20),\n      headerColorBorder: ColorUtils.color.brightness(headerColor, 10),\n\n      borderColor: theme.borderColor || ColorUtils.color.brightness(bg1, 20), // #555\n      lightBorderColor: theme.lightBorderColor || ColorUtils.color.brightness(bg1, 15), // #444\n      codeBorderColor: theme.codeBorderColor || ColorUtils.color.brightness(bg1, 30),\n\n      inputBg: theme.inputBg || ColorUtils.color.brightness(bg1, -5), // #2f2f2f\n      placeHolder: theme.placeHolder || ColorUtils.color.opacity(fg1, '0.3'),\n      hoverColor: theme.hoverColor || ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      red: theme.red ? theme.red : '#F06560',\n      lightRed: theme.lightRed ? theme.lightRed : ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      pink: theme.pink ? theme.pink : '#ffb2b2',\n      lightPink: theme.lightPink || ColorUtils.color.brightness(bg1, -10),\n\n      green: theme.green || '#7ec699',\n      lightGreen: theme.lightGreen || ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      blue: theme.blue || '#71b7ff',\n      lightBlue: theme.lightBlue || ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      orange: theme.orange ? theme.orange : '#f08d49',\n      lightOrange: theme.lightOrange || ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      yellow: theme.yellow || '#827717',\n      lightYellow: theme.lightYellow || ColorUtils.color.brightness(bg1, -10), // #2a2a2a\n\n      purple: theme.purple || '#786FF1',\n      brown: theme.brown || '#D4AC0D',\n\n      codeBg: theme.codeBg || ColorUtils.color.opacity(ColorUtils.color.brightness(bg1, -15), 0.7),\n      codeFg: theme.codeFg || '#aaa',\n      codePropertyColor: theme.codePropertyColor || '#f8c555',\n      codeKeywordColor: theme.codeKeywordColor || '#cc99cd',\n      codeOperatorColor: theme.codeOperatorColor || '#67cdcc',\n    };\n  } else {\n    const bg1 = (theme.bg1 ? theme.bg1 : '#fafbfc');\n    const fg1 = (theme.fg1 ? theme.fg1 : '#444444');\n    const bg2 = theme.bg2 ? theme.bg2 : ColorUtils.color.brightness(bg1, -5); // or '#fafafa'\n    const bg3 = theme.bg3 ? theme.bg3 : ColorUtils.color.brightness(bg1, -15); // or '#f6f6f6'\n    const lightBg = theme.bg3 ? theme.bg3 : ColorUtils.color.brightness(bg1, -45);\n    const fg2 = theme.fg2 ? theme.fg2 : ColorUtils.color.brightness(fg1, 17); // or '#555'\n    const fg3 = theme.fg3 ? theme.fg3 : ColorUtils.color.brightness(fg1, 30); // or #666\n    const lightFg = theme.fg3 ? theme.fg3 : ColorUtils.color.brightness(fg1, 70); // or #999\n    const inlineCodeFg = theme.inlineCodeFg ? theme.inlineCodeFg : 'brown';\n\n    const selectionBg = '#444';\n    const selectionFg = '#eee';\n\n    const headerColor = theme.headerColor ? theme.headerColor : ColorUtils.color.brightness(bg1, -180);\n\n    /*\n    const navBgColor = theme.navBgColor ? theme.navBgColor : ColorUtils.color.brightness(bg1, -10);\n    const navTextColor = theme.navTextColor ? theme.navTextColor : ColorUtils.color.brightness(fg1, 5);\n    const navHoverBgColor = theme.navHoverBgColor ? theme.navHoverBgColor : bg1;\n    const navHoverTextColor = theme.navHoverTextColor ? theme.navHoverTextColor : primaryColor;\n    const navAccentColor = theme.navAccentColor ? theme.navAccentColor : primaryColor;\n    */\n    const navBgColor = theme.navBgColor ? theme.navBgColor : ColorUtils.color.brightness(bg1, -200);\n    const navTextColor = theme.navTextColor ? theme.navTextColor : ColorUtils.color.opacity(ColorUtils.color.invert(navBgColor), '0.65');\n    const navHoverBgColor = theme.navHoverBgColor ? theme.navHoverBgColor : ColorUtils.color.brightness(navBgColor, -15);\n    const navHoverTextColor = theme.navHoverTextColor ? theme.navHoverTextColor : ColorUtils.color.invert(navBgColor);\n    const navAccentColor = theme.navAccentColor ? theme.navAccentColor : ColorUtils.color.brightness(primaryColor, 25);\n    const navAccentTextColor = theme.navAccentTextColor ? theme.navAccenttextColor : ColorUtils.color.invert(navAccentColor);\n    const overlayBg = 'rgba(0, 0, 0, 0.4)';\n\n    newTheme = {\n      bg1,\n      bg2,\n      bg3,\n      lightBg,\n      fg1,\n      fg2,\n      fg3,\n      lightFg,\n      inlineCodeFg,\n      primaryColor,\n      primaryColorTrans,\n      primaryColorInvert,\n      selectionBg,\n      selectionFg,\n      overlayBg,\n      navBgColor,\n      navTextColor,\n      navHoverBgColor,\n      navHoverTextColor,\n      navAccentColor,\n      navAccentTextColor,\n      headerColor,\n      headerColorInvert: ColorUtils.color.invert(headerColor),\n      headerColorDarker: ColorUtils.color.brightness(headerColor, -20),\n      headerColorBorder: ColorUtils.color.brightness(headerColor, 10),\n\n      borderColor: theme.borderColor || ColorUtils.color.brightness(bg1, -38),\n      lightBorderColor: theme.lightBorderColor || ColorUtils.color.brightness(bg1, -23),\n      codeBorderColor: theme.codeBorderColor || 'transparent',\n\n      inputBg: theme.inputBg || ColorUtils.color.brightness(bg1, 10), // #fff\n      placeHolder: theme.placeHolder || ColorUtils.color.brightness(lightFg, 20), // #dedede\n      hoverColor: theme.hoverColor || ColorUtils.color.brightness(bg1, -5), // # f1f1f1\n\n      red: theme.red || '#F06560',\n      lightRed: theme.lightRed || '#fff0f0',\n\n      pink: theme.pink ? theme.pink : '#990055',\n      lightPink: theme.lightPink ? theme.lightPink : '#ffb2b2',\n\n      green: theme.green || '#690',\n      lightGreen: theme.lightGreen || '#fbfff0',\n\n      blue: theme.blue || '#47AFE8',\n      lightBlue: theme.lightBlue || '#eff8fd',\n\n      orange: theme.orange || '#FF9900',\n      lightOrange: theme.lightOrange || '#fff5e6',\n\n      yellow: theme.yellow || '#827717',\n      lightYellow: theme.lightYellow || '#fff5cc',\n\n      purple: theme.purple || '#786FF1',\n      brown: theme.brown || '#D4AC0D',\n\n      codeBg: theme.codeBg || ColorUtils.color.opacity(ColorUtils.color.brightness(bg1, -15), 0.7),\n      codeFg: theme.codeFg || '#666',\n      codePropertyColor: theme.codePropertyColor || '#905',\n      codeKeywordColor: theme.codeKeywordColor || '#07a',\n      codeOperatorColor: theme.codeOperatorColor || '#9a6e3a',\n    };\n  }\n  return html`\n  <style>\n  *, *:before, *:after { box-sizing: border-box; }\n  \n  :host {\n    /* Common Styles - irrespective of themes */  \n    --border-radius: 2px;\n    --layout: ${this.layout || 'row'};\n    --font-mono: ${this.monoFont || 'Monaco, \"Andale Mono\", \"Roboto Mono\", Consolas, monospace'};\n    --font-regular: ${this.regularFont || '\"Open Sans\", Avenir, \"Segoe UI\", Arial, sans-serif'};\n    --scroll-bar-width: 8px;\n    --nav-item-padding: ${this.navItemSpacing === 'relaxed'\n    ? '10px 16px 10px 10px'\n    : (this.navItemSpacing === 'compact'\n      ? '5px 16px 5px 10px'\n      : '7px 16px 7px 10px')};\n    \n    --resp-area-height: ${this.responseAreaHeight};\n    --font-size-small: ${this.fontSize === 'default' ? '12px' : (this.fontSize === 'large' ? '13px' : '14px')};\n    --font-size-mono: ${this.fontSize === 'default' ? '13px' : (this.fontSize === 'large' ? '14px' : '15px')};\n    --font-size-regular: ${this.fontSize === 'default' ? '14px' : (this.fontSize === 'large' ? '15px' : '16px')};\n    --dialog-z-index: 1000;\n\n    --focus-shadow: 0 0 0 1px transparent, 0 0 0 3px ${newTheme.primaryColorTrans};\n\n    /* Theme specific styles */  \n    --bg:${newTheme.bg1};\n    --bg2:${newTheme.bg2};\n    --bg3:${newTheme.bg3};\n    --light-bg:${newTheme.lightBg};\n    --fg:${newTheme.fg1};\n    --fg2:${newTheme.fg2};\n    --fg3:${newTheme.fg3};\n    --light-fg:${newTheme.lightFg};\n    --selection-bg:${newTheme.selectionBg};\n    --selection-fg:${newTheme.selectionFg};\n    --overlay-bg:${newTheme.overlayBg};\n    \n    /* Border Colors */\n    --border-color:${newTheme.borderColor};\n    --light-border-color:${newTheme.lightBorderColor};\n    --code-border-color:${newTheme.codeBorderColor};\n\n    --input-bg:${newTheme.inputBg};\n    --placeholder-color:${newTheme.placeHolder};\n    --hover-color:${newTheme.hoverColor};\n    --red:${newTheme.red};\n    --light-red:${newTheme.lightRed};\n    --pink:${newTheme.pink};\n    --light-pink:${newTheme.lightPink};\n    --green:${newTheme.green};\n    --light-green:${newTheme.lightGreen};\n    --blue:${newTheme.blue};\n    --light-blue:${newTheme.lightBlue};\n    --orange:${newTheme.orange};\n    --light-orange:${newTheme.lightOrange};\n    --yellow:${newTheme.yellow};\n    --light-yellow:${newTheme.lightYellow};\n    --purple:${newTheme.purple};\n    --brown:${newTheme.brown};\n\n    /* Header Color */\n    --header-bg:${newTheme.headerColor};\n    --header-fg:${newTheme.headerColorInvert};\n    --header-color-darker:${newTheme.headerColorDarker};\n    --header-color-border:${newTheme.headerColorBorder};\n\n    /* Nav Colors */  \n    --nav-bg-color:${newTheme.navBgColor};\n    --nav-text-color:${newTheme.navTextColor};\n    --nav-hover-bg-color:${newTheme.navHoverBgColor};\n    --nav-hover-text-color:${newTheme.navHoverTextColor};\n    --nav-accent-color:${newTheme.navAccentColor};\n    --nav-accent-text-color:${newTheme.navAccentTextColor};\n\n    /* Nav API Method Colors*/\n    --nav-get-color:${newTheme.blue};\n    --nav-put-color:${newTheme.orange};\n    --nav-post-color:${newTheme.green};\n    --nav-delete-color:${newTheme.red};\n    --nav-head-color:${newTheme.yellow};\n\n    /* Primary Colors */  \n    --primary-color:${newTheme.primaryColor};\n    --primary-color-invert:${newTheme.primaryColorInvert};\n    --primary-color-trans:${newTheme.primaryColorTrans};\n\n    /*Code Syntax Color*/\n    --code-bg:${newTheme.codeBg};\n    --code-fg:${newTheme.codeFg};\n    --inline-code-fg:${newTheme.inlineCodeFg};\n    --code-property-color:${newTheme.codePropertyColor};\n    --code-keyword-color:${newTheme.codeKeywordColor};\n    --code-operator-color:${newTheme.codeOperatorColor};\n  }\n  </style>`;\n}\n", "import { html } from 'lit';\n\n// Templates\nimport expandedEndpointTemplate from '~/templates/expanded-endpoint-template';\nimport focusedEndpointTemplate from '~/templates/focused-endpoint-template';\nimport overviewTemplate from '~/templates/overview-template';\nimport endpointTemplate from '~/templates/endpoint-template';\nimport serverTemplate from '~/templates/server-template';\nimport securitySchemeTemplate, { recoverPersistedApiKeys } from '~/templates/security-scheme-template';\nimport headerTemplate from '~/templates/header-template';\nimport navbarTemplate from '~/templates/navbar-template';\nimport advancedSearchTemplate from '~/templates/advance-search-template';\nimport SetTheme from '~/utils/theme';\nimport { isValidHexColor } from '~/utils/color-utils';\n\nexport default function mainBodyTemplate(isMini = false, showExpandCollapse = true, showTags = true, pathsExpanded = false) {\n  if (!this.resolvedSpec) {\n    return '';\n  }\n  if (this.persistAuth === 'true') {\n    recoverPersistedApiKeys.call(this);\n  }\n  const newTheme = {\n    bg1: isValidHexColor(this.bgColor) ? this.bgColor : '',\n    fg1: isValidHexColor(this.textColor) ? this.textColor : '',\n    headerColor: isValidHexColor(this.headerColor) ? this.headerColor : '',\n    primaryColor: isValidHexColor(this.primaryColor) ? this.primaryColor : '',\n    navBgColor: isValidHexColor(this.navBgColor) ? this.navBgColor : '',\n    navTextColor: isValidHexColor(this.navTextColor) ? this.navTextColor : '',\n    navHoverBgColor: isValidHexColor(this.navHoverBgColor) ? this.navHoverBgColor : '',\n    navHoverTextColor: isValidHexColor(this.navHoverTextColor) ? this.navHoverTextColor : '',\n    navAccentColor: isValidHexColor(this.navAccentColor) ? this.navAccentColor : '',\n    navAccentTextColor: isValidHexColor(this.navAccentTextColor) ? this.navAccentTextColor : '',\n  };\n  /* eslint-disable indent */\n  if (this.resolvedSpec.specLoadError) {\n    if (isMini) {\n      return html`\n        ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n        <div style='display:flex; align-items:center; border:1px dashed var(--border-color); height:42px; padding:5px; font-size:var(--font-size-small); color:var(--red); font-family:var(--font-mono)'> ${this.resolvedSpec.info.description} </div>\n      `;\n    }\n    return html`\n      ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n      <!-- Header -->\n      ${headerTemplate.call(this)}\n      <main class='main-content regular-font' part='section-main-content'>\n        <slot></slot>\n        <div style='margin:24px; text-align: center;'>\n          <h1 style='color: var(--red)'> ${this.resolvedSpec.info.title} </h1>\n          <div style='font-family:var(--font-mono)'> ${this.resolvedSpec.info.description} </div>\n        </div>\n      </main>  \n    `;\n  }\n  if (this.resolvedSpec.isSpecLoading) {\n    return html`\n      ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n      <main class='main-content regular-font' part='section-main-content'>\n        <slot></slot>\n        <div class='main-content-inner--${this.renderStyle}-mode'>\n          <div class='loader'></div>\n        </div>\n      </main>\n    `;\n  }\n\n  return html`\n    ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n\n    <!-- Header -->\n    ${this.showHeader === 'false' ? '' : headerTemplate.call(this)}\n    \n    <!-- Advanced Search -->\n    ${this.allowAdvancedSearch === 'false' ? '' : advancedSearchTemplate.call(this)}\n\n    <div id='the-main-body' class='body ${this.cssClasses}' dir='${this.pageDirection}' >\n      <!-- Side Nav -->\n      ${((this.renderStyle === 'read' || this.renderStyle === 'focused')\n          && this.showSideNav === 'true'\n          && this.resolvedSpec\n        ) ? navbarTemplate.call(this) : ''\n      }\n\n      <!-- Main Content -->\n      <main class='main-content regular-font' tabindex='-1' part='section-main-content'>\n        <slot></slot>\n        <div class='main-content-inner--${this.renderStyle}-mode'>\n          ${this.loading === true\n            ? html`<div class='loader'></div>`\n            : html`\n              ${this.loadFailed === true\n                ? html`<div style='text-align: center;margin: 16px;'> Unable to load the Spec</div>`\n                : html`\n                  <div class='operations-root' @click='${(e) => { this.handleHref(e); }}'>\n                  ${this.renderStyle === 'focused'\n                    ? html`${focusedEndpointTemplate.call(this)}`\n                    : html`\n                      ${this.showInfo === 'true' ? overviewTemplate.call(this) : ''}\n                      ${this.allowServerSelection === 'true' ? serverTemplate.call(this) : ''}\n                      ${this.allowAuthentication === 'true' ? securitySchemeTemplate.call(this) : ''}\n                      <div id='operations-top' class='observe-me'>\n                        <slot name='operations-top'></slot>\n                      </div>  \n                      ${this.renderStyle === 'read'\n                        ? expandedEndpointTemplate.call(this)\n                        : endpointTemplate.call(this, showExpandCollapse, showTags, pathsExpanded)\n                      }\n                    `\n                  }\n                  </div>\n                `\n              }`\n          }\n        </div>\n        <slot name='footer'></slot>\n      </main>\n    </div>  \n  `;\n}\n/* eslint-enable indent */\n", "import { css, LitElement, unsafeCSS } from 'lit';\nimport { marked } from 'marked';\nimport Prism from 'prismjs';\nimport 'prismjs/components/prism-css';\nimport 'prismjs/components/prism-yaml';\nimport 'prismjs/components/prism-go';\nimport 'prismjs/components/prism-java';\nimport 'prismjs/components/prism-json';\nimport 'prismjs/components/prism-bash';\nimport 'prismjs/components/prism-python';\nimport 'prismjs/components/prism-http';\nimport 'prismjs/components/prism-csharp';\n\n// Styles\nimport FontStyles from '~/styles/font-styles';\nimport InputStyles from '~/styles/input-styles';\nimport FlexStyles from '~/styles/flex-styles';\nimport TableStyles from '~/styles/table-styles';\nimport EndpointStyles from '~/styles/endpoint-styles';\nimport PrismStyles from '~/styles/prism-styles';\nimport TabStyles from '~/styles/tab-styles';\nimport NavStyles from '~/styles/nav-styles';\nimport InfoStyles from '~/styles/info-styles';\nimport CustomStyles from '~/styles/custom-styles';\n// import { expandCollapseNavBarTag } from '@/templates/navbar-template';\nimport { advancedSearch, pathIsInSearch, componentIsInSearch, rapidocApiKey, sleep } from '~/utils/common-utils';\nimport ProcessSpec from '~/utils/spec-parser';\nimport mainBodyTemplate from '~/templates/main-body-template';\nimport { applyApiKey, onClearAllApiKeys } from '~/templates/security-scheme-template';\nimport { setApiServer } from '~/templates/server-template';\n\nexport default class RapiDoc extends LitElement {\n  constructor() {\n    super();\n    const intersectionObserverOptions = {\n      root: this.getRootNode().host,\n      rootMargin: '-50px 0px -50px 0px', // when the element is visible 100px from bottom\n      threshold: 0,\n    };\n    this.showSummaryWhenCollapsed = true;\n    // Will activate intersection observer only after spec load and hash analyze\n    // to scroll to the proper element without being reverted by observer behavior\n    this.isIntersectionObserverActive = false;\n    this.intersectionObserver = new IntersectionObserver((entries) => { this.onIntersect(entries); }, intersectionObserverOptions);\n  }\n\n  static get properties() {\n    return {\n      // Heading\n      headingText: { type: String, attribute: 'heading-text' },\n      gotoPath: { type: String, attribute: 'goto-path' },\n\n      // Spec\n      updateRoute: { type: String, attribute: 'update-route' },\n      routePrefix: { type: String, attribute: 'route-prefix' },\n      specUrl: { type: String, attribute: 'spec-url' },\n      sortTags: { type: String, attribute: 'sort-tags' },\n      generateMissingTags: { type: String, attribute: 'generate-missing-tags' },\n      sortEndpointsBy: { type: String, attribute: 'sort-endpoints-by' },\n      specFile: { type: String, attribute: false },\n\n      // UI Layouts\n      layout: { type: String },\n      renderStyle: { type: String, attribute: 'render-style' },\n      defaultSchemaTab: { type: String, attribute: 'default-schema-tab' },\n      responseAreaHeight: { type: String, attribute: 'response-area-height' },\n      fillRequestFieldsWithExample: { type: String, attribute: 'fill-request-fields-with-example' },\n      persistAuth: { type: String, attribute: 'persist-auth' },\n      onNavTagClick: { type: String, attribute: 'on-nav-tag-click' },\n\n      // Schema Styles\n      schemaStyle: { type: String, attribute: 'schema-style' },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      schemaHideReadOnly: { type: String, attribute: 'schema-hide-read-only' },\n      schemaHideWriteOnly: { type: String, attribute: 'schema-hide-write-only' },\n\n      // API Server\n      apiKeyName: { type: String, attribute: 'api-key-name' },\n      apiKeyLocation: { type: String, attribute: 'api-key-location' },\n      apiKeyValue: { type: String, attribute: 'api-key-value' },\n      defaultApiServerUrl: { type: String, attribute: 'default-api-server' },\n      serverUrl: { type: String, attribute: 'server-url' },\n      oauthReceiver: { type: String, attribute: 'oauth-receiver' },\n\n      // Hide/Show Sections & Enable Disable actions\n      showHeader: { type: String, attribute: 'show-header' },\n      showSideNav: { type: String, attribute: 'show-side-nav' },\n      showInfo: { type: String, attribute: 'show-info' },\n      allowAuthentication: { type: String, attribute: 'allow-authentication' },\n      allowTry: { type: String, attribute: 'allow-try' },\n      showCurlBeforeTry: { type: String, attribute: 'show-curl-before-try' },\n      allowSpecUrlLoad: { type: String, attribute: 'allow-spec-url-load' },\n      allowSpecFileLoad: { type: String, attribute: 'allow-spec-file-load' },\n      allowSpecFileDownload: { type: String, attribute: 'allow-spec-file-download' },\n      allowSearch: { type: String, attribute: 'allow-search' },\n      allowAdvancedSearch: { type: String, attribute: 'allow-advanced-search' },\n      allowServerSelection: { type: String, attribute: 'allow-server-selection' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n      showComponents: { type: String, attribute: 'show-components' },\n      pageDirection: { type: String, attribute: 'page-direction' },\n\n      // Main Colors and Font\n      theme: { type: String },\n      bgColor: { type: String, attribute: 'bg-color' },\n      textColor: { type: String, attribute: 'text-color' },\n      headerColor: { type: String, attribute: 'header-color' },\n      primaryColor: { type: String, attribute: 'primary-color' },\n      fontSize: { type: String, attribute: 'font-size' },\n      regularFont: { type: String, attribute: 'regular-font' },\n      monoFont: { type: String, attribute: 'mono-font' },\n      loadFonts: { type: String, attribute: 'load-fonts' },\n      cssFile: { type: String, attribute: 'css-file' },\n      cssClasses: { type: String, attribute: 'css-classes' },\n\n      // Nav Bar Colors\n      navBgColor: { type: String, attribute: 'nav-bg-color' },\n      navTextColor: { type: String, attribute: 'nav-text-color' },\n      navHoverBgColor: { type: String, attribute: 'nav-hover-bg-color' },\n      navHoverTextColor: { type: String, attribute: 'nav-hover-text-color' },\n      navAccentColor: { type: String, attribute: 'nav-accent-color' },\n      navAccentTextColor: { type: String, attribute: 'nav-accent-text-color' },\n      navActiveItemMarker: { type: String, attribute: 'nav-active-item-marker' },\n      navItemSpacing: { type: String, attribute: 'nav-item-spacing' },\n      showMethodInNavBar: { type: String, attribute: 'show-method-in-nav-bar' },\n      usePathInNavBar: { type: String, attribute: 'use-path-in-nav-bar' },\n      infoDescriptionHeadingsInNavBar: { type: String, attribute: 'info-description-headings-in-navbar' },\n\n      // Fetch Options\n      fetchCredentials: { type: String, attribute: 'fetch-credentials' },\n\n      // Filters\n      matchPaths: { type: String, attribute: 'match-paths' },\n      matchType: { type: String, attribute: 'match-type' },\n\n      // Internal Properties\n      loading: { type: Boolean }, // indicates spec is being loaded\n      focusedElementId: { type: String }, // updating the focusedElementId will automatically render appropriate section in focused mode\n      showAdvancedSearchDialog: { type: Boolean },\n      advancedSearchMatches: { type: Object },\n    };\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      InputStyles,\n      FlexStyles,\n      TableStyles,\n      EndpointStyles,\n      PrismStyles,\n      TabStyles,\n      NavStyles,\n      InfoStyles,\n      css`\n      :host {\n        display:flex;\n        flex-direction: column;\n        min-width:360px;\n        width:100%;\n        height:100%;\n        margin:0;\n        padding:0;\n        overflow: hidden;\n        letter-spacing:normal;\n        color:var(--fg);\n        background-color:var(--bg);\n        font-family:var(--font-regular);\n      }\n      :where(button, input[type=\"checkbox\"], [tabindex=\"0\"]):focus-visible { box-shadow: var(--focus-shadow); }\n      :where(input[type=\"text\"], input[type=\"password\"], select, textarea):focus-visible { border-color: var(--primary-color); }\n    .body {\n        display:flex;\n        height:100%;\n        width:100%;\n        overflow:hidden;\n      }\n      .main-content { \n        margin:0;\n        padding: 0; \n        display:block;\n        flex:1;\n        height:100%;\n        overflow-y: auto;\n        overflow-x: hidden;\n        scrollbar-width: thin;\n        scrollbar-color: var(--border-color) transparent;\n      }\n\n      .main-content-inner--view-mode {\n        padding: 0 8px;\n      }\n      .main-content::-webkit-scrollbar {\n        width: 8px;\n        height: 8px;\n      }\n      .main-content::-webkit-scrollbar-track {\n        background:transparent;\n      }\n      .main-content::-webkit-scrollbar-thumb {\n        background-color: var(--border-color);\n      }\n\n      .section-gap.section-tag {\n        border-bottom:1px solid var(--border-color);\n      }\n      .section-gap,\n      .section-gap--focused-mode,\n      .section-gap--read-mode { \n        padding: 0px 4px; \n      }\n      .section-tag-header {\n        position:relative;\n        cursor: n-resize;\n        padding: 12px 0;\n      }\n      .collapsed .section-tag-header:hover{\n        cursor: s-resize;\n      }\n\n      .section-tag-header:hover{\n        background-image: linear-gradient(to right, rgba(0,0,0,0), var(--border-color), rgba(0,0,0,0));\n      }\n\n      .section-tag-header:hover::after {\n        position:absolute;\n        margin-left:-24px;\n        font-size:20px;\n        top: calc(50% - 14px);\n        color:var(--primary-color);\n        content: '⬆'; \n      }\n\n      .collapsed .section-tag-header::after {\n        position:absolute;\n        margin-left:-24px;\n        font-size:20px;\n        top: calc(50% - 14px);\n        color: var(--border-color);\n        content: '⬇'; \n      }\n      .collapsed .section-tag-header:hover::after {\n        color:var(--primary-color);\n      }\n\n      .collapsed .section-tag-body {\n        display:none;\n      }\n\n      .logo {\n        height:36px;\n        width:36px;\n        margin-left:5px; \n      }\n      .only-large-screen-flex,\n      .only-large-screen{\n        display:none;\n      }\n      .tag.title {\n        text-transform: uppercase;\n      }\n      .main-header {\n        background-color:var(--header-bg);\n        color:var(--header-fg);\n        width:100%;\n      }\n      .header-title {\n        font-size:calc(var(--font-size-regular) + 8px); \n        padding:0 8px;\n      }\n      input.header-input{\n        background:var(--header-color-darker);\n        color:var(--header-fg);\n        border:1px solid var(--header-color-border);\n        flex:1; \n        padding-right:24px;\n        border-radius:3px;\n      }\n      input.header-input::placeholder {\n        opacity:0.4;\n      }\n      .loader {\n        margin: 16px auto 16px auto; \n        border: 4px solid var(--bg3);\n        border-radius: 50%;\n        border-top: 4px solid var(--primary-color);\n        width: 36px;\n        height: 36px;\n        animation: spin 2s linear infinite;\n      }\n      .expanded-endpoint-body { \n        position: relative;\n        padding: 6px 0px; \n      }\n      .expanded-endpoint-body .tag-description {\n        background: var(--code-bg);\n        border-radius: var(--border-radius);\n        transition: max-height .2s ease-out;\n      }\n      .expanded-endpoint-body .tag-icon {\n        transition: transform .2s ease-out;\n      }\n      .expanded-endpoint-body .tag-icon.expanded {\n        transform: rotate(180deg);\n      }\n      .divider { \n        border-top: 2px solid var(--border-color);\n        margin: 24px 0;\n        width:100%;\n      }\n\n      .tooltip {\n        cursor:pointer;\n        border: 1px solid var(--border-color);\n        border-left-width: 4px;\n        margin-left:2px;\n      }\n      .tooltip a {\n        color: var(--fg2);\n        text-decoration: none;\n      }\n      .tooltip-text {\n        color: var(--fg2);\n        max-width: 400px;\n        position: absolute;\n        z-index:1;\n        background-color: var(--bg2);\n        visibility: hidden;\n\n        overflow-wrap: break-word;\n      }\n      .tooltip:hover {\n        color: var(--primary-color);\n        border-color: var(--primary-color);\n      }\n      .tooltip:hover a:hover {\n        color: var(--primary-color);\n      }\n\n      .tooltip:hover .tooltip-text {\n        visibility: visible;\n      }\n\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n\n      .nav-method { font-weight: bold; margin-right: 4px; font-size: calc(var(--font-size-small) - 2px); white-space: nowrap; }\n      .nav-method.false { display: none; }\n\n      .nav-method.as-colored-text.get { color:var(--nav-get-color); }\n      .nav-method.as-colored-text.put { color:var(--nav-put-color); }\n      .nav-method.as-colored-text.post { color:var(--nav-post-color); }\n      .nav-method.as-colored-text.delete { color:var(--nav-delete-color); }\n      .nav-method.as-colored-text.head, .nav-method.as-colored-text.patch, .nav-method.as-colored-text.options { color:var(--nav-head-color); }\n      \n      .nav-method.as-colored-block {\n        padding: 1px 4px;\n        min-width: 30px;\n        border-radius: 4px 0 0 4px;\n        color: #000;\n      }\n      .colored-block .nav-method.as-colored-block {\n        outline: 1px solid;\n      }\n\n      .nav-method.as-colored-block.get { background-color: var(--blue); }\n      .nav-method.as-colored-block.put { background-color: var(--orange); }\n      .nav-method.as-colored-block.post { background-color: var(--green); }\n      .nav-method.as-colored-block.delete { background-color: var(--red); }\n      .nav-method.as-colored-block.head, .nav-method.as-colored-block.patch , .nav-method.as-colored-block.options { \n        background-color: var(--yellow); \n      }\n\n      @media only screen and (min-width: 768px) {\n        .nav-bar {\n          width: 260px;\n          display:flex;\n        }\n        .only-large-screen{\n          display:block;\n        }\n        .only-large-screen-flex{\n          display:flex;\n        }\n        .section-gap { \n          padding: 0 0 0 24px; \n        }\n        .section-gap--focused-mode {\n          padding: 24px 8px; \n        }\n        .section-gap--read-mode { \n          padding: 24px 8px; \n        }\n        .endpoint-body {\n          position: relative;\n          padding:36px 0 48px 0;\n        }\n      }\n\n      @media only screen and (min-width: 1024px) {\n        .nav-bar {\n          width: ${unsafeCSS(this.fontSize === 'default' ? '300px' : this.fontSize === 'large' ? '315px' : '330px')};\n          display:flex;\n        }\n        .section-gap--focused-mode { \n          padding: 12px 80px 12px 80px; \n        }\n        .section-gap--read-mode { \n          padding: 24px 80px 12px 80px; \n        }\n      }`,\n      CustomStyles,\n    ];\n  }\n\n  // Startup\n  connectedCallback() {\n    super.connectedCallback();\n    const parent = this.parentElement;\n    if (parent) {\n      if (parent.offsetWidth === 0 && parent.style.width === '') {\n        parent.style.width = '100vw';\n      }\n      if (parent.offsetHeight === 0 && parent.style.height === '') {\n        parent.style.height = '100vh';\n      }\n      if (parent.tagName === 'BODY') {\n        if (!parent.style.marginTop) { parent.style.marginTop = '0'; }\n        if (!parent.style.marginRight) { parent.style.marginRight = '0'; }\n        if (!parent.style.marginBottom) { parent.style.marginBottom = '0'; }\n        if (!parent.style.marginLeft) { parent.style.marginLeft = '0'; }\n      }\n    }\n\n    if (this.loadFonts !== 'false') {\n      const fontDescriptor = {\n        family: 'Open Sans',\n        style: 'normal',\n        weight: '300',\n        unicodeRange: 'U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD',\n      };\n      const fontWeight300 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UN_r8OUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontDescriptor.weight = '600';\n      const fontWeight600 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UNirkOUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontWeight300.load().then((font) => { document.fonts.add(font); });\n      fontWeight600.load().then((font) => { document.fonts.add(font); });\n    }\n\n    if (!this.layout || !'row, column,'.includes(`${this.layout},`)) { this.layout = 'row'; }\n    if (!this.renderStyle || !'read, view, focused,'.includes(`${this.renderStyle},`)) { this.renderStyle = 'focused'; }\n    if (!this.schemaStyle || !'tree, table,'.includes(`${this.schemaStyle},`)) { this.schemaStyle = 'tree'; }\n    if (!this.theme || !'light, dark,'.includes(`${this.theme},`)) {\n      this.theme = (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) ? 'light' : 'dark';\n    }\n    if (!this.defaultSchemaTab || !'example, schema, model,'.includes(`${this.defaultSchemaTab},`)) {\n      this.defaultSchemaTab = 'example';\n    } else if (this.defaultSchemaTab === 'model') {\n      this.defaultSchemaTab = 'schema';\n    }\n    if (!this.schemaExpandLevel || this.schemaExpandLevel < 1) { this.schemaExpandLevel = 99999; }\n    if (!this.schemaDescriptionExpanded || !'true, false,'.includes(`${this.schemaDescriptionExpanded},`)) { this.schemaDescriptionExpanded = 'false'; }\n\n    if (!this.schemaHideReadOnly || !'default, never,'.includes(`${this.schemaHideReadOnly},`)) { this.schemaHideReadOnly = 'default'; }\n    if (!this.schemaHideWriteOnly || !'default, never,'.includes(`${this.schemaHideWriteOnly},`)) { this.schemaHideWriteOnly = 'default'; }\n\n    if (!this.fillRequestFieldsWithExample || !'true, false,'.includes(`${this.fillRequestFieldsWithExample},`)) { this.fillRequestFieldsWithExample = 'true'; }\n    if (!this.persistAuth || !'true, false,'.includes(`${this.persistAuth},`)) { this.persistAuth = 'false'; }\n    if (!this.responseAreaHeight) {\n      this.responseAreaHeight = '400px';\n    }\n\n    if (!this.allowSearch || !'true, false,'.includes(`${this.allowSearch},`)) { this.allowSearch = 'true'; }\n    if (!this.allowAdvancedSearch || !'true, false,'.includes(`${this.allowAdvancedSearch},`)) { this.allowAdvancedSearch = 'true'; }\n\n    if (!this.allowTry || !'true, false,'.includes(`${this.allowTry},`)) { this.allowTry = 'true'; }\n    if (!this.apiKeyValue) { this.apiKeyValue = '-'; }\n    if (!this.apiKeyLocation) { this.apiKeyLocation = 'header'; }\n    if (!this.apiKeyName) { this.apiKeyName = ''; }\n\n    if (!this.oauthReceiver) { this.oauthReceiver = 'oauth-receiver.html'; }\n    if (!this.updateRoute || !'true, false,'.includes(`${this.updateRoute},`)) { this.updateRoute = 'true'; }\n    if (!this.routePrefix) { this.routePrefix = '#'; }\n    if (!this.sortTags || !'true, false,'.includes(`${this.sortTags},`)) { this.sortTags = 'false'; }\n    if (!this.generateMissingTags || !'true, false,'.includes(`${this.generateMissingTags},`)) { this.generateMissingTags = 'false'; }\n    if (!this.sortEndpointsBy || !'method, path, summary, none,'.includes(`${this.sortEndpointsBy},`)) { this.sortEndpointsBy = 'path'; }\n\n    if (!this.onNavTagClick || !'expand-collapse, show-description,'.includes(`${this.onNavTagClick},`)) { this.onNavTagClick = 'expand-collapse'; }\n    if (!this.navItemSpacing || !'compact, relaxed, default,'.includes(`${this.navItemSpacing},`)) { this.navItemSpacing = 'default'; }\n    if (!this.showMethodInNavBar || !'false, as-plain-text, as-colored-text, as-colored-block,'.includes(`${this.showMethodInNavBar},`)) { this.showMethodInNavBar = 'false'; }\n    if (!this.usePathInNavBar || !'true, false,'.includes(`${this.usePathInNavBar},`)) { this.usePathInNavBar = 'false'; }\n    if (!this.navActiveItemMarker || !'left-bar, colored-block'.includes(`${this.navActiveItemMarker},`)) { this.navActiveItemMarker = 'left-bar'; }\n\n    if (!this.fontSize || !'default, large, largest,'.includes(`${this.fontSize},`)) { this.fontSize = 'default'; }\n    if (!this.showInfo || !'true, false,'.includes(`${this.showInfo},`)) { this.showInfo = 'true'; }\n    if (!this.allowServerSelection || !'true, false,'.includes(`${this.allowServerSelection},`)) { this.allowServerSelection = 'true'; }\n    if (!this.allowAuthentication || !'true, false,'.includes(`${this.allowAuthentication},`)) { this.allowAuthentication = 'true'; }\n    if (!this.allowSchemaDescriptionExpandToggle || !'true, false,'.includes(`${this.allowSchemaDescriptionExpandToggle},`)) { this.allowSchemaDescriptionExpandToggle = 'true'; }\n\n    if (!this.showSideNav || !'true false'.includes(this.showSideNav)) { this.showSideNav = 'true'; }\n    if (!this.showComponents || !'true false'.includes(this.showComponents)) { this.showComponents = 'false'; }\n    if (!this.infoDescriptionHeadingsInNavBar || !'true, false,'.includes(`${this.infoDescriptionHeadingsInNavBar},`)) { this.infoDescriptionHeadingsInNavBar = 'false'; }\n    if (!this.fetchCredentials || !'omit, same-origin, include,'.includes(`${this.fetchCredentials},`)) { this.fetchCredentials = ''; }\n    if (!this.matchType || !'includes regex'.includes(this.matchType)) { this.matchType = 'includes'; }\n\n    if (!this.showAdvancedSearchDialog) { this.showAdvancedSearchDialog = false; }\n\n    if (!this.cssFile) { this.cssFile = null; }\n    if (!this.cssClasses) { this.cssClasses = ''; }\n\n    marked.setOptions({\n      highlight: (code, lang) => {\n        if (Prism.languages[lang]) {\n          return Prism.highlight(code, Prism.languages[lang], lang);\n        }\n        return code;\n      },\n    });\n\n    window.addEventListener('hashchange', () => {\n      this.scrollToPath(this.getElementIDFromURL());\n    }, true);\n  }\n\n  // Cleanup\n  disconnectedCallback() {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n    }\n    super.disconnectedCallback();\n  }\n\n  infoDescriptionHeadingRenderer() {\n    const renderer = new marked.Renderer();\n    renderer.heading = ((text, level, raw, slugger) => `<h${level} class=\"observe-me\" id=\"${slugger.slug(raw)}\">${text}</h${level}>`);\n    return renderer;\n  }\n\n  render() {\n    // return render(mainBodyTemplate(this), this.shadowRoot, { eventContext: this });\n    const cssLinkEl = document.querySelector(`link[href*=\"${this.cssFile}\"]`);\n    // adding custom style for RapiDoc\n    if (cssLinkEl) {\n      this.shadowRoot.appendChild(cssLinkEl.cloneNode());\n    }\n    return mainBodyTemplate.call(this);\n  }\n\n  observeExpandedContent() {\n    // Main Container\n    const observeOverviewEls = this.shadowRoot.querySelectorAll('.observe-me');\n    observeOverviewEls.forEach((targetEl) => {\n      this.intersectionObserver.observe(targetEl);\n    });\n  }\n\n  attributeChangedCallback(name, oldVal, newVal) {\n    if (name === 'spec-url') {\n      if (oldVal !== newVal) {\n        // put it at the end of event-loop to load all the attributes\n        window.setTimeout(async () => {\n          await this.loadSpec(newVal);\n          // If goto-path is provided and no location-hash is present then try to scroll there\n          if (this.gotoPath && !window.location.hash) {\n            this.scrollToPath(this.gotoPath);\n          }\n        }, 0);\n      }\n    }\n    if (name === 'render-style') {\n      if (newVal === 'read') {\n        window.setTimeout(() => {\n          this.observeExpandedContent();\n        }, 100);\n      } else {\n        this.intersectionObserver.disconnect();\n      }\n    }\n    if (name === 'api-key-name' || name === 'api-key-location' || name === 'api-key-value') {\n      let updateSelectedApiKey = false;\n      let apiKeyName = '';\n      let apiKeyLocation = '';\n      let apiKeyValue = '';\n\n      if (name === 'api-key-name') {\n        if (this.getAttribute('api-key-location') && this.getAttribute('api-key-value')) {\n          apiKeyName = newVal;\n          apiKeyLocation = this.getAttribute('api-key-location');\n          apiKeyValue = this.getAttribute('api-key-value');\n          updateSelectedApiKey = true;\n        }\n      } else if (name === 'api-key-location') {\n        if (this.getAttribute('api-key-name') && this.getAttribute('api-key-value')) {\n          apiKeyLocation = newVal;\n          apiKeyName = this.getAttribute('api-key-name');\n          apiKeyValue = this.getAttribute('api-key-value');\n          updateSelectedApiKey = true;\n        }\n      } else if (name === 'api-key-value') {\n        if (this.getAttribute('api-key-name') && this.getAttribute('api-key-location')) {\n          apiKeyValue = newVal;\n          apiKeyLocation = this.getAttribute('api-key-location');\n          apiKeyName = this.getAttribute('api-key-name');\n          updateSelectedApiKey = true;\n        }\n      }\n\n      if (updateSelectedApiKey) {\n        if (this.resolvedSpec) {\n          const rapiDocApiKey = this.resolvedSpec.securitySchemes.find((v) => v.securitySchemeId === rapidocApiKey);\n          if (!rapiDocApiKey) {\n            this.resolvedSpec.securitySchemes.push({\n              securitySchemeId: rapidocApiKey,\n              description: 'api-key provided in rapidoc element attributes',\n              type: 'apiKey',\n              name: apiKeyName,\n              in: apiKeyLocation,\n              value: apiKeyValue,\n              finalKeyValue: apiKeyValue,\n            });\n          } else {\n            rapiDocApiKey.name = apiKeyName;\n            rapiDocApiKey.in = apiKeyLocation;\n            rapiDocApiKey.value = apiKeyValue;\n            rapiDocApiKey.finalKeyValue = apiKeyValue;\n          }\n          this.requestUpdate();\n        }\n      }\n    }\n    super.attributeChangedCallback(name, oldVal, newVal);\n  }\n\n  onSpecUrlChange() {\n    this.setAttribute('spec-url', this.shadowRoot.getElementById('spec-url').value);\n  }\n\n  onSpecFileChange(e) {\n    this.setAttribute('spec-file', this.shadowRoot.getElementById('spec-file').value);\n    const specFile = e.target.files[0];\n    const reader = new FileReader();\n    reader.onload = () => {\n      try {\n        const specObj = JSON.parse(reader.result);\n        this.loadSpec(specObj);\n        this.shadowRoot.getElementById('spec-url').value = '';\n      } catch (err) {\n        console.error('RapiDoc: Unable to read or parse json'); // eslint-disable-line no-console\n      }\n    };\n    // Read the Text file\n    reader.readAsText(specFile);\n  }\n\n  onFileLoadClick() {\n    this.shadowRoot.getElementById('spec-file').click();\n  }\n\n  onSearchChange(e) {\n    this.matchPaths = e.target.value;\n    this.resolvedSpec.tags.forEach((tag) => tag.paths.filter((v) => {\n      if (this.matchPaths) {\n        // v.expanded = false;\n        if (pathIsInSearch(this.matchPaths, v, this.matchType)) {\n          tag.expanded = true;\n        }\n      }\n    }));\n    this.resolvedSpec.components.forEach((component) => component.subComponents.filter((v) => {\n      v.expanded = false;\n      if (!this.matchPaths || componentIsInSearch(this.matchPaths, v)) {\n        v.expanded = true;\n      }\n    }));\n    this.requestUpdate();\n  }\n\n  onClearSearch() {\n    const searchEl = this.shadowRoot.getElementById('nav-bar-search');\n    searchEl.value = '';\n    this.matchPaths = '';\n    this.resolvedSpec.components.forEach((component) => component.subComponents.filter((v) => {\n      v.expanded = true;\n    }));\n  }\n\n  onShowSearchModalClicked() {\n    this.showAdvancedSearchDialog = true;\n  }\n\n  // Event Handler on Dialog-Box is opened\n  async onOpenSearchDialog(e) {\n    // Set focus to text input\n    const inputEl = e.detail.querySelector('input');\n    await sleep(0);\n    if (inputEl) {\n      inputEl.focus();\n    }\n  }\n\n  // Public Method\n  async loadSpec(specUrl) {\n    if (!specUrl) {\n      return;\n    }\n    this.matchPaths = '';\n    try {\n      this.resolvedSpec = {\n        specLoadError: false,\n        isSpecLoading: true,\n        tags: [],\n      };\n      this.loading = true;\n      this.loadFailed = false;\n      const spec = await ProcessSpec.call(\n        this,\n        specUrl,\n        this.generateMissingTags === 'true',\n        this.sortTags === 'true',\n        this.getAttribute('sort-endpoints-by'),\n        this.getAttribute('api-key-name'),\n        this.getAttribute('api-key-location'),\n        this.getAttribute('api-key-value'),\n        this.getAttribute('server-url'),\n      );\n      this.loading = false;\n      this.afterSpecParsedAndValidated(spec);\n    } catch (err) {\n      this.loading = false;\n      this.loadFailed = true;\n      this.resolvedSpec = null;\n      console.error(`RapiDoc: Unable to resolve the API spec..  ${err.message}`); // eslint-disable-line no-console\n    }\n  }\n\n  async afterSpecParsedAndValidated(spec) {\n    this.resolvedSpec = spec;\n    this.selectedServer = undefined;\n    if (this.defaultApiServerUrl) {\n      if (this.defaultApiServerUrl === this.serverUrl) {\n        this.selectedServer = {\n          url: this.serverUrl,\n          computedUrl: this.serverUrl,\n        };\n      } else if (this.resolvedSpec.servers) {\n        this.selectedServer = this.resolvedSpec.servers.find((v) => (v.url === this.defaultApiServerUrl));\n      }\n    }\n    if (!this.selectedServer) {\n      if (this.resolvedSpec.servers) {\n        this.selectedServer = this.resolvedSpec.servers[0]; // eslint-disable-line prefer-destructuring\n      }\n    }\n    this.requestUpdate();\n    // eslint-disable-next-line no-await-in-loop\n    while (!await this.updateComplete);\n    const specLoadedEvent = new CustomEvent('spec-loaded', { detail: spec });\n    this.dispatchEvent(specLoadedEvent);\n\n    // Initiate IntersectionObserver and put it at the end of event loop, to allow loading all the child elements (must for larger specs)\n    this.intersectionObserver.disconnect();\n    if (this.renderStyle === 'read') {\n      await sleep(100);\n      this.observeExpandedContent(); // This will auto-highlight the selected nav-item in read-mode\n    }\n\n    this.isIntersectionObserverActive = true;\n\n    // On first time Spec load, try to navigate to location hash if provided\n    const elementId = this.getElementIDFromURL();\n\n    if (elementId) {\n      if (this.renderStyle === 'view') {\n        this.expandAndGotoOperation(elementId, true, true);\n      } else {\n        this.scrollToPath(elementId);\n      }\n    } else if (this.renderStyle === 'focused') {\n      // If goto-path is provided and no location-hash is present then try to scroll to default element\n      if (!this.gotoPath) {\n        const defaultElementId = this.showInfo ? 'overview' : this.resolvedSpec.tags[0]?.paths[0];\n        this.scrollToPath(defaultElementId);\n      }\n    }\n  }\n\n  /**\n   * Return the URL from where is served the RapiDoc component, removing any hash and route prefix\n   */\n  getComponentBaseURL() {\n    const { href } = window.location;\n\n    // Remove end of string # or /\n    const cleanRouterPrefix = this.routePrefix.replace(/(#|\\/)$/, '');\n\n    if (!cleanRouterPrefix) {\n      return href.split('#')[0];\n    }\n\n    const indexOfRoutePrefix = href.lastIndexOf(cleanRouterPrefix);\n\n    if (indexOfRoutePrefix === -1) {\n      return href;\n    }\n\n    return href.slice(0, indexOfRoutePrefix);\n  }\n\n  /**\n   * From the URL return the ID of the element whether it is in the hash or if used a router prefix without a hash\n   */\n  getElementIDFromURL() {\n    const baseURL = this.getComponentBaseURL();\n    const elementId = window.location.href.replace(baseURL + this.routePrefix, '');\n    return elementId;\n  }\n\n  replaceHistoryState(hashId) {\n    const baseURL = this.getComponentBaseURL();\n    window.history.replaceState(null, null, `${baseURL}${this.routePrefix || '#'}${hashId}`);\n  }\n\n  expandAndGotoOperation(elementId, scrollToElement = true) {\n    if (!this.resolvedSpec) {\n      return;\n    }\n    // Expand full operation and tag\n    let isExpandingNeeded = true;\n    const tmpElementId = elementId.indexOf('#') === -1 ? elementId : elementId.substring(1);\n    if (tmpElementId.startsWith('overview') || tmpElementId === 'servers' || tmpElementId === 'auth') {\n      isExpandingNeeded = false;\n    } else {\n      for (let i = 0; i < this.resolvedSpec.tags?.length; i++) {\n        const tag = this.resolvedSpec.tags[i];\n        const path = tag.paths?.find((p) => p.elementId === elementId);\n        if (path) {\n          if (path.expanded && tag.expanded) {\n            isExpandingNeeded = false;\n          } else {\n            path.expanded = true;\n            tag.expanded = true;\n          }\n        }\n      }\n    }\n    if (scrollToElement) {\n      // requestUpdate() and delay required, else we cant find element\n      if (isExpandingNeeded) {\n        this.requestUpdate();\n      }\n      window.setTimeout(() => {\n        const gotoEl = this.shadowRoot.getElementById(tmpElementId);\n        if (gotoEl) {\n          gotoEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n          if (this.updateRoute === 'true') {\n            this.replaceHistoryState(tmpElementId);\n          }\n        }\n      }, isExpandingNeeded ? 150 : 0);\n    }\n  }\n\n  isValidTopId(id) {\n    return (id.startsWith('overview') || id === 'servers' || id === 'auth');\n  }\n\n  isValidPathId(id) {\n    if (id === 'overview' && this.showInfo) {\n      return true;\n    }\n    if (id === 'servers' && this.allowServerSelection) {\n      return true;\n    }\n    if (id === 'auth' && this.allowAuthentication) {\n      return true;\n    }\n    if (id.startsWith('tag--')) {\n      return this.resolvedSpec?.tags?.find((tag) => tag.elementId === id);\n    }\n    return this.resolvedSpec?.tags?.find((tag) => tag.paths.find((path) => path.elementId === id));\n  }\n\n  onIntersect(entries) {\n    if (this.isIntersectionObserverActive === false) {\n      return;\n    }\n\n    entries.forEach((entry) => {\n      if (entry.isIntersecting && entry.intersectionRatio > 0) {\n        const oldNavEl = this.shadowRoot.querySelector('.nav-bar-tag.active, .nav-bar-path.active, .nav-bar-info.active, .nav-bar-h1.active, .nav-bar-h2.active, .operations.active');\n        const newNavEl = this.shadowRoot.getElementById(`link-${entry.target.id}`);\n\n        // Add active class in the new element\n        if (newNavEl) {\n          if (this.updateRoute === 'true') {\n            this.replaceHistoryState(entry.target.id);\n          }\n          newNavEl.scrollIntoView({ behavior: 'auto', block: 'center' });\n          newNavEl.classList.add('active');\n          newNavEl.part.add('section-navbar-active-item');\n        }\n\n        // Remove active class from previous element\n        // if it is different from the new one (edge case on loading in read render style)\n        if (oldNavEl && oldNavEl !== newNavEl) {\n          oldNavEl.classList.remove('active');\n          oldNavEl.part.remove('section-navbar-active-item');\n        }\n      }\n    });\n  }\n\n  // Called by anchor tags created using markdown\n  handleHref(e) {\n    if (e.target.tagName.toLowerCase() === 'a') {\n      if (e.target.getAttribute('href').startsWith('#')) {\n        const gotoEl = this.shadowRoot.getElementById(e.target.getAttribute('href').replace('#', ''));\n        if (gotoEl) {\n          gotoEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n        }\n      }\n    }\n  }\n\n  /**\n   * Called by\n   *  - onClick of Navigation Bar\n   *  - onClick of Advanced Search items\n   *\n   * Functionality:\n   *  1. First deactivate IntersectionObserver\n   *  2. Scroll to the element\n   *  3. Activate IntersectionObserver (after little delay)\n   *\n  */\n  async scrollToEventTarget(event, scrollNavItemToView = true) {\n    if (!(event.type === 'click' || (event.type === 'keyup' && event.keyCode === 13))) {\n      return;\n    }\n    const navEl = event.target;\n    if (!navEl.dataset.contentId) {\n      return;\n    }\n    this.isIntersectionObserverActive = false;\n    if (this.renderStyle === 'focused') {\n      const requestEl = this.shadowRoot.querySelector('api-request');\n      if (requestEl) {\n        requestEl.beforeNavigationFocusedMode();\n      }\n    }\n    this.scrollToPath(navEl.dataset.contentId, true, scrollNavItemToView);\n    setTimeout(() => {\n      this.isIntersectionObserverActive = true;\n    }, 300);\n  }\n\n  // Public Method (scrolls to a given path and highlights the left-nav selection)\n  async scrollToPath(elementId, expandPath = true, scrollNavItemToView = true) {\n    if (this.renderStyle === 'focused') {\n      // for focused mode update this.focusedElementId to update the rendering, else it wont find the needed html elements\n      // focusedElementId will get validated in the template\n      this.focusedElementId = elementId;\n      await sleep(0);\n    }\n    if (this.renderStyle === 'view') {\n      this.expandAndGotoOperation(elementId, expandPath, true);\n    } else {\n      let isValidElementId = false;\n      const contentEl = this.shadowRoot.getElementById(elementId);\n      if (contentEl) {\n        isValidElementId = true;\n        contentEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n      } else {\n        isValidElementId = false;\n      }\n      if (isValidElementId) {\n        // for focused style it is important to reset request-body-selection and response selection which maintains the state for in case of multiple req-body or multiple response mime-type\n        if (this.renderStyle === 'focused') {\n          const requestEl = this.shadowRoot.querySelector('api-request');\n          if (requestEl) {\n            requestEl.afterNavigationFocusedMode();\n          }\n          const responseEl = this.shadowRoot.querySelector('api-response');\n          if (responseEl) {\n            responseEl.resetSelection();\n          }\n        }\n\n        // Update Location Hash\n        if (this.updateRoute === 'true') {\n          this.replaceHistoryState(elementId);\n        }\n\n        // Update NavBar View and Styles\n        const newNavEl = this.shadowRoot.getElementById(`link-${elementId}`);\n\n        if (newNavEl) {\n          if (scrollNavItemToView) {\n            newNavEl.scrollIntoView({ behavior: 'auto', block: 'center' });\n          }\n          await sleep(0);\n          const oldNavEl = this.shadowRoot.querySelector('.nav-bar-tag.active, .nav-bar-path.active, .nav-bar-info.active, .nav-bar-h1.active, .nav-bar-h2.active, .operations.active');\n          if (oldNavEl) {\n            oldNavEl.classList.remove('active');\n            oldNavEl.part.remove('active');\n            oldNavEl.part.remove('section-navbar-active-item');\n          }\n          newNavEl.classList.add('active'); // must add the class after scrolling\n          newNavEl.part.add('section-navbar-active-item');\n          // this.requestUpdate();\n        }\n      }\n    }\n  }\n\n  // Public Method - to update security-scheme of type http\n  setHttpUserNameAndPassword(securitySchemeId, username, password) {\n    return applyApiKey.call(this, securitySchemeId, username, password);\n  }\n\n  // Public Method - to update security-scheme of type apiKey or OAuth\n  setApiKey(securitySchemeId, apiKeyValue) {\n    return applyApiKey.call(this, securitySchemeId, '', '', apiKeyValue);\n  }\n\n  // Public Method\n  removeAllSecurityKeys() {\n    return onClearAllApiKeys.call(this);\n  }\n\n  // Public Method\n  setApiServer(apiServerUrl) {\n    // return apiServerUrl;\n    return setApiServer.call(this, apiServerUrl);\n  }\n\n  // Event handler for Advanced Search text-inputs and checkboxes\n  onAdvancedSearch(ev, delay) {\n    const eventTargetEl = ev.target;\n    clearTimeout(this.timeoutId);\n    this.timeoutId = setTimeout(() => {\n      let searchInputEl;\n      if (eventTargetEl.type === 'text') {\n        searchInputEl = eventTargetEl;\n      } else {\n        searchInputEl = eventTargetEl.closest('.advanced-search-options').querySelector('input[type=text]');\n      }\n      const searchOptions = [...eventTargetEl.closest('.advanced-search-options').querySelectorAll('input:checked')].map((v) => v.id);\n      this.advancedSearchMatches = advancedSearch(searchInputEl.value, this.resolvedSpec.tags, searchOptions);\n    }, delay);\n  }\n}\ncustomElements.define('rapi-doc', RapiDoc);\n", "import { css, LitElement } from 'lit';\nimport { marked } from 'marked';\nimport Prism from 'prismjs';\nimport 'prismjs/components/prism-css';\nimport 'prismjs/components/prism-yaml';\nimport 'prismjs/components/prism-go';\nimport 'prismjs/components/prism-java';\nimport 'prismjs/components/prism-json';\nimport 'prismjs/components/prism-bash';\nimport 'prismjs/components/prism-python';\nimport 'prismjs/components/prism-http';\nimport 'prismjs/components/prism-csharp';\n\n// Styles\nimport FontStyles from '~/styles/font-styles';\nimport InputStyles from '~/styles/input-styles';\nimport FlexStyles from '~/styles/flex-styles';\nimport TableStyles from '~/styles/table-styles';\nimport PrismStyles from '~/styles/prism-styles';\nimport TabStyles from '~/styles/tab-styles';\nimport NavStyles from '~/styles/nav-styles';\nimport InfoStyles from '~/styles/info-styles';\n\nimport EndpointStyles from '~/styles/endpoint-styles';\nimport { rapidocApiKey } from '~/utils/common-utils';\nimport ProcessSpec from '~/utils/spec-parser';\nimport mainBodyTemplate from '~/templates/main-body-template';\nimport { applyApiKey, onClearAllApiKeys } from '~/templates/security-scheme-template';\nimport { setApiServer } from '~/templates/server-template';\n\nexport default class RapiDocMini extends LitElement {\n  constructor() {\n    super();\n    this.isMini = true;\n    this.updateRoute = 'false';\n    this.renderStyle = 'view';\n    this.showHeader = 'false';\n    this.allowAdvancedSearch = 'false';\n  }\n\n  static get properties() {\n    return {\n      // Spec\n      specUrl: { type: String, attribute: 'spec-url' },\n      sortEndpointsBy: { type: String, attribute: 'sort-endpoints-by' },\n\n      // UI Layouts\n      layout: { type: String },\n      pathsExpanded: { type: String, attribute: 'paths-expanded' },\n      defaultSchemaTab: { type: String, attribute: 'default-schema-tab' },\n      responseAreaHeight: { type: String, attribute: 'response-area-height' },\n      showSummaryWhenCollapsed: { type: String, attribute: 'show-summary-when-collapsed' },\n      fillRequestFieldsWithExample: { type: String, attribute: 'fill-request-fields-with-example' },\n      persistAuth: { type: String, attribute: 'persist-auth' },\n\n      // Schema Styles\n      schemaStyle: { type: String, attribute: 'schema-style' },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n\n      // API Server\n      apiKeyName: { type: String, attribute: 'api-key-name' },\n      apiKeyLocation: { type: String, attribute: 'api-key-location' },\n      apiKeyValue: { type: String, attribute: 'api-key-value' },\n      defaultApiServerUrl: { type: String, attribute: 'default-api-server' },\n      serverUrl: { type: String, attribute: 'server-url' },\n      oauthReceiver: { type: String, attribute: 'oauth-receiver' },\n\n      allowTry: { type: String, attribute: 'allow-try' },\n\n      // Main Colors and Font\n      theme: { type: String },\n      bgColor: { type: String, attribute: 'bg-color' },\n      textColor: { type: String, attribute: 'text-color' },\n      primaryColor: { type: String, attribute: 'primary-color' },\n      fontSize: { type: String, attribute: 'font-size' },\n      regularFont: { type: String, attribute: 'regular-font' },\n      monoFont: { type: String, attribute: 'mono-font' },\n      loadFonts: { type: String, attribute: 'load-fonts' },\n\n      // Fetch Options\n      fetchCredentials: { type: String, attribute: 'fetch-credentials' },\n\n      // Filters\n      matchPaths: { type: String, attribute: 'match-paths' },\n      matchType: { type: String, attribute: 'match-type' },\n\n      // Internal Properties\n      loading: { type: Boolean }, // indicates spec is being loaded\n    };\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      InputStyles,\n      FlexStyles,\n      TableStyles,\n      EndpointStyles,\n      PrismStyles,\n      TabStyles,\n      NavStyles,\n      InfoStyles,\n      css`\n      :host {\n        display:flex;\n        flex-direction: column;\n        min-width:360px;\n        width:100%;\n        height:100%;\n        margin:0;\n        padding:0;\n        overflow: hidden;\n        letter-spacing:normal;\n        color:var(--fg);\n        background-color:var(--bg);\n        font-family:var(--font-regular);\n      }\n\n      @media only screen and (min-width: 768px) {\n        .only-large-screen{\n          display:block;\n        }\n        .only-large-screen-flex{\n          display:flex;\n        }\n      }`,\n    ];\n  }\n\n  // Startup\n  connectedCallback() {\n    super.connectedCallback();\n\n    if (this.loadFonts !== 'false') {\n      const fontDescriptor = {\n        family: 'Open Sans',\n        style: 'normal',\n        weight: '300',\n        unicodeRange: 'U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD',\n      };\n      const fontWeight300 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UN_r8OUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontDescriptor.weight = '600';\n      const fontWeight600 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UNirkOUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontWeight300.load().then((font) => { document.fonts.add(font); });\n      fontWeight600.load().then((font) => { document.fonts.add(font); });\n    }\n\n    if (!this.showSummaryWhenCollapsed || !'true, false,'.includes(`${this.showSummaryWhenCollapsed},`)) { this.showSummaryWhenCollapsed = 'true'; }\n    if (!this.layout || !'row, column,'.includes(`${this.layout},`)) { this.layout = 'row'; }\n    if (!this.schemaStyle || !'tree, table,'.includes(`${this.schemaStyle},`)) { this.schemaStyle = 'tree'; }\n    if (!this.theme || !'light, dark,'.includes(`${this.theme},`)) {\n      this.theme = (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) ? 'light' : 'dark';\n    }\n    if (!this.defaultSchemaTab || !'example, schema, model,'.includes(`${this.defaultSchemaTab},`)) {\n      this.defaultSchemaTab = 'example';\n    } else if (this.defaultSchemaTab === 'model') {\n      this.defaultSchemaTab = 'schema';\n    }\n    this.pathsExpanded = this.pathsExpanded === 'true';\n    if (!this.schemaExpandLevel || this.schemaExpandLevel < 1) { this.schemaExpandLevel = 99999; }\n    if (!this.schemaDescriptionExpanded || !'true, false,'.includes(`${this.schemaDescriptionExpanded},`)) { this.schemaDescriptionExpanded = 'false'; }\n    if (!this.fillRequestFieldsWithExample || !'true, false,'.includes(`${this.fillRequestFieldsWithExample},`)) { this.fillRequestFieldsWithExample = 'true'; }\n    if (!this.persistAuth || !'true, false,'.includes(`${this.persistAuth},`)) { this.persistAuth = 'false'; }\n    if (!this.responseAreaHeight) { this.responseAreaHeight = '300px'; }\n\n    if (!this.allowTry || !'true, false,'.includes(`${this.allowTry},`)) { this.allowTry = 'true'; }\n    if (!this.apiKeyValue) { this.apiKeyValue = '-'; }\n    if (!this.apiKeyLocation) { this.apiKeyLocation = 'header'; }\n    if (!this.apiKeyName) { this.apiKeyName = ''; }\n\n    if (!this.oauthReceiver) { this.oauthReceiver = 'oauth-receiver.html'; }\n    if (!this.sortTags || !'true, false,'.includes(`${this.sortTags},`)) { this.sortTags = 'false'; }\n    if (!this.sortEndpointsBy || !'method, path, summary,'.includes(`${this.sortEndpointsBy},`)) { this.sortEndpointsBy = 'path'; }\n    if (!this.fontSize || !'default, large, largest,'.includes(`${this.fontSize},`)) { this.fontSize = 'default'; }\n    if (!this.matchType || !'includes regex'.includes(this.matchType)) { this.matchType = 'includes'; }\n\n    if (!this.allowSchemaDescriptionExpandToggle || !'true, false,'.includes(`${this.allowSchemaDescriptionExpandToggle},`)) { this.allowSchemaDescriptionExpandToggle = 'true'; }\n    if (!this.fetchCredentials || !'omit, same-origin, include,'.includes(`${this.fetchCredentials},`)) { this.fetchCredentials = ''; }\n\n    marked.setOptions({\n      highlight: (code, lang) => {\n        if (Prism.languages[lang]) {\n          return Prism.highlight(code, Prism.languages[lang], lang);\n        }\n        return code;\n      },\n    });\n  }\n\n  render() {\n    return mainBodyTemplate.call(this, true, false, false, this.pathsExpanded);\n  }\n\n  attributeChangedCallback(name, oldVal, newVal) {\n    if (name === 'spec-url') {\n      if (oldVal !== newVal) {\n        // put it at the end of event-loop to load all the attributes\n        window.setTimeout(async () => {\n          await this.loadSpec(newVal);\n        }, 0);\n      }\n    }\n    if (name === 'api-key-name' || name === 'api-key-location' || name === 'api-key-value') {\n      let updateSelectedApiKey = false;\n      let apiKeyName = '';\n      let apiKeyLocation = '';\n      let apiKeyValue = '';\n\n      if (name === 'api-key-name') {\n        if (this.getAttribute('api-key-location') && this.getAttribute('api-key-value')) {\n          apiKeyName = newVal;\n          apiKeyLocation = this.getAttribute('api-key-location');\n          apiKeyValue = this.getAttribute('api-key-value');\n          updateSelectedApiKey = true;\n        }\n      } else if (name === 'api-key-location') {\n        if (this.getAttribute('api-key-name') && this.getAttribute('api-key-value')) {\n          apiKeyLocation = newVal;\n          apiKeyName = this.getAttribute('api-key-name');\n          apiKeyValue = this.getAttribute('api-key-value');\n          updateSelectedApiKey = true;\n        }\n      } else if (name === 'api-key-value') {\n        if (this.getAttribute('api-key-name') && this.getAttribute('api-key-location')) {\n          apiKeyValue = newVal;\n          apiKeyLocation = this.getAttribute('api-key-location');\n          apiKeyName = this.getAttribute('api-key-name');\n          updateSelectedApiKey = true;\n        }\n      }\n\n      if (updateSelectedApiKey) {\n        if (this.resolvedSpec) {\n          const rapiDocApiKey = this.resolvedSpec.securitySchemes.find((v) => v.securitySchemeId === rapidocApiKey);\n          if (!rapiDocApiKey) {\n            this.resolvedSpec.securitySchemes.push({\n              apiKeyId: rapidocApiKey,\n              description: 'api-key provided in rapidoc element attributes',\n              type: 'apiKey',\n              name: apiKeyName,\n              in: apiKeyLocation,\n              value: apiKeyValue,\n              finalKeyValue: apiKeyValue,\n            });\n          } else {\n            rapiDocApiKey.name = apiKeyName;\n            rapiDocApiKey.in = apiKeyLocation;\n            rapiDocApiKey.value = apiKeyValue;\n            rapiDocApiKey.finalKeyValue = apiKeyValue;\n          }\n          this.requestUpdate();\n        }\n      }\n    }\n    super.attributeChangedCallback(name, oldVal, newVal);\n  }\n\n  onSpecUrlChange() {\n    this.setAttribute('spec-url', this.shadowRoot.getElementById('spec-url').value);\n  }\n\n  // Public Method\n  async loadSpec(specUrl) {\n    if (!specUrl) {\n      return;\n    }\n    try {\n      this.resolvedSpec = {\n        specLoadError: false,\n        isSpecLoading: true,\n        tags: [],\n      };\n      this.loading = true;\n      this.loadFailed = false;\n      this.requestUpdate();\n      const spec = await ProcessSpec.call(\n        this,\n        specUrl,\n        this.generateMissingTags === 'true',\n        this.sortTags === 'true',\n        this.getAttribute('sort-endpoints-by'),\n        this.getAttribute('api-key-name'),\n        this.getAttribute('api-key-location'),\n        this.getAttribute('api-key-value'),\n        this.getAttribute('server-url'),\n      );\n      this.loading = false;\n      this.afterSpecParsedAndValidated(spec);\n    } catch (err) {\n      this.loading = false;\n      this.loadFailed = true;\n      this.resolvedSpec = null;\n      console.error(`RapiDoc: Unable to resolve the API spec..  ${err.message}`); // eslint-disable-line no-console\n    }\n  }\n\n  // Public Method - to update security-scheme of type http\n  setHttpUserNameAndPassword(securitySchemeId, username, password) {\n    return applyApiKey.call(this, securitySchemeId, username, password);\n  }\n\n  // Public Method - to update security-scheme of type apiKey or OAuth\n  setApiKey(securitySchemeId, apiKeyValue) {\n    return applyApiKey.call(this, securitySchemeId, '', '', apiKeyValue);\n  }\n\n  // Public Method\n  removeAllSecurityKeys() {\n    return onClearAllApiKeys.call(this);\n  }\n\n  // Public Method\n  setApiServer(apiServerUrl) {\n    // return apiServerUrl;\n    return setApiServer.call(this, apiServerUrl);\n  }\n\n  async afterSpecParsedAndValidated(spec) {\n    this.resolvedSpec = spec;\n    this.selectedServer = undefined;\n    if (this.defaultApiServerUrl) {\n      if (this.defaultApiServerUrl === this.serverUrl) {\n        this.selectedServer = {\n          url: this.serverUrl,\n          computedUrl: this.serverUrl,\n        };\n      } else if (this.resolvedSpec.servers) {\n        this.selectedServer = this.resolvedSpec.servers.find((v) => (v.url === this.defaultApiServerUrl));\n      }\n    }\n    if (!this.selectedServer) {\n      if (this.resolvedSpec.servers) {\n        this.selectedServer = this.resolvedSpec.servers[0]; // eslint-disable-line prefer-destructuring\n      }\n    }\n    this.requestUpdate();\n    // eslint-disable-next-line no-await-in-loop\n    while (!await this.updateComplete);\n    const specLoadedEvent = new CustomEvent('spec-loaded', { detail: spec });\n    this.dispatchEvent(specLoadedEvent);\n  }\n\n  // Called by anchor tags created using markdown\n  handleHref(e) {\n    if (e.target.tagName.toLowerCase() === 'a') {\n      if (e.target.getAttribute('href').startsWith('#')) {\n        const gotoEl = this.shadowRoot.getElementById(e.target.getAttribute('href').replace('#', ''));\n        if (gotoEl) {\n          gotoEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n        }\n      }\n    }\n  }\n}\ncustomElements.define('rapi-doc-mini', RapiDocMini);\n", "import { html } from 'lit';\nimport { unsafeHTML } from 'lit/directives/unsafe-html.js'; // eslint-disable-line import/extensions\nimport { marked } from 'marked';\n\n// Templates\nimport overviewTemplate from '~/templates/overview-template';\nimport headerTemplate from '~/templates/header-template';\nimport { schemaInObjectNotation, generateExample } from '~/utils/schema-utils';\nimport '~/components/json-tree';\nimport '~/components/schema-tree';\nimport SetTheme from '~/utils/theme';\nimport { isValidHexColor } from '~/utils/color-utils';\n\n/* eslint-disable indent */\n// Json Schema Nav Template\nfunction jsonSchemaNavTemplate() {\n  return html`\n  <nav class='nav-bar' part=\"section-navbar\">\n    <slot name=\"nav-logo\" class=\"logo\"></slot>\n    <div style=\"display:flex;line-height:22px; padding:8px\">\n      <input id=\"nav-bar-search\" \n        part = \"textbox textbox-nav-filter\"\n        style = \"width:100%; height: 26px; padding-right:20px; color:var(--nav-hover-text-color); border-color:var(--nav-accent-color); background-color:var(--nav-hover-bg-color)\" \n        type = \"text\"\n        placeholder = \"Filter\" \n        @change = \"${this.onSearchChange}\"  \n        spellcheck = \"false\" \n      >\n      <div style=\"margin: 6px 5px 0 -24px; font-size:var(--font-size-regular); cursor:pointer;\">&#x21a9;</div>\n    </div>\n    <nav style=\"flex:1\" class='nav-scroll' part=\"section-navbar-scroll\">\n      ${this.resolvedSpec.schemaAndExamples.map((v) => html`\n        <div class='nav-bar-path' data-content-id='${v.elementId}' id='link-${v.elementId}'\n          @click = '${(e) => {\n            this.scrollToEventTarget(e, false);\n          }}'\n        > \n          ${v.name}\n        </div>`)\n      }\n    </nav>  \n  </nav>\n  `;\n}\n\n// Json Schema Body Template\nfunction jsonSchemaBodyTemplate() {\n  return html`\n    ${this.showInfo === 'true' ? overviewTemplate.call(this) : ''}\n    <div style=\"font-size:var(--font-size-regular);\">\n    ${this.resolvedSpec.schemaAndExamples.map((jSchemaBody) => {\n      const examplesObj = generateExample(jSchemaBody.schema, 'json', jSchemaBody.examples, jSchemaBody.example, true, false, 'json', true);\n      jSchemaBody.selectedExample = examplesObj[0]?.exampleId;\n      return html`\n        <section id='${jSchemaBody.elementId}' class='json-schema-and-example regular-font' style=\"display:flex; flex-direction: column; border:1px solid var(--border-color); margin-bottom:32px; border-top: 5px solid var(--border-color)\">\n          <div style=\"padding:16px; border-bottom: 1px solid var(--border-color)\">\n            <div style=\"font-size:var(--font-size-small); font-weight:bold\">${jSchemaBody.name}</div>\n            <span class=\"json-schema-description m-markdown \">${unsafeHTML(marked(jSchemaBody.description || ''))}</span>\n          </div>  \n          <div style=\"display:flex; flex-direction: row; gap:16px;\">\n            <div class=\"json-schema-def\" style=\"flex:1; padding:16px 0 16px 16px; \">\n              <schema-tree\n                .data = \"${schemaInObjectNotation(jSchemaBody.schema, {})}\"\n                schema-expand-level = \"${this.schemaExpandLevel}\"\n                schema-description-expanded = \"${this.schemaDescriptionExpanded}\"\n                allow-schema-description-expand-toggle = \"${this.allowSchemaDescriptionExpandToggle}\"\n                schema-hide-read-only = \"false\"\n                schema-hide-write-only = \"false\"\n              > </schema-tree>\n            </div>\n            <div class=\"json-schema-example-panel\" style=\"width:400px; background-color: var(--input-bg); padding:16px 0 16px 16px; border-left: 1px dashed var(--border-color);\">\n              ${examplesObj.length > 1\n                ? html`<select style=\"min-width:100px; max-width:100%\" @change='${(e) => this.onSelectExample(e, jSchemaBody)}'>\n                    ${examplesObj.map((v) => html`\n                      <option value=\"${v.exampleId}\" ?selected=${(v.exampleId === jSchemaBody.selectedExample)}> \n                        ${v.exampleSummary.length > 80 ? v.exampleId : v.exampleSummary}\n                      </option>`)\n                    }\n                  </select>`\n                : html`<div style=\"font-size: var(--font-size-small);font-weight:700; margin:5px 0\"> ${examplesObj[0].exampleSummary}</div>`\n              }\n              ${examplesObj.map((v) => html`\n                <json-tree \n                  .data = \"${v.exampleValue}\"\n                  data-example = \"${v.exampleId}\"\n                  class = \"example\"\n                  style = \"margin-top:16px; display: ${v.exampleId === jSchemaBody.selectedExample ? 'flex' : 'none'}\"\n                ></json-tree>`)\n              }\n            </div>\n          </div>\n        </section>`;\n      })\n    }\n    </div>\n  `;\n}\n/* eslint-enable indent */\n\n// Json Schema Root Template\nexport default function jsonSchemaViewerTemplate(isMini = false) {\n// export default function jsonSchemaViewerTemplate(isMini = false, showExpandCollapse = true, showTags = true, pathsExpanded = false) {\n  if (!this.resolvedSpec) {\n    return '';\n  }\n  const newTheme = {\n    bg1: isValidHexColor(this.bgColor) ? this.bgColor : '',\n    fg1: isValidHexColor(this.textColor) ? this.textColor : '',\n    headerColor: isValidHexColor(this.headerColor) ? this.headerColor : '',\n    primaryColor: isValidHexColor(this.primaryColor) ? this.primaryColor : '',\n    navBgColor: isValidHexColor(this.navBgColor) ? this.navBgColor : '',\n    navTextColor: isValidHexColor(this.navTextColor) ? this.navTextColor : '',\n    navHoverBgColor: isValidHexColor(this.navHoverBgColor) ? this.navHoverBgColor : '',\n    navHoverTextColor: isValidHexColor(this.navHoverTextColor) ? this.navHoverTextColor : '',\n    navAccentColor: isValidHexColor(this.navAccentColor) ? this.navAccentColor : '',\n    navAccenttextColor: isValidHexColor(this.navAccentTextColor) ? this.navAccentTextColor : '',\n  };\n  /* eslint-disable indent */\n  if (this.resolvedSpec.specLoadError) {\n    if (isMini) {\n      return html`\n        ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n        <div style=\"display:flex; align-items:center; border:1px dashed var(--border-color); height:42px; padding:5px; font-size:var(--font-size-small); color:var(--red); font-family:var(--font-mono)\"> ${this.resolvedSpec.info.description} </div>\n      `;\n    }\n    return html`\n      ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n      <!-- Header -->\n      ${headerTemplate.call(this)}\n      <h1> Header </h1>\n      <main class=\"main-content regular-font\" part=\"section-main-content\">\n        <slot></slot>\n        <div style=\"margin:24px; text-align: center;\">\n          <h1 style=\"color: var(--red)\"> ${this.resolvedSpec.info.title} </h1>\n          <div style=\"font-family:var(--font-mono)\"> ${this.resolvedSpec.info.description} </div>\n        </div>\n      </main>  \n    `;\n  }\n  if (this.resolvedSpec.isSpecLoading) {\n    return html`\n      ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n      <main class=\"main-content regular-font\" part=\"section-main-content\">\n        <slot></slot>\n        <div class=\"main-content-inner--${this.renderStyle}-mode\">\n          <div class=\"loader\"></div>\n        </div>\n      </main>  \n    `;\n  }\n\n  return html`\n    ${this.theme === 'dark' ? SetTheme.call(this, 'dark', newTheme) : SetTheme.call(this, 'light', newTheme)}\n\n    <!-- Header -->\n    ${this.showHeader === 'false' ? '' : headerTemplate.call(this)}\n    \n    <div id='the-main-body' class=\"body ${this.cssClasses}\" dir= ${this.pageDirection}>\n\n      <!-- Side Nav -->\n      ${jsonSchemaNavTemplate.call(this)}\n\n      <!-- Main Content -->\n      <main class=\"main-content regular-font\" part=\"section-main-content\">\n        <slot></slot>\n        <div class=\"main-content-inner--${this.renderStyle}-mode\">\n          ${this.loading === true\n            ? html`<div class=\"loader\"></div>`\n            : html`\n              ${this.loadFailed === true\n                ? html`<div style=\"text-align: center;margin: 16px;\"> Unable to load the Spec</div>`\n                : html`\n                  <div class=\"operations-root\" @click=\"${(e) => { this.handleHref(e); }}\">\n                    ${jsonSchemaBodyTemplate.call(this)}\n                  </div>\n                `\n              }`\n          }\n        </div>\n        <slot name=\"footer\"></slot>\n      </main>\n    </div>  \n  `;\n}\n/* eslint-enable indent */\n", "import { css, LitElement } from 'lit';\nimport { marked } from 'marked';\nimport Prism from 'prismjs';\nimport 'prismjs/components/prism-css';\nimport 'prismjs/components/prism-yaml';\nimport 'prismjs/components/prism-go';\nimport 'prismjs/components/prism-java';\nimport 'prismjs/components/prism-json';\nimport 'prismjs/components/prism-bash';\nimport 'prismjs/components/prism-python';\nimport 'prismjs/components/prism-http';\nimport 'prismjs/components/prism-csharp';\n\n// Styles\nimport FontStyles from '~/styles/font-styles';\nimport InputStyles from '~/styles/input-styles';\nimport FlexStyles from '~/styles/flex-styles';\nimport TableStyles from '~/styles/table-styles';\nimport PrismStyles from '~/styles/prism-styles';\nimport TabStyles from '~/styles/tab-styles';\nimport NavStyles from '~/styles/nav-styles';\nimport InfoStyles from '~/styles/info-styles';\n\nimport EndpointStyles from '~/styles/endpoint-styles';\nimport ProcessSpec from '~/utils/spec-parser';\nimport jsonSchemaViewerTemplate from '~/templates/json-schema-viewer-template';\n\nexport default class JsonSchemaViewer extends LitElement {\n  constructor() {\n    super();\n    this.isMini = false;\n    this.updateRoute = 'false';\n    this.renderStyle = 'focused';\n    this.showHeader = 'true';\n    this.allowAdvancedSearch = 'false';\n    this.selectedExampleForEachSchema = {};\n  }\n\n  static get properties() {\n    return {\n      // Spec\n      specUrl: { type: String, attribute: 'spec-url' },\n\n      // Schema Styles\n      schemaStyle: { type: String, attribute: 'schema-style' },\n      schemaExpandLevel: { type: Number, attribute: 'schema-expand-level' },\n      schemaDescriptionExpanded: { type: String, attribute: 'schema-description-expanded' },\n      allowSchemaDescriptionExpandToggle: { type: String, attribute: 'allow-schema-description-expand-toggle' },\n\n      // Hide/show Sections\n      showHeader: { type: String, attribute: 'show-header' },\n      showSideNav: { type: String, attribute: 'show-side-nav' },\n      showInfo: { type: String, attribute: 'show-info' },\n\n      // Allow or restrict features\n      allowSpecUrlLoad: { type: String, attribute: 'allow-spec-url-load' },\n      allowSpecFileLoad: { type: String, attribute: 'allow-spec-file-load' },\n      allowSpecFileDownload: { type: String, attribute: 'allow-spec-file-download' },\n      allowSearch: { type: String, attribute: 'allow-search' },\n\n      // Main Colors and Font\n      theme: { type: String },\n      bgColor: { type: String, attribute: 'bg-color' },\n      textColor: { type: String, attribute: 'text-color' },\n      primaryColor: { type: String, attribute: 'primary-color' },\n      fontSize: { type: String, attribute: 'font-size' },\n      regularFont: { type: String, attribute: 'regular-font' },\n      monoFont: { type: String, attribute: 'mono-font' },\n      loadFonts: { type: String, attribute: 'load-fonts' },\n\n      // Internal Properties\n      loading: { type: Boolean }, // indicates spec is being loaded\n    };\n  }\n\n  static get styles() {\n    return [\n      FontStyles,\n      InputStyles,\n      FlexStyles,\n      TableStyles,\n      EndpointStyles,\n      PrismStyles,\n      TabStyles,\n      NavStyles,\n      InfoStyles,\n      css`\n      :host {\n        display:flex;\n        flex-direction: column;\n        min-width:360px;\n        width:100%;\n        height:100%;\n        margin:0;\n        padding:0;\n        overflow: hidden;\n        letter-spacing:normal;\n        color:var(--fg);\n        background-color:var(--bg);\n        font-family:var(--font-regular);\n      }\n      .body {\n        display:flex;\n        height:100%;\n        width:100%;\n        overflow:hidden;\n      }\n      .nav-bar {\n        width: 230px;\n        display:flex;\n      }\n\n      .main-content { \n        margin:0;\n        padding: 16px; \n        display:block;\n        flex:1;\n        height:100%;\n        overflow-y: auto;\n        overflow-x: hidden;\n        scrollbar-width: thin;\n        scrollbar-color: var(--border-color) transparent;\n      }\n      .main-content-inner--view-mode {\n        padding: 0 8px;\n      }\n      .main-content::-webkit-scrollbar {\n        width: 8px;\n        height: 8px;\n      }\n      .main-content::-webkit-scrollbar-track {\n        background:transparent;\n      }\n      .main-content::-webkit-scrollbar-thumb {\n        background-color: var(--border-color);\n      }\n      .main-header {\n        background-color:var(--header-bg);\n        color:var(--header-fg);\n        width:100%;\n      }\n      .header-title {\n        font-size:calc(var(--font-size-regular) + 8px); \n        padding:0 8px;\n      }\n      input.header-input{\n        background:var(--header-color-darker);\n        color:var(--header-fg);\n        border:1px solid var(--header-color-border);\n        flex:1; \n        padding-right:24px;\n        border-radius:3px;\n      }\n      input.header-input::placeholder {\n        opacity:0.4;\n      }\n      .loader {\n        margin: 16px auto 16px auto; \n        border: 4px solid var(--bg3);\n        border-radius: 50%;\n        border-top: 4px solid var(--primary-color);\n        width: 36px;\n        height: 36px;\n        animation: spin 2s linear infinite;\n      }\n      @media only screen and (min-width: 768px) {\n        .only-large-screen{\n          display:block;\n        }\n        .only-large-screen-flex{\n          display:flex;\n        }\n      }`,\n    ];\n  }\n\n  // Startup\n  connectedCallback() {\n    super.connectedCallback();\n    const parent = this.parentElement;\n    if (parent) {\n      if (parent.offsetWidth === 0 && parent.style.width === '') {\n        parent.style.width = '100vw';\n      }\n      if (parent.offsetHeight === 0 && parent.style.height === '') {\n        parent.style.height = '100vh';\n      }\n      if (parent.tagName === 'BODY') {\n        if (!parent.style.marginTop) { parent.style.marginTop = '0'; }\n        if (!parent.style.marginRight) { parent.style.marginRight = '0'; }\n        if (!parent.style.marginBottom) { parent.style.marginBottom = '0'; }\n        if (!parent.style.marginLeft) { parent.style.marginLeft = '0'; }\n      }\n    }\n\n    if (this.loadFonts !== 'false') {\n      const fontDescriptor = {\n        family: 'Open Sans',\n        style: 'normal',\n        weight: '300',\n        unicodeRange: 'U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD',\n      };\n      const fontWeight300 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UN_r8OUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontDescriptor.weight = '600';\n      const fontWeight600 = new FontFace(\n        'Open Sans',\n        \"url(https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UNirkOUuhpKKSTjw.woff2) format('woff2')\",\n        fontDescriptor,\n      );\n      fontWeight300.load().then((font) => { document.fonts.add(font); });\n      fontWeight600.load().then((font) => { document.fonts.add(font); });\n    }\n\n    this.renderStyle = 'focused';\n    this.pathsExpanded = this.pathsExpanded === 'true';\n\n    if (!this.showInfo || !'true, false,'.includes(`${this.showInfo},`)) { this.showInfo = 'true'; }\n    if (!this.showSideNav || !'true false'.includes(this.showSideNav)) { this.showSideNav = 'true'; }\n    if (!this.showHeader || !'true, false,'.includes(`${this.showHeader},`)) { this.showHeader = 'true'; }\n\n    if (!this.schemaStyle || !'tree, table,'.includes(`${this.schemaStyle},`)) { this.schemaStyle = 'tree'; }\n    if (!this.theme || !'light, dark,'.includes(`${this.theme},`)) {\n      this.theme = (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) ? 'light' : 'dark';\n    }\n    if (!this.allowSearch || !'true, false,'.includes(`${this.allowSearch},`)) { this.allowSearch = 'true'; }\n    if (!this.schemaExpandLevel || this.schemaExpandLevel < 1) { this.schemaExpandLevel = 99999; }\n    if (!this.schemaDescriptionExpanded || !'true, false,'.includes(`${this.schemaDescriptionExpanded},`)) { this.schemaDescriptionExpanded = 'false'; }\n    if (!this.fontSize || !'default, large, largest,'.includes(`${this.fontSize},`)) { this.fontSize = 'default'; }\n    if (!this.matchType || !'includes regex'.includes(this.matchType)) { this.matchType = 'includes'; }\n    if (!this.allowSchemaDescriptionExpandToggle || !'true, false,'.includes(`${this.allowSchemaDescriptionExpandToggle},`)) { this.allowSchemaDescriptionExpandToggle = 'true'; }\n\n    marked.setOptions({\n      highlight: (code, lang) => {\n        if (Prism.languages[lang]) {\n          return Prism.highlight(code, Prism.languages[lang], lang);\n        }\n        return code;\n      },\n    });\n  }\n\n  render() {\n    return jsonSchemaViewerTemplate.call(this, true, false, false, this.pathsExpanded);\n  }\n\n  attributeChangedCallback(name, oldVal, newVal) {\n    if (name === 'spec-url') {\n      if (oldVal !== newVal) {\n        // put it at the end of event-loop to load all the attributes\n        window.setTimeout(async () => {\n          await this.loadSpec(newVal);\n        }, 0);\n      }\n    }\n    super.attributeChangedCallback(name, oldVal, newVal);\n  }\n\n  onSpecUrlChange() {\n    this.setAttribute('spec-url', this.shadowRoot.getElementById('spec-url').value);\n  }\n\n  onSearchChange(e) {\n    // Todo: Filter Search\n    this.matchPaths = e.target.value;\n  }\n\n  // Public Method\n  async loadSpec(specUrl) {\n    if (!specUrl) {\n      return;\n    }\n    try {\n      this.resolvedSpec = {\n        specLoadError: false,\n        isSpecLoading: true,\n        tags: [],\n      };\n      this.loading = true;\n      this.loadFailed = false;\n      this.requestUpdate();\n      const spec = await ProcessSpec.call(\n        this,\n        specUrl,\n        this.generateMissingTags === 'true',\n        this.sortTags === 'true',\n        this.getAttribute('sort-endpoints-by'),\n      );\n      this.loading = false;\n      this.afterSpecParsedAndValidated(spec);\n    } catch (err) {\n      this.loading = false;\n      this.loadFailed = true;\n      this.resolvedSpec = null;\n      console.error(`RapiDoc: Unable to resolve the API spec..  ${err.message}`); // eslint-disable-line no-console\n    }\n  }\n\n  async afterSpecParsedAndValidated(spec) {\n    this.resolvedSpec = spec;\n    const specLoadedEvent = new CustomEvent('spec-loaded', { detail: spec });\n    this.dispatchEvent(specLoadedEvent);\n  }\n\n  // Called by anchor tags created using markdown\n  handleHref(e) {\n    if (e.target.tagName.toLowerCase() === 'a') {\n      if (e.target.getAttribute('href').startsWith('#')) {\n        const gotoEl = this.shadowRoot.getElementById(e.target.getAttribute('href').replace('#', ''));\n        if (gotoEl) {\n          gotoEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n        }\n      }\n    }\n  }\n\n  // Example Dropdown @change Handler\n  onSelectExample(e) {\n    const exampleContainerEl = e.target.closest('.json-schema-example-panel');\n    const exampleEls = [...exampleContainerEl.querySelectorAll('.example')];\n    exampleEls.forEach((v) => {\n      v.style.display = v.dataset.example === e.target.value ? 'flex' : 'none';\n    });\n  }\n\n  async scrollToEventTarget(event) {\n    const navEl = event.currentTarget;\n    if (!navEl.dataset.contentId) {\n      return;\n    }\n    const contentEl = this.shadowRoot.getElementById(navEl.dataset.contentId);\n    if (contentEl) {\n      contentEl.scrollIntoView({ behavior: 'auto', block: 'start' });\n    }\n  }\n}\ncustomElements.define('json-schema-viewer', JsonSchemaViewer);\n"], "names": [], "sourceRoot": ""}