package request

import (
	"context"
	"net/http"
	"net/url"
	"testing"
	"time"
)

func Test_Get(t *testing.T) {
	uri := "https://**********:8000/adminyx/admin/v1/key_batch/list"

	uv := url.Values{}
	uv.Set("page", "1")
	uv.Set("limit", "2")

	h := http.Header{
		"Content-Type": []string{"application/x-www-form-urlencoded"},
	}

	hc := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,              // 最大空闲连接数
			MaxIdleConnsPerHost: 20,               // 每个主机的最大空闲连接数
			IdleConnTimeout:     30 * time.Second, // 空闲连接超时时间
		},
	}

	respHeader, respBody, err := GET(context.Background(), uri+"?"+uv.Encode(), NewOptions(WithHeaders(h), WithHttpClient(hc)))
	if err != nil {
		t.<PERSON>rror(err)
		return
	}

	t.Logf("响应体:", string(respBody))
	t.Logf("响应头:", respHeader)
}
