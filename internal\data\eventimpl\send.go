package eventimpl

import (
	"context"

	"ccb/internal/biz/event"
	"ccb/internal/data"

	event2 "codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/event"

	"github.com/go-kratos/kratos/v2/log"
)

type SendEventImpl struct {
	event.SendEvent
	hLog *log.Helper
	data *data.Data
}

// NewSendEventImpl 发布事件工厂方法
func NewSendEventImpl(hLog *log.Helper, data *data.Data) event.SendEvent {
	return &SendEventImpl{hLog: hLog, data: data}
}

func (s *SendEventImpl) SendSync(ctx context.Context, topicName string, body []byte, sendOptions ...event2.SendOption) error {
	// 发送消息
	return s.data.MqProducer.SendSync(ctx, topicName, body, sendOptions...)
}

func (s *SendEventImpl) SendASync(ctx context.Context, topicName string, body []byte, errFn func(error), sendOptions ...event2.SendOption) error {
	// 发送消息
	err := s.data.MqProducer.SendAsync(ctx, topicName, body, func(err error) {
		if err == nil {
			return
		}
		s.hLog.Errorf("异步发送消息出错：topName=%s，body=%s，err=%s", topicName, body, err.Error())
		errFn(err)
	}, sendOptions...)
	return err
}

func (s *SendEventImpl) SendAsyncNotFn(ctx context.Context, topic string, body []byte, sendOptions ...event2.SendOption) error {
	// 发送消息
	err := s.data.MqProducer.SendAsync(ctx, topic, body, func(err error) {
		if err == nil {
			return
		}
		s.hLog.Errorf("异步发送消息出错：topName=%s，body=%s，err=%s", topic, body, err.Error())
	}, sendOptions...)
	return err
}
