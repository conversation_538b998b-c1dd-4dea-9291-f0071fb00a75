version: '3.7'
services:
  xaInsurance:
    #    build:
    #      context: .
    #      dockerfile: Dockerfile_win_local
    image: qing7260/lsxd:latest
    container_name: xainsurance
    restart: unless-stopped
    environment:
      - "TZ=Asia/Shanghai"
    tty: true
    command:
      /usr/local/go/bin/go env -w GO111MODULE=on && /usr/local/go/bin/go env -w GOPROXY=https://goproxy.cn,direct
    volumes:
      - ./:/app
    working_dir:
      /app
    entrypoint:
      - /bin/sh
      - -c
      - tail -f /dev/null


# 快速构建windows开发环境


# 进入项目根目录
# 启动命令
# docker compose up
# 进入容器
# docker exec -it xaInsurance /bin/bash
# 配置go环境
# export PATH=$PATH:/root/go/bin:/usr/local/go/bin && source /etc/profile
# 配置go代理
# go env -w GO111MODULE=on && go env -w GOPROXY=https://goproxy.cn,direct
