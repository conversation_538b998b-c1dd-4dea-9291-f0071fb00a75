package front

import (
	http2 "net/http"

	"ccb/internal/biz"
	"ccb/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type CCBNotifyService struct {
	logger *log.Helper
	c      *conf.Bootstrap
	ccbBiz *biz.CCBBiz
}

func NewCCBNotifyService(logger *log.Helper, c *conf.Bootstrap, ccbBiz *biz.CCBBiz) *CCBNotifyService {
	return &CCBNotifyService{logger: logger, c: c, ccbBiz: ccbBiz}
}

// ProductList 产品列表
func (srv *CCBNotifyService) ProductList(ctx http.Context) error {
	header, body := srv.ccbBiz.ProductList(ctx)
	return srv.response(ctx, header, body)
}

// ProductStock 产品库存
func (srv *CCBNotifyService) ProductStock(ctx http.Context) error {
	header, body := srv.ccbBiz.ProductStock(ctx)
	return srv.response(ctx, header, body)
}

// CouponReceive 优惠券领取
func (srv *CCBNotifyService) CouponReceive(ctx http.Context) error {
	header, body := srv.ccbBiz.CouponReceive(ctx)
	return srv.response(ctx, header, body)
}

// DynamicVerifyCode 动态核销码
func (srv *CCBNotifyService) DynamicVerifyCode(ctx http.Context) error {
	return nil
}

// CouponStatus 优惠券状态
func (srv *CCBNotifyService) CouponStatus(ctx http.Context) error {
	header, body := srv.ccbBiz.CouponStatus(ctx)
	return srv.response(ctx, header, body)
}

// CouponCancel 优惠券作废
func (srv *CCBNotifyService) CouponCancel(ctx http.Context) error {
	header, body := srv.ccbBiz.CouponCancelV2(ctx)
	return srv.response(ctx, header, body)
}

// ReconciliationNotify 对账单处理回调
func (srv *CCBNotifyService) ReconciliationNotify(ctx http.Context) error {
	header, body := srv.ccbBiz.ReconciliationNotify(ctx)
	return srv.response(ctx, header, body)
}

// UserRecharge 直充充值
func (srv *CCBNotifyService) UserRecharge(ctx http.Context) error {
	header, body := srv.ccbBiz.UserRecharge(ctx)
	return srv.response(ctx, header, body)
}

// 格式化回复
func (srv *CCBNotifyService) response(ctx http.Context, header map[string]string, byt []byte) error {
	if val, ok := header["Env"]; ok && val == "lsxd" {
		ctx.String(200, string(byt))
		return nil
	}
	for s := range header {
		ctx.Response().Header().Set(s, header[s])
	}
	_ = ctx.Blob(http2.StatusOK, "application/octet-stream;charset=UTF-8", byt)
	return nil
}

// WechatCpnRegisterNotifyTag 微信券码注册通知
func (srv *CCBNotifyService) WechatCpnRegisterNotifyTag(ctx http.Context) error {
	return srv.ccbBiz.WechatCpnRegisterNotifyTag(ctx)
}

// GetReconciliationInfo 微信立减金自动对账
func (srv *CCBNotifyService) GetReconciliationInfo(ctx http.Context) error {
	resp := srv.ccbBiz.GetReconciliationInfo(ctx)
	return ctx.JSON(200, resp)
}

func (srv *CCBNotifyService) Proxy(ctx http.Context) error {
	srv.logger.Infof("收到代理回调 notify body: %s", ctx.Query().Encode())
	link, err := srv.ccbBiz.Proxy(ctx)
	if err != nil {
		return ctx.JSON(200, map[string]string{"error": err.Error()})
	}
	http2.Redirect(ctx.Response(), ctx.Request(), link, http2.StatusMovedPermanently)
	return nil
}
