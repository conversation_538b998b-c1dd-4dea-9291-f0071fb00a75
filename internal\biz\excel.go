package biz

import (
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"ccb/internal/pkg"
	"ccb/internal/pkg/helper/attachment"

	"github.com/go-kratos/kratos/v2/log"
)

type ExcelBiz struct {
	logHelper *log.Helper
}

func NewExcelBiz(logHelper *log.Helper) *ExcelBiz {
	return &ExcelBiz{logHelper: logHelper}
}

// StartExport 开始导出
func (e *ExcelBiz) StartExport(header []string, sheet ...string) (*pkg.Excel, error) {
	excel, err := pkg.NewExcel(sheet...)
	if err != nil {
		return nil, fmt.Errorf("创建excel失败:%s", err.Error())
	}
	err = excel.SetSheetHeader(header)
	if err != nil {
		return nil, fmt.Errorf("设置excel表头失败:%s", err.<PERSON>rror())
	}
	return excel, nil
}

// ExportData 导出数据
func (e *ExcelBiz) ExportData(data []interface{}, excel *pkg.Excel) error {
	err := excel.SetSheetRow(data)
	if err != nil {
		return fmt.Errorf("写入excel失败:%s", err.Error())
	}
	return nil
}

// EndExport 导出完成
func (e *ExcelBiz) EndExport(excel *pkg.Excel) (*attachment.UploadResp, error) {
	filePath := "./export/"
	finfo, err := os.Stat(filePath)
	if (err != nil && os.IsNotExist(err)) || !finfo.IsDir() { // 不存在文件夹则创建文件夹
		_ = os.MkdirAll(filePath, os.ModePerm)
	}
	fp := path.Join(filePath, fmt.Sprintf("%s.xlsx", strconv.FormatInt(time.Now().Unix(), 10)))
	err = excel.Save(fp)
	if err != nil {
		return nil, err
	}
	fp = strings.TrimPrefix(fp, ".")
	return &attachment.UploadResp{
		Url:        fp,
		PreviewUrl: fp,
	}, nil
}
