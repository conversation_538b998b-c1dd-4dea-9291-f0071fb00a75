package valobj

import "strconv"

type ProductType int

const (
	ProductTypeDirct ProductType = iota + 1
	ProductTypeCardZLTX
	ProductTypeKeyZLTX
	ProductTypeKeyMarket
	ProductTypeVoucherWechat
	ProductTypeYmt
)

var productTypeMap = map[ProductType]string{
	ProductTypeDirct:         "直充",
	ProductTypeCardZLTX:      "直连天下卡密",
	ProductTypeKeyZLTX:       "直连天下延伸卡密",
	ProductTypeKeyMarket:     "营销系统延伸卡密",
	ProductTypeVoucherWechat: "微信立减金",
	ProductTypeYmt:           "易码通商品",
}

func (p ProductType) GetName() string {
	if _, ok := productTypeMap[p]; !ok {
		return strconv.Itoa(int(p))
	}
	return productTypeMap[p]
}

func (p ProductType) GetValue() int {
	return int(p)
}
