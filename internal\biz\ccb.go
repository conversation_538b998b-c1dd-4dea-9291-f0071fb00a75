package biz

import (
	"bufio"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"math"
	http2 "net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"ccb/internal/biz/bo"
	"ccb/internal/biz/do"
	"ccb/internal/biz/repository"
	"ccb/internal/biz/rpc"
	"ccb/internal/biz/valobj"
	"ccb/internal/biz/wechatrepository"
	"ccb/internal/conf"
	"ccb/internal/data/ent"
	"ccb/internal/pkg"
	"ccb/internal/pkg/crypto"
	"ccb/internal/pkg/helper"
	"ccb/internal/pkg/helper/attachment"

	attachmentsdk "codeup.aliyun.com/5f9118049cffa29cfdd3be1c/attachment-sdk"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/api"
	"github.com/duke-git/lancet/v2/retry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
	"github.com/pkg/errors"
	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
)

var (
	defaultFormatLayout = "********150405"
	json                = jsoniter.ConfigCompatibleWithStandardLibrary
)

type CCBBiz struct {
	c                  *conf.Bootstrap
	log                *log.Helper
	ccbrpc             rpc.CCBRpc
	zltxrpc            rpc.ZLTXRpc
	marketRpc          rpc.MarketRpc
	orderRepo          repository.OrderRepo
	couponRepo         repository.CouponRepo
	couponLogRepo      repository.CouponLogRepo
	productRepo        repository.ProductRepo
	stockRepo          repository.StockRepo
	reconciliationRepo repository.ReconciliationRepo
	transactionRepo    repository.TransactionRepo
	wechatCpnRepo      wechatrepository.WechatCpnRepo
	smsRepo            repository.Sms
	ymtrpc             rpc.YmtRpc
	fixServer          *coroutine.Fixed
}

func NewCCBBiz(
	c *conf.Bootstrap,
	log *log.Helper,
	ccbrpc rpc.CCBRpc,
	zltxrpc rpc.ZLTXRpc,
	marketRpc rpc.MarketRpc,
	orderRepo repository.OrderRepo,
	couponRepo repository.CouponRepo,
	couponLogRepo repository.CouponLogRepo,
	productRepo repository.ProductRepo,
	stockRepo repository.StockRepo,
	reconciliationRepo repository.ReconciliationRepo,
	transactionRepo repository.TransactionRepo,
	wechatCpnRepo wechatrepository.WechatCpnRepo,
	smsRepo repository.Sms,
	ymtrpc rpc.YmtRpc,
) *CCBBiz {
	extra.RegisterFuzzyDecoders()
	return &CCBBiz{
		c:                  c,
		log:                log,
		ccbrpc:             ccbrpc,
		zltxrpc:            zltxrpc,
		marketRpc:          marketRpc,
		orderRepo:          orderRepo,
		couponRepo:         couponRepo,
		couponLogRepo:      couponLogRepo,
		productRepo:        productRepo,
		stockRepo:          stockRepo,
		transactionRepo:    transactionRepo,
		reconciliationRepo: reconciliationRepo,
		wechatCpnRepo:      wechatCpnRepo,
		smsRepo:            smsRepo,
		ymtrpc:             ymtrpc,
		fixServer:          coroutine.NewFixed(10),
	}
}

func (cb *CCBBiz) ProductList(httpCtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDS023")
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.ProductListReq)
	_ = json.Unmarshal(body, &req)
	if req.PageNo < 1 {
		req.PageNo = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 1
	}
	if req.MrchId != "" && req.MrchId != cb.c.CcbMap[header["Channel-Id"]].PlatformId {
		return cb.response(header, &do.CodeMsg{
			Code: "4000",
			Msg:  "商户号不匹配",
		})
	}
	if req.PdCl != "" && req.PdCl != "900" {
		resp := &do.CCBProductListResp{
			Code:      "0000",
			Msg:       "success",
			PageSize:  req.PageSize,
			PageNo:    req.PageNo,
			Total:     0,
			TotalPage: 0,
			Products:  make([]do.CCBProductDo, 0),
		}
		return cb.response(header, resp)
	}
	list, page, err := cb.stockRepo.FindAll(ctx, &bo.ProductStockListBo{
		MerchantId: header["Channel-Id"],
		Page: &bo.ReqPageBo{
			Size: req.PageSize,
			Num:  req.PageNo,
		},
		ProductCode: req.ProductId,
		Status:      1,
	})
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "4000",
			Msg:  err.Error(),
		})
	}
	products := make([]do.CCBProductDo, 0)
	for i := range list {
		products = append(products, do.CCBProductDo{
			ProductId:   list[i].Product.Code,
			ProductName: list[i].Product.Name,
			Desc:        list[i].Product.Desc,
			MrchId:      cb.c.CcbMap[header["Channel-Id"]].PlatformId,
			FVal:        list[i].Product.FacePrice.FormatYuan(),
			CstPrc:      list[i].Product.CostPrice.FormatYuan(),
			Attr:        list[i].Product.Attr,
			UdcrgTm:     "",
			Expire:      nil,
			Limit:       "",
		})
	}
	resp := &do.CCBProductListResp{
		Code:      "0000",
		Msg:       "success",
		PageSize:  page.Size,
		PageNo:    page.Num,
		Total:     page.Total,
		TotalPage: int(math.Ceil(float64(page.Total) / float64(page.Size))),
		Products:  products,
	}
	return cb.response(header, resp)
}

func (cb *CCBBiz) ProductStock(httpCtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDS022")
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.ProductStockReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	if req.ProductId == "" {
		return cb.response(header, &do.CodeMsg{
			Code: "2101",
			Msg:  "产品编号不能为空",
		})
	}
	ps, err := cb.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductCode: req.ProductId,
		MerchantId:  header["Channel-Id"],
		ActivityId:  req.DccpAvyId,
		Status:      1,
	})
	if err != nil {
		if ent.IsNotFound(err) {
			return cb.response(header, &do.CodeMsg{
				Code: "3002",
				Msg:  "不存在库存",
			})
		}
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	return cb.response(header, &do.CCBProductStockDo{
		ProductId:  ps.Product.Code,
		PrpIvntNum: strconv.Itoa(ps.Total),
		AvlIvntNum: strconv.Itoa(ps.Residue),
		UsedNum:    strconv.Itoa(ps.UsedNum),
		UdtTm:      &ps.UpdatedTime,
	})
}

func (cb *CCBBiz) CouponStatus(httpCtx http.Context) (map[string]string, []byte) {
	resp := &do.CCBResponse{Header: make(map[string]string)}
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDS004")
	resp.Header = header
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.CCBCouponStatusReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2102",
			Msg:  err.Error(),
		})
	}
	coupon, err := cb.couponRepo.FindByCode(ctx, header["Channel-Id"], req.CouponCode)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2102",
			Msg:  err.Error(),
		})
	}
	if coupon.Status == valobj.CouponStatusUnkown { // 同步状态失败的情况
		coupon, err = cb.couponRepo.UpdateStatus(ctx, coupon.Id, 0, coupon.Status, valobj.CouponStatusUnused, coupon.TransactionId)
		if err != nil {
			return cb.response(header, &do.CouponStatusResp{
				Code:         "0000",
				Msg:          "success",
				CouponCode:   coupon.Code,
				CouponStatus: "03",
				StatusDesc:   "",
				ExpireTm:     coupon.ExpireTime.Format(defaultFormatLayout),
			})
		}
	}
	couponStatus := "03"
	switch coupon.Status {
	case valobj.CouponStatusUnused:
		couponStatus = "00"
	case valobj.CouponStatusUsed, valobj.CouponStatusOut, valobj.CouponStatusVerify:
		couponStatus = "01"
	case valobj.CouponStatusCancel:
		couponStatus = "02"
	}
	resp.Body = &do.CouponStatusResp{
		Code:         "0000",
		Msg:          "success",
		CouponCode:   coupon.Code,
		CouponStatus: couponStatus,
		StatusDesc:   "",
		ExpireTm:     coupon.ExpireTime.Format(defaultFormatLayout),
	}
	if couponStatus == "01" {
		prftAmt := coupon.Product.FacePrice.FormatYuanWithPrec(2)
		useTm := coupon.UpdateTime.Format(defaultFormatLayout)
		couponLog, _ := cb.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
			CouponId: coupon.Id,
		})
		if couponLog != nil && (couponLog.Status == valobj.CouponStatusUsed || couponLog.Status == valobj.CouponStatusVerify) {
			if coupon.Product.Type == valobj.ProductTypeYmt {
				couponlogReq := &key.Notify{}
				err = json.Unmarshal([]byte(couponLog.RequestData), couponlogReq)
				if err == nil {
					if couponlogReq.Data.NotifyId != "" {
						if couponlogReq.Data.SettlementPrice > 0 {
							prftAmt = strconv.FormatFloat(float64(couponlogReq.Data.SettlementPrice), 'f', 2, 64)
						}
						if couponlogReq.Data.UsageTime != "" {
							useTm = strings.NewReplacer("-", "", ":", "", " ", "").Replace(couponlogReq.Data.UsageTime)
						}
					}
				}
			}
		}
		resp.Body = &do.CouponStatusResp{
			Code:         "0000",
			Msg:          "success",
			CouponCode:   coupon.Code,
			CouponStatus: couponStatus,
			UseOrderId:   coupon.TransactionId,
			OrderAmt:     "",
			PayAmt:       "",
			PrftAmt:      prftAmt,
			StoreId2:     "lsxd",
			UseTm:        useTm,
			ExpireTm:     "",
		}
	}
	return cb.response(resp.Header, resp.Body)
}

func (cb *CCBBiz) CouponReceive(httpCtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDS002")
	now := time.Now()
	resp := &do.CouponReceiveResp{
		Code:       codeMsg.Code,
		Msg:        codeMsg.Msg,
		Status:     "2",
		OrderId:    "",
		AsynFlag:   "1",
		CompleteTm: now.Format(defaultFormatLayout),
	}
	if codeMsg.Code != "0000" {
		return cb.response(header, resp)
	}
	req := new(bo.CCBCouponReceiveReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		resp.Code = "2000"
		resp.Msg = err.Error()
		return cb.response(header, &resp)
	}
	if req.Num < 1 {
		req.Num = 1
	}
	if req.RetryCnt == 2 {
		cb.log.Errorf("订单%s重试%d次", req.OrderId, req.RetryCnt)
	}
	res, byt := cb.checkOrder(header, req.OrderId)
	if byt != nil {
		return res, byt
	}
	// 先判断库存
	stock, err := cb.checkStock(req.ProductId, header["Channel-Id"], req.DccpAvyId, req.Num)
	if stock == nil {
		if err != nil {
			if ent.IsNotFound(err) {
				resp.Code = "3101"
				resp.Msg = "产品编号不存在"
				return cb.response(header, &resp)
			}
			cb.log.Errorf("查询库存失败:%s", err.Error())
		}
		resp.Code = "9000"
		resp.Msg = "系统异常"
		return cb.response(header, &resp)
	}
	if stock.Product.Type == valobj.ProductTypeDirct {
		resp.Code = "9000"
		resp.Msg = "不支持直充商品"
		return cb.response(header, &resp)
	}
	if codeMsg := cb.checkParams(stock, req); codeMsg != nil {
		resp.Code = codeMsg.Code
		resp.Msg = codeMsg.Msg
		return cb.response(header, &codeMsg)
	}
	payAmt, _ := strconv.ParseFloat(req.PaySummary.PayAmt, 64)
	integralAmt, _ := strconv.ParseFloat(req.PaySummary.IntegralAmt, 64)
	mrchCost, _ := strconv.ParseFloat(req.PaySummary.MrchCost, 64)
	bankCost, _ := strconv.ParseFloat(req.PaySummary.BankCost, 64)
	extraData := req.ExtrBsnData.Unmarshal()
	if stock.Product.Type == valobj.ProductTypeVoucherWechat && (extraData == nil || extraData.WechtOpenidCd == "" || extraData.WechtAppidCd == "") {
		resp.Code = "2102"
		resp.Msg = "领取微信立减金缺少openid"
		return cb.response(header, &resp)
	}
	batchNo := ""
	if extraData != nil {
		if extraData.CrdTpCd == "01" && stock.BatchNo != "" { // 借记卡
			batchNo = stock.BatchNo
		}
		if extraData.CrdTpCd == "02" && stock.BatchNo2 != "" { // 信用卡
			batchNo = stock.BatchNo2
		}
	}
	order := &do.OrderDo{}
	_ = cb.transactionRepo.Exec(ctx, func(tx context.Context) error {
		order, err = cb.orderRepo.CreateE(tx, &do.OrderDo{
			OrderNumber:        req.OrderId,
			UserId:             req.UserId,
			Type:               valobj.OrderTypeCard,
			MerchantId:         header["Channel-Id"],
			UpstreamMerchantID: stock.Key,
			ProductId:          stock.ProductID,
			ProductCode:        stock.ProductCode,
			Number:             req.Num,
			TotalFee:           valobj.NewMoneyFromYuan(payAmt),
			Price:              valobj.NewMoneyFromYuan(payAmt).Add(valobj.NewMoneyFromYuan(integralAmt)).Add(valobj.NewMoneyFromYuan(mrchCost)).Add(valobj.NewMoneyFromYuan(bankCost)),
			Status:             valobj.OrderStatusWaitRecharge,
			ActivityId:         req.DccpAvyId,
			PayInfo:            string(body),
			OurAppId:           cb.c.CcbMap[header["Channel-Id"]].OurAppId,
			SendMchId:          stock.Product.SendMchId,
			CreatorMchId:       stock.Product.CreatorMchId,
			BatchNo:            batchNo,
		})
		if err != nil {
			return err
		}
		err = cb.stockRepo.UpdateResidue(tx, stock.ID, -req.Num)
		return err
	})
	if err != nil {
		cb.log.Errorf("创建订单%s失败:%s", req.OrderId, err.Error())
		resp.Code = "9000"
		resp.Msg = "本次领取失败,请稍候重试"
		return cb.response(header, &resp)
	}
	err = cb.couponReceive(ctx, header, order, stock)
	if err != nil {
		if err.Error() == "订单失败" {
			resp.Code = "9000"
			resp.Msg = "本次领取失败,请稍候重试"
			return cb.response(header, &resp)
		}
		resp.Code = "9000"
		resp.Msg = err.Error()
		return cb.response(header, &resp)
	}
	coupons, _ := cb.couponRepo.FindByOrderId(ctx, order.Id)
	respCoupons := make([]do.CCBCoupon, 0, len(coupons))
	if len(coupons) > 0 {
		for _, c := range coupons {
			respCoupons = append(respCoupons, do.CCBCoupon{
				CouponCode: c.Code,
				Password:   "",
				Link:       c.Link,
			})
		}
	}
	if len(respCoupons) > 0 {
		return cb.response(header, &do.CouponReceiveResp{
			Code:       "0000",
			Msg:        "权益发放成功",
			Status:     "3",
			OrderId:    req.OrderId,
			AsynFlag:   stock.Product.Attr[1:2],
			UseType:    stock.Product.Attr[2:3],
			RespFlag:   "1",
			CompleteTm: order.UpdateTime.Format(defaultFormatLayout),
			Coupons:    respCoupons,
		})
	}
	return cb.response(header, &do.CouponReceiveResp{
		Code:     "0000",
		Msg:      "权益发放中",
		Status:   "1",
		OrderId:  req.OrderId,
		AsynFlag: "2",
		UseType:  "1",
	})
}

func (cb *CCBBiz) couponReceive(ctx context.Context, header map[string]string, order *do.OrderDo, stock *do.StockDo) error {
	req := new(bo.CCBCouponReceiveReq)
	err := json.Unmarshal([]byte(order.PayInfo), &req)
	if err != nil {
		return err
	}
	now := time.Now()
	code := ""
	codeLink := ""
	status := valobj.OrderStatusRecharging
	exprtime := now.AddDate(10, 1, 1)
	extraData := req.ExtrBsnData.Unmarshal()
	if stock.Product.Type == valobj.ProductTypeVoucherWechat && (extraData == nil || extraData.WechtOpenidCd == "" || extraData.WechtAppidCd == "") {
		return errors.New("领取微信立减金缺少openid")
	}
	switch stock.Product.Type {
	case valobj.ProductTypeVoucherWechat:
		code, err = cb.wechatCpnRepo.SendCoupon(ctx, &bo.WechatSendCouponBo{
			StockSendMchId:    stock.Product.SendMchId,
			StockCreatorMchId: stock.Product.CreatorMchId,
			Appid:             extraData.WechtAppidCd,
			OutRequestNo:      req.OrderId,
			Openid:            extraData.WechtOpenidCd,
			StockId:           order.BatchNo,
		})
		status = valobj.OrderStatusRechargeFailed
		if code != "" {
			status = valobj.OrderStatusCompleted
		}
	case valobj.ProductTypeKeyMarket:
		marketConf := cb.c.MarketMap[stock.Key]
		ret, err := cb.marketRpc.ReceiveKey(&bo.MarketReceiveKeyReq{
			AppId:       marketConf.AppId,
			MemId:       marketConf.MerchantId,
			MobileNo:    req.UserId,
			PosId:       marketConf.AppId,
			ReqSerialNo: req.OrderId,
			SendMsg:     valobj.MarketSendMsgFalse,
			Timestamp:   now.Format(defaultFormatLayout),
			VoucherId:   req.ProductId,
			VoucherNum:  "1",
		})
		if err != nil || ret.ErrCode != "00" {
			if ret != nil && ret.Msg != "" {
				err = errors.New(ret.Msg)
			}
			status = valobj.OrderStatusRechargeFailed
		} else {
			code = ret.Data.VoucherCode
			codeLink = ret.Data.ShortUrl
			if strings.Contains(code, "http") {
				link, err := url.Parse(code)
				if err == nil && link.Path != "" {
					code = strings.TrimLeft(link.Path, "/")
				}
				if codeLink == "" {
					codeLink = ret.Data.VoucherCode
				}
			}
			if ret.Data.VoucherEdate != "" {
				exprtime, _ = time.Parse("********", ret.Data.VoucherEdate)
			}
		}
	case valobj.ProductTypeYmt:
		productNo := req.ProductId
		if stock.Code != "" {
			productNo = stock.Code
		}
		account := ""
		if order.BatchNo != "" {
			productNo = order.BatchNo
		}
		if stock.AccountType == valobj.AccountTypePhone {
			account = order.UserId
		}
		if cb.c.YmtMap[stock.Key] == nil {
			cb.log.Errorf("易码通，订单%s发放失败:%s", req.OrderId, "未找到配置")
			status = valobj.OrderStatusRechargeFailed
		} else {
			rsp, err := cb.ymtReceiveKey(req.OrderId, productNo, account, stock.Key)
			if err != nil {
				cb.log.Errorf("易码通，订单%s发放失败:%s", req.OrderId, err.Error())
				status = valobj.OrderStatusRechargeFailed
			} else {
				code = rsp.Key
				codeLink = rsp.Url
				if strings.Contains(codeLink, "http") {
					link, err := url.Parse(codeLink)
					if err == nil && link.Path != "" {
						link.Path = strings.Trim(link.Path, "/")
						if temp := strings.Split(link.Path, "/"); len(temp) > 0 {
							code = temp[len(temp)-1]
						}
					}
				}
				// 解析过期时间
				exprtime, _ = time.ParseInLocation("2006-01-02 15:04:05", rsp.ValidEndTime, time.Local)
				status = valobj.OrderStatusCompleted
			}
		}
	case valobj.ProductTypeCardZLTX, valobj.ProductTypeKeyZLTX:
		pid, _ := strconv.Atoi(req.ProductId)
		if stock.Code != "" {
			if oid, _ := strconv.Atoi(stock.Code); oid > 0 {
				pid = oid
			}
		}
		ret, err := cb.zltxrpc.CardOrder(order.UpstreamMerchantID, &api.CardOrderReq{
			OutTradeNo:      req.OrderId,
			ProductId:       pid,
			AccountType:     1,
			Number:          req.Num,
			NotifyUrl:       cb.c.ZltxMap[order.UpstreamMerchantID].NotifyUrl,
			ExtendParameter: "",
		})
		if err != nil || ret.IsFailed() {
			if err == nil {
				err = errors.New(ret.Message)
			}
			cb.log.Errorf("%s卡券订单失败:%s", order.OrderNumber, err.Error())
			status = valobj.OrderStatusRechargeFailed
		}
	default:
		cb.log.Errorf("订单%s发放失败,产品类型错误:%s", req.OrderId, stock.Product.Type.GetName())
		status = valobj.OrderStatusRechargeFailed
		err = errors.Errorf("产品类型错误:%s", stock.Product.Type.GetName())
	}
	if err != nil {
		return err
	}
	orderNumber := order.OrderNumber
	oldStatus := order.Status
	_ = retry.Retry(func() error {
		return cb.transactionRepo.Exec(ctx, func(tx context.Context) error {
			order, err = cb.orderRepo.UpdateStatus(tx, order.Id, oldStatus, status)
			if err == nil {
				if status == valobj.OrderStatusRechargeFailed {
					err = cb.stockRepo.UpdateResidue(tx, stock.ID, 1) // 失败 返回库存
				}
				if status == valobj.OrderStatusCompleted && (code != "" || codeLink != "") {
					_, err = cb.couponRepo.CreateE(tx, &do.CouponDo{
						Code:        code,
						Link:        codeLink,
						OrderId:     order.Id,
						ProductId:   order.ProductId,
						ProductCode: order.ProductCode,
						MerchantId:  order.MerchantId,
						Status:      valobj.CouponStatusUnused,
						Total:       order.Number,
						Residue:     order.Number,
						ExpireTime:  &exprtime,
						CreateTime:  &now,
					})
				}
			}
			return err
		})
	}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
	if err != nil {
		cb.log.Errorf("订单号:%s 更新状态%s为%s失败:%s", orderNumber, oldStatus.GetName(), status.GetName(), err.Error())
	} else {
		if status == valobj.OrderStatusRechargeFailed {
			return errors.New("订单失败")
		}
		if stock.SendType == valobj.StockSendTypePhone && status == valobj.OrderStatusCompleted && code != "" {
			cb.fixServer.Run(fmt.Sprintf("发送短信:%s:%s", order.UserId, order.OrderNumber), func() {
				cb.log.Infof("发送短信:%s:%s", order.UserId, order.OrderNumber)
				var sendErr error
				_ = retry.Retry(func() error {
					sendErr = cb.smsRepo.SendCpnSms(ctx, order.UserId, map[string]any{
						"name": stock.Product.Name,
						"code": code,
					})
					return sendErr
				}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
				if sendErr != nil {
					cb.log.Errorf("发送短信失败:%s:%s:%s", order.UserId, order.OrderNumber, sendErr.Error())
				}
			})
		}
	}
	return err
}

func (cb *CCBBiz) checkOrder(header map[string]string, orderNum string) (map[string]string, []byte) {
	respFlag := "2"
	ctx := context.Background()
	resp := &do.CouponReceiveResp{
		Code:       "0000",
		Msg:        "success",
		Status:     "2",
		OrderId:    "",
		AsynFlag:   "1",
		CompleteTm: time.Now().Format(defaultFormatLayout),
	}
	order, err := cb.orderRepo.FindByOrderNo(ctx, orderNum)
	if err != nil && !ent.IsNotFound(err) {
		cb.log.Errorf("%s查询订单失败:%s", orderNum, err.Error())
		resp.Code = "2000"
		resp.Msg = "查询失败"
		return cb.response(header, &resp)
	}
	if order != nil && order.Id > 0 { // 已经存在
		products, _ := cb.productRepo.FindE(ctx, order.ProductId)
		if len(products) == 0 {
			return cb.response(header, &do.CouponReceiveResp{
				Code:     "9000",
				Msg:      "查询失败",
				Status:   "2",
				OrderId:  orderNum,
				AsynFlag: products[0].Attr[1:2],
				UseType:  products[0].Attr[2:3],
				RespFlag: respFlag,
			})
		}
		if order.Status == valobj.OrderStatusRechargeFailed {
			return cb.response(header, &do.CouponReceiveResp{
				Code:     "9000",
				Msg:      "订单失败",
				Status:   "2",
				OrderId:  orderNum,
				AsynFlag: products[0].Attr[1:2],
				UseType:  products[0].Attr[2:3],
			})
		}
		if order.Status == valobj.OrderStatusCompleted || order.Status == valobj.OrderStatusVerify {
			if order.Type == valobj.OrderTypeDirect {
				return cb.response(header, &do.CouponReceiveResp{
					Code:     "0000",
					Msg:      "订单成功",
					Status:   "3",
					OrderId:  orderNum,
					AsynFlag: products[0].Attr[1:2],
					UseType:  products[0].Attr[2:3],
					RespFlag: respFlag,
				})
			}
			coupons, err := cb.couponRepo.FindByOrderId(ctx, order.Id)
			if err != nil {
				cb.log.Errorf("%s查询订单失败:%s", orderNum, err.Error())
				return cb.response(header, &do.CouponReceiveResp{
					Code:     "9100",
					Msg:      "权益发放中",
					Status:   "4",
					OrderId:  orderNum,
					AsynFlag: products[0].Attr[1:2],
					UseType:  products[0].Attr[2:3],
					RespFlag: respFlag,
				})
			}
			ccbcoupon := make([]do.CCBCoupon, 0)
			for i := range coupons {
				ccbcoupon = append(ccbcoupon, do.CCBCoupon{
					CouponCode: coupons[i].Code,
					Link:       coupons[i].Link,
				})
			}
			return cb.response(header, &do.CouponReceiveResp{
				Code:       "0000",
				Msg:        "订单成功",
				Status:     "3",
				OrderId:    orderNum,
				AsynFlag:   products[0].Attr[1:2],
				UseType:    products[0].Attr[2:3],
				RespFlag:   respFlag,
				CompleteTm: order.UpdateTime.Format(defaultFormatLayout),
				Coupons:    ccbcoupon,
			})
		}
		return cb.response(header, &do.CouponReceiveResp{
			Code:     "3999",
			Msg:      "权益发放中!",
			Status:   "1",
			OrderId:  orderNum,
			AsynFlag: products[0].Attr[1:2],
			UseType:  products[0].Attr[2:3],
			RespFlag: respFlag,
		})
	}
	return nil, nil
}

func (cb *CCBBiz) checkParams(stock *do.StockDo, req *bo.CCBCouponReceiveReq) *do.CodeMsg {
	if stock.Product == nil {
		return &do.CodeMsg{
			Code: "3101",
			Msg:  "产品编号不存在!",
		}
	}
	if stock.Product.Attr[4:5] == "0" && req.Num > 1 {
		return &do.CodeMsg{
			Code: "9000",
			Msg:  "当前商品不支持单次发放多张优惠券!",
		}
	}
	if stock.SendType == valobj.StockSendTypePhone || stock.AccountType == valobj.AccountTypePhone {
		if !pkg.IsMobile(req.UserId) {
			return &do.CodeMsg{
				Code: "9000",
				Msg:  "当前商品只支持手机号领取!",
			}
		}
	}
	return nil
}

func (cb *CCBBiz) CouponCancel(httpCtxtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtxtx, "P5STDS005")
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.CCBCouponCancelReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	coupon, err := cb.couponRepo.FindByCode(ctx, header["Channel-Id"], req.CouponCode)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}
	if coupon.Status == valobj.CouponStatusUsed || coupon.Status == valobj.CouponStatusOut || coupon.Status == valobj.CouponStatusVerify {
		return cb.response(header, &do.CodeMsg{
			Code: "3902",
			Msg:  "券码已使用,不能作废",
		})
	}
	if coupon.Status == valobj.CouponStatusCancel || coupon.Status == valobj.CouponStatusExpired {
		return cb.response(header, &do.CodeMsg{
			Code: "3901",
			Msg:  "券码已作废,不能重复作废",
		})
	}
	order, err := cb.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		cb.log.Errorf("查询订单%d失败:%s", coupon.OrderId, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "作废失败:系统异常",
		})
	}
	stock, err := cb.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductCode: coupon.ProductCode,
		ProductId:   coupon.ProductId,
		MerchantId:  coupon.MerchantId,
	})
	if err != nil {
		cb.log.Errorf("查询库存%d:%s失败:%s", coupon.OrderId, coupon.ProductCode, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}
	if stock.Product.Type != valobj.ProductTypeKeyMarket && stock.Product.Type != valobj.ProductTypeVoucherWechat {
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "该类型卡密不支持作废",
		})
	}
	queryResp, err := cb.marketRpc.QueryKey(&bo.MarketQueryKeyReq{
		AppId:       order.UpstreamMerchantID,
		MemId:       order.UpstreamMerchantID,
		MobileNo:    order.UserId,
		Timestamp:   time.Now().Format(defaultFormatLayout),
		VoucherCode: coupon.Code,
		VoucherId:   coupon.ProductCode,
	})
	if err != nil {
		cb.log.Errorf("查询卡密状态失败%d:%s失败:%s", coupon.OrderId, coupon.Code, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "查询卡密状态失败",
		})
	}
	if queryResp.Data.VoucherStatus != valobj.VoucherStatusSended {
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "该订单卡密状态不支持作废:" + string(queryResp.Data.VoucherStatus),
		})
	}
	beforeStatus := coupon.Status
	dateStatus := fmt.Sprintf("%d,%d", beforeStatus, valobj.CouponStatusCancel)
	coupon.Status = valobj.CouponStatusCancel
	coupon.Total = coupon.Total - 1
	coupon.Residue = coupon.Residue - 1
	coupon, err = cb.couponRepo.UpdateE(ctx, coupon)
	if err != nil {
		cb.log.Errorf("更新卡密状态失败%d:%s失败:%s", coupon.OrderId, coupon.Code, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}
	if stock.Product.Type != valobj.ProductTypeKeyMarket && stock.Product.Type != valobj.ProductTypeVoucherWechat {
		return cb.response(header, &do.CodeMsg{
			Code: "1302",
			Msg:  "该权益券不能作废",
		})
	}
	cancelret, err := cb.marketRpc.CancelKey(&bo.MarketCancelKeyReq{
		AppId:       cb.c.MarketMap[order.UpstreamMerchantID].AppId,
		MemId:       cb.c.MarketMap[order.UpstreamMerchantID].MerchantId,
		MobileNo:    order.UserId,
		Timestamp:   time.Now().Format(defaultFormatLayout),
		VoucherCode: coupon.Code,
		VoucherId:   coupon.ProductCode,
	})
	if err != nil || cancelret.ErrCode != "00" {
		if cancelret.Msg != "" {
			err = errors.New(cancelret.Msg)
		}
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}
	now := helper.ZeroToday()
	couponLog, err := cb.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
		BeginDate: &now,
		CouponId:  coupon.Id,
	})
	if err != nil && !ent.IsNotFound(err) {
		cb.log.Errorf("作废券码%s记录日志错误:%s", coupon.Code, err.Error())
		return cb.response(header, codeMsg)
	}
	if couponLog != nil && couponLog.Id > 0 {
		ss := strings.Split(couponLog.DateStatus, ",")
		dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusCancel)
	}
	_, _ = cb.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
		CouponId:     coupon.Id,
		BeforeStatus: beforeStatus,
		Status:       valobj.CouponStatusCancel,
		DateStatus:   dateStatus,
		RequestData:  string(body),
	})
	_ = cb.stockRepo.UpdateResidue(ctx, stock.ID, 1)
	return cb.response(header, &do.CodeMsg{Code: "0000", Msg: "success"})
}

func (cb *CCBBiz) UserRecharge(httpCtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDS006")
	resp := &do.CCBUserRechargeResp{
		Code:          codeMsg.Code,
		Msg:           codeMsg.Msg,
		Status:        "2",
		OrderId:       "",
		AsynFlag:      "1",
		CompleteTm:    time.Now().Format(defaultFormatLayout),
		PltfrmOrderId: "",
	}
	if codeMsg.Code != "0000" {
		return cb.response(header, resp)
	}
	req := new(bo.CCBUserRechargeReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		resp.Code = "2000"
		resp.Msg = err.Error()
		return cb.response(header, resp)
	}
	pid, _ := strconv.Atoi(req.ProductId)

	order, err := cb.orderRepo.FindByOrderNo(ctx, req.OrderId)
	if err != nil && !ent.IsNotFound(err) {
		cb.log.Errorf("查询订单%s失败:%s", req.OrderId, err.Error())
		resp.Code = "9000"
		resp.Msg = "系统异常"
		return cb.response(header, &resp)
	}
	if order != nil && order.Id > 0 { // 已经存在
		if order.Status == valobj.OrderStatusRechargeFailed {
			return cb.response(header, &do.CCBUserRechargeResp{
				Code:       "9000",
				Msg:        "订单失败",
				Status:     "2",
				OrderId:    req.OrderId,
				AsynFlag:   "1",
				CompleteTm: time.Now().Format(defaultFormatLayout),
			})
		}
		if order.Status == valobj.OrderStatusRecharging {
			return cb.response(header, &do.CCBUserRechargeResp{
				Code:     "3999",
				Msg:      "权益发放中",
				Status:   "1",
				OrderId:  req.OrderId,
				AsynFlag: "2",
			})
		}
		return cb.response(header, &do.CCBUserRechargeResp{
			Code:       "0000",
			Msg:        "订单成功",
			Status:     "3",
			OrderId:    req.OrderId,
			AsynFlag:   "2",
			CompleteTm: order.UpdateTime.Format(defaultFormatLayout),
		})
	}

	stock, err := cb.checkStock(req.ProductId, header["Channel-Id"], req.DccpAvyId, 1)
	if err != nil {
		resp.Code = "9000"
		resp.Msg = err.Error()
		return cb.response(header, &resp)
	}
	if stock.Product.Type != valobj.ProductTypeDirct {
		resp.Code = "9000"
		resp.Msg = "仅支持直充产品"
		return cb.response(header, &resp)
	}
	err = cb.stockRepo.UpdateResidue(ctx, stock.ID, -1)
	if err != nil {
		resp.Code = "9000"
		resp.Msg = err.Error()
		return cb.response(header, &resp)
	}
	if stock.Code != "" {
		if oid, _ := strconv.Atoi(stock.Code); oid > 0 {
			pid = oid
		}
	}
	order, err = cb.orderRepo.CreateE(ctx, &do.OrderDo{
		OrderNumber:        req.OrderId,
		UserId:             req.UserId,
		Type:               valobj.OrderTypeDirect,
		MerchantId:         header["Channel-Id"],
		UpstreamMerchantID: stock.Key,
		ProductId:          stock.ProductID,
		ProductCode:        stock.ProductCode,
		Number:             1,
		TotalFee:           0,
		Price:              valobj.NewMoneyFromYuan(float64(req.Amount)),
		Status:             valobj.OrderStatusWaitRecharge,
		ActivityId:         req.DccpAvyId,
		PayInfo:            string(body),
		OurAppId:           cb.c.CcbMap[header["Channel-Id"]].OurAppId,
		SendMchId:          stock.Product.SendMchId,
		CreatorMchId:       stock.Product.CreatorMchId,
		BatchNo:            stock.BatchNo,
	})
	if err != nil {
		resp.Code = "9000"
		resp.Msg = err.Error()
		return cb.response(header, resp)
	}
	ret, err := cb.zltxrpc.RechargeOrder(order.UpstreamMerchantID, &api.RechargeOrderReq{
		OutTradeNo:      req.OrderId,
		ProductId:       pid,
		RechargeAccount: req.UserId,
		AccountType:     1,
		Number:          1,
		NotifyUrl:       cb.c.ZltxMap[order.UpstreamMerchantID].NotifyUrl,
		ExtendParameter: "",
	})
	status := valobj.OrderStatusRecharging
	if err != nil || ret.IsFailed() {
		if err == nil {
			err = errors.New(ret.Message)
		}
		cb.log.Errorf("%s直充订单失败:%s", order.OrderNumber, err.Error())
		status = valobj.OrderStatusRechargeFailed
	}
	orderNumber := order.OrderNumber
	oldStatus := order.Status
	_ = retry.Retry(func() error {
		order, err = cb.orderRepo.UpdateStatus(ctx, order.Id, order.Status, status)
		return err
	}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
	if err != nil {
		cb.log.Errorf("订单号:%s 更新状态%s为%s失败:%s", orderNumber, oldStatus.GetName(), status.GetName(), err.Error())
		resp.Code = "9000"
		resp.Msg = err.Error()
		return cb.response(header, resp)
	}
	if status == valobj.OrderStatusRechargeFailed {
		err = cb.stockRepo.UpdateResidue(ctx, stock.ID, 1)
		resp = &do.CCBUserRechargeResp{
			Code:          "9000",
			Msg:           "订单失败",
			Status:        "2",
			OrderId:       req.OrderId,
			AsynFlag:      "2",
			CompleteTm:    time.Now().Format(defaultFormatLayout),
			PltfrmOrderId: "",
		}
		return cb.response(header, resp)
	}
	resp = &do.CCBUserRechargeResp{
		Code:          "0000",
		Msg:           "请求成功,权益发放中",
		Status:        "1",
		OrderId:       req.OrderId,
		AsynFlag:      "2",
		CompleteTm:    time.Now().Format(defaultFormatLayout),
		PltfrmOrderId: "",
	}
	return cb.response(header, resp)
}

func (cb *CCBBiz) checkStock(pid, mrchId, activityId string, num int) (*do.StockDo, error) {
	ctx := context.Background()
	stock, err := cb.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductCode: pid,
		MerchantId:  mrchId,
		Status:      1,
		ActivityId:  activityId,
	})
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, errors.New("该商户没有该商品")
		}
		cb.log.Errorf("%s:%s查询库存失败:%s", mrchId, pid, err.Error())
		return stock, errors.New("系统错误,请稍后再试")
	}
	if stock.Residue < num {
		return stock, errors.New("系统错误,请稍后再试")
	}
	return stock, nil
}

func (cb *CCBBiz) ReconciliationNotify(httpCtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "P5STDA001")
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.CCBReconciliationNotifyReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	reconciliation, err := cb.reconciliationRepo.FindByNumber(ctx, req.BatchNum)
	if err != nil {
		if ent.IsNotFound(err) {
			return cb.response(header, &do.CodeMsg{
				Code: "3001",
				Msg:  "对账文件批次号不存在",
			})
		}
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	if req.Result == "00" {
		if reconciliation.Status != valobj.ReconciliationStatusCompleted {
			// 更改状态
			err = cb.reconciliationRepo.UpdateStatusByNumber(ctx, reconciliation.Number, req.Msg, valobj.ReconciliationStatusCompleted)
			if err != nil {
				cb.log.Errorf("更新对账单号:%s,<%s>状态到<%s>失败:%s", req.BatchNum, reconciliation.Status.GetName(), valobj.ReconciliationStatusCompleted.GetName(), err.Error())
			}
		}
	} else {
		cb.SendReconciliation(ctx, &bo.PushReconciliationReq{
			Number: req.BatchNum,
			Status: []int{
				valobj.ReconciliationStatusWaitSend.GetValue(),
				valobj.ReconciliationStatusSended.GetValue(),
				valobj.ReconciliationStatusFailed.GetValue(),
			},
			BeginDate: reconciliation.StartDate.Format("2006-01-02"),
		})
	}
	return cb.response(header, &do.CodeMsg{Code: "0000", Msg: "success"})
}

func (cb *CCBBiz) SendReconciliation(ctx context.Context, reqbo *bo.PushReconciliationReq) (err error) {
	reqBo := &bo.ReconciliationListBo{Number: reqbo.Number, Status: make([]int, 0)}
	if len(reqbo.Status) > 0 {
		reqBo.Status = reqbo.Status
	} else {
		reqBo.Status = []int{
			valobj.ReconciliationStatusWaitSend.GetValue(),
			valobj.ReconciliationStatusFailed.GetValue(),
		}
	}
	beginDate := helper.ZeroToday().AddDate(0, 0, -1)
	reqBo.BeginDate = &beginDate
	if reqbo.BeginDate != "" {
		beginDate, err = time.Parse("2006-01-02", reqbo.BeginDate)
		if err == nil && beginDate.Unix() > 0 {
			reqBo.BeginDate = &beginDate
		}
	}
	list, err := cb.reconciliationRepo.SearchList(ctx, &bo.ReconciliationListBo{
		BeginDate: reqBo.BeginDate,
		Status:    reqBo.Status,
		Number:    reqBo.Number,
	})
	if err != nil {
		return err
	}
	for i := range list {
		fileUrl := attachmentsdk.GeneratePreviewPrivateUrl(
			cb.c.Rpc.AttachmentDomain,
			"",
			list[i].File,
			"",
			time.Now().Format(defaultFormatLayout),
			time.Now().Add(60*time.Second).Unix())
		resp, err := http2.Get(fileUrl)
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			continue
		}
		ret, err := cb.ccbrpc.SendReconciliation(list[i].MerchantId, list[i].Number, body)
		if err == nil && ret != nil && ret.Code == "0000" {
			_ = cb.reconciliationRepo.UpdateStatusByNumber(ctx, list[i].Number, "", valobj.ReconciliationStatusSended)
		}
	}
	return
}

// 创建对账单
func (cb *CCBBiz) CreateReconciliation(ctx context.Context, day int) error {
	enddate := helper.ZeroToday().AddDate(0, 0, 1-day)
	startdate := enddate.AddDate(0, 0, -day)

	rows, err := cb.reconciliationRepo.SearchList(ctx, &bo.ReconciliationListBo{
		BeginDate: &startdate,
		EndDate:   &enddate,
	})
	if err != nil {
		return err
	}
	rowMap := make(map[string]struct{})
	if len(rows) > 0 {
		for i := range rows {
			rowMap[rows[i].MerchantId] = struct{}{}
		}
	}
	now := time.Now()
	for s := range cb.c.CcbMap {
		if _, ok := rowMap[s]; ok {
			continue
		}
		copons, err := cb.couponLogRepo.DayRows(ctx, &bo.CouponLogListBo{
			BeginDate:  &startdate,
			EndDate:    &enddate,
			MerchantID: s,
		})
		if err != nil {
			return err
		}
		couponMap := make(map[int]*do.CouponDo)
		if len(copons) > 0 {
			coponIds := make([]int, len(copons))
			for _, coponLog := range copons {
				coponIds = append(coponIds, coponLog.CouponId)
			}
			cs, _, err := cb.couponRepo.FindPageList(ctx, &bo.CouponListBo{
				Ids:       coponIds,
				ChannelId: defaultChannelId,
			})
			if err != nil {
				panic(err)
			}
			for _, coupon := range cs {
				couponMap[coupon.Id] = coupon
			}
		}
		maxSize := 20000
		fileCount := (len(copons) / maxSize) + 1
		for i := 1; i <= fileCount; i++ {
			number := fmt.Sprintf("%s_%d", startdate.Format("********"), i)
			end := i * maxSize
			if end > len(copons) {
				end = len(copons)
			}
			fileUrl, err := cb.createFile(number, s, startdate, enddate, copons[(i-1)*maxSize:end], couponMap)
			if err != nil {
				return err
			}
			_, err = cb.reconciliationRepo.CreateE(ctx, &do.ReconciliationDo{
				Number:     number,
				MerchantId: s,
				File:       fileUrl,
				StartDate:  &startdate,
				EndDate:    &enddate,
				Status:     valobj.ReconciliationStatusWaitSend,
				Msg:        "",
				CreateTime: &now,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (cb *CCBBiz) createFile(number, platformId string, startdate, enddate time.Time, logs []*do.CouponLogDo, couponMap map[int]*do.CouponDo) (string, error) {
	fileName := fmt.Sprintf("%s.txt", number)
	file, err := os.Create(fileName)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writer := bufio.NewWriter(file)
	str := fmt.Sprintf("Version=2.0|@|PlatformId=%s|@|CreateTime=%s|@|BeginTime=%s|@|EndTime=%s|@|RecordNum=%d|@|",
		platformId, time.Now().Format(defaultFormatLayout), startdate.Format(defaultFormatLayout),
		enddate.Add(-1*time.Second).Format(defaultFormatLayout), len(logs))
	_, err = writer.WriteString(str + "\n")
	if err != nil {
		return "", err
	}
	for ix := range logs {
		if val, ok := couponMap[logs[ix].CouponId]; !ok || val.Product == nil {
			continue
		}
		changeStatus := strings.Split(logs[ix].DateStatus, ",")
		if len(changeStatus) < 2 || changeStatus[0] == changeStatus[1] {
			continue
		}
		usetm := logs[ix].CreateTime
		useOrderId := logs[ix].TransactionId
		operation := valobj.CCBCouponOperationToUsed.GetValue()
		if changeStatus[1] == strconv.Itoa(valobj.CouponStatusUnused.GetValue()) {
			operation = valobj.CCBCouponOperationToUnUsed.GetValue()
		} else {
			plainText := &bo.PlainText{}
			_ = json.UnmarshalFromString(logs[ix].RequestData, &plainText)
			if plainText.ConsumeInformation.ConsumeTime != "" {
				tm, err := helper.DateStringToTimeV2(plainText.ConsumeInformation.ConsumeTime)
				if err == nil && tm.Unix() > 0 {
					usetm = &tm
				}
			}
		}
		useAmt := ""
		unuseAmt := ""
		/* if operation == "011032" || operation == "011033" { // 011032：部分使用  011033：部分退单
			useAmt = couponMap[logs[ix].CouponId].Product.FacePrice.FormatYuanWithPrec(2)
			unuseAmt = "0.00"
		} */
		storeId := platformId
		str = fmt.Sprintf("%s|@|%s|@|%s|@|%s|@|%s|@|%s|@|%s|@|%s|@|",
			couponMap[logs[ix].CouponId].ProductCode,
			couponMap[logs[ix].CouponId].Code,
			operation,
			useOrderId,
			storeId,
			usetm.Format(defaultFormatLayout),
			useAmt,
			unuseAmt,
		)
		_, err = writer.WriteString(str + "\n")
		if err != nil {
			return "", err
		}
	}
	err = writer.Flush()
	if err != nil {
		return "", err
	}
	uploadResp, err := attachment.Upload(cb.c.Rpc.AttachmentDomain, fileName, "crmApi", "download", "file")
	if err != nil {
		return "", err
	}
	os.Remove(fileName)
	return uploadResp.Url, nil
}

func (cb *CCBBiz) ParseBody(httpCtx http.Context, transCode string) (ctx context.Context, header map[string]string, body []byte, resp *do.CodeMsg) {
	ctx, header, body, codeMsg := cb.bodyMap(httpCtx)
	if codeMsg.Code != "0000" {
		return ctx, header, body, codeMsg
	}
	resp = codeMsg
	if header["Trans-Code"] == "" || header["Trans-Code"] != transCode {
		resp.Code = "2002"
		resp.Msg = "transcode is not exist"
		return
	}
	return
}

func (cb *CCBBiz) bodyMap(ctx http.Context) (valueCtx context.Context, header map[string]string, body []byte, resp *do.CodeMsg) {
	headers := ctx.Request().Header
	header = make(map[string]string)
	for s := range headers {
		header[s] = headers.Get(s)
	}
	valueCtx = util.CopyValueCtx(ctx)
	resp = &do.CodeMsg{
		Code: "0000",
		Msg:  "success",
	}
	body, err := io.ReadAll(ctx.Request().Body)
	if err != nil || len(body) < 1 {
		resp.Code = "2100"
		resp.Msg = "body is empty"
		return
	}
	cb.log.Info("解密前的请求参数:", string(body))
	channelId := headers.Get("Channel-Id")
	if channelId == "" {
		resp.Code = "2000"
		resp.Msg = "channelId is empty"
		return
	}
	if _, ok := cb.c.CcbMap[channelId]; !ok {
		resp.Code = "2000"
		resp.Msg = "channelId is not exist"
		return
	}
	if val, ok := header["Env"]; ok && val == "lsxd" {
		return
	}
	err = cb.ccbrpc.VerifySignature(header, body)
	if err != nil {
		resp.Code = "1200"
		resp.Msg = "verify signature failed"
		return
	}
	body, err = cb.ccbrpc.DecryptData(header, body)
	if err != nil {
		resp.Code = "2101"
		resp.Msg = "decrypt data failed"
	}
	cb.log.Info("解密后的请求参数:", string(body))
	return
}

func (cb *CCBBiz) response(header map[string]string, body interface{}) (map[string]string, []byte) {
	byt, _ := json.Marshal(body)
	if val, ok := header["Env"]; ok && val == "lsxd" {
		return header, byt
	}
	byt, _ = cb.ccbrpc.EncryptData(header, byt)
	header = cb.ccbrpc.ResponseHeader(header, byt)
	return header, byt
}

func (cb *CCBBiz) Response(ctx http.Context, header map[string]string, body interface{}) {
	header, byt := cb.response(header, body)
	ctx.Response().Header().Set("Content-Type", "application/json")
	for s := range header {
		ctx.Response().Header().Set(s, header[s])
	}
	ctx.Response().Write(byt)
}

// 易码通获取券码
func (cb *CCBBiz) ymtReceiveKey(orderNo, productNo, account, ymtKey string) (rsp do.OrderResponse, err error) {
	req := &key.OrderRequest{
		OutBizNo:   orderNo,
		ActivityNo: productNo,
		Number:     1,
		Account:    account,
	}
	rsp, err = cb.ymtrpc.GetKey(req, ymtKey)

	return
}

// 营销系统或者易码通的卡券作废
func (cb *CCBBiz) CouponCancelV2(httpCtxtx http.Context) (map[string]string, []byte) {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtxtx, "P5STDS005")
	if codeMsg.Code != "0000" {
		return cb.response(header, codeMsg)
	}
	req := new(bo.CCBCouponCancelReq)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "2000",
			Msg:  err.Error(),
		})
	}
	coupon, err := cb.couponRepo.FindByCode(ctx, header["Channel-Id"], req.CouponCode)
	if err != nil {
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}
	if coupon.Status == valobj.CouponStatusUsed || coupon.Status == valobj.CouponStatusOut || coupon.Status == valobj.CouponStatusVerify {
		return cb.response(header, &do.CodeMsg{
			Code: "3902",
			Msg:  "券码已使用,不能作废",
		})
	}
	if coupon.Status == valobj.CouponStatusCancel || coupon.Status == valobj.CouponStatusExpired {
		return cb.response(header, &do.CodeMsg{
			Code: "3901",
			Msg:  "券码已作废,不能重复作废",
		})
	}
	order, err := cb.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		cb.log.Errorf("查询订单%d失败:%s", coupon.OrderId, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "作废失败:系统异常",
		})
	}
	stock, err := cb.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductCode: coupon.ProductCode,
		ProductId:   coupon.ProductId,
		MerchantId:  coupon.MerchantId,
	})
	if err != nil {
		cb.log.Errorf("查询库存%d:%s失败:%s", coupon.OrderId, coupon.ProductCode, err.Error())
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  err.Error(),
		})
	}

	if stock.Product.Type == valobj.ProductTypeYmt {
		ymtKey := stock.Key
		// 易码通商品
		if queryResp, err := cb.ymtrpc.Query(order.OrderNumber, ymtKey); err != nil {
			cb.log.Errorf("易码通查询订单%s失败:%s", order.OrderNumber, err.Error())
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  "查询易码通订单失败",
			})
		} else if queryResp.Status != valobj.YmtStatusNormal {
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  "该订单状态不支持作废:" + strconv.Itoa(queryResp.Status),
			})
		}

		// 请求易码通作废
		_, err = cb.ymtrpc.Discard(order.OrderNumber, ymtKey)
		if err != nil {
			cb.log.Errorf("易码通作废订单%s失败:%s", order.OrderNumber, err.Error())
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  "作废易码通订单失败",
			})
		}

		// 更新状态
		beforeStatus := coupon.Status
		dateStatus := fmt.Sprintf("%d,%d", beforeStatus, valobj.CouponStatusCancel)
		coupon.Status = valobj.CouponStatusCancel
		coupon.Total = coupon.Total - 1
		coupon.Residue = coupon.Residue - 1
		coupon, err = cb.couponRepo.UpdateE(ctx, coupon)
		if err != nil {
			cb.log.Errorf("更新易码通状态失败%d:%s失败:%s", coupon.OrderId, coupon.Code, err.Error())
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  err.Error(),
			})
		}

		// 记录日志
		now := helper.ZeroToday()
		couponLog, err := cb.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
			BeginDate: &now,
			CouponId:  coupon.Id,
		})
		if err != nil && !ent.IsNotFound(err) {
			cb.log.Errorf("作废券码%s记录日志错误:%s", coupon.Code, err.Error())
			return cb.response(header, codeMsg)
		}
		if couponLog != nil && couponLog.Id > 0 {
			ss := strings.Split(couponLog.DateStatus, ",")
			dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusCancel)
		}
		_, _ = cb.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
			CouponId:     coupon.Id,
			BeforeStatus: beforeStatus,
			Status:       valobj.CouponStatusCancel,
			DateStatus:   dateStatus,
			RequestData:  string(body),
		})
		_ = cb.stockRepo.UpdateResidue(ctx, stock.ID, 1)
	} else if stock.Product.Type == valobj.ProductTypeKeyMarket {
		// 营销系统商品
		queryResp, err := cb.marketRpc.QueryKey(&bo.MarketQueryKeyReq{
			AppId:       order.UpstreamMerchantID,
			MemId:       order.UpstreamMerchantID,
			MobileNo:    order.UserId,
			Timestamp:   time.Now().Format(defaultFormatLayout),
			VoucherCode: coupon.Code,
			VoucherId:   coupon.ProductCode,
		})
		if err != nil {
			cb.log.Errorf("查询卡密状态失败%d:%s失败:%s", coupon.OrderId, coupon.Code, err.Error())
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  "查询卡密状态失败",
			})
		}
		if queryResp.Data.VoucherStatus != valobj.VoucherStatusSended {
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  "该订单卡密状态不支持作废:" + string(queryResp.Data.VoucherStatus),
			})
		}
		beforeStatus := coupon.Status
		dateStatus := fmt.Sprintf("%d,%d", beforeStatus, valobj.CouponStatusCancel)
		coupon.Status = valobj.CouponStatusCancel
		coupon.Total = coupon.Total - 1
		coupon.Residue = coupon.Residue - 1
		coupon, err = cb.couponRepo.UpdateE(ctx, coupon)
		if err != nil {
			cb.log.Errorf("更新卡密状态失败%d:%s失败:%s", coupon.OrderId, coupon.Code, err.Error())
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  err.Error(),
			})
		}
		if stock.Product.Type != valobj.ProductTypeKeyMarket && stock.Product.Type != valobj.ProductTypeVoucherWechat {
			return cb.response(header, &do.CodeMsg{
				Code: "1302",
				Msg:  "该权益券不能作废",
			})
		}
		cancelret, err := cb.marketRpc.CancelKey(&bo.MarketCancelKeyReq{
			AppId:       cb.c.MarketMap[order.UpstreamMerchantID].AppId,
			MemId:       cb.c.MarketMap[order.UpstreamMerchantID].MerchantId,
			MobileNo:    order.UserId,
			Timestamp:   time.Now().Format(defaultFormatLayout),
			VoucherCode: coupon.Code,
			VoucherId:   coupon.ProductCode,
		})
		if err != nil || cancelret.ErrCode != "00" {
			if cancelret.Msg != "" {
				err = errors.New(cancelret.Msg)
			}
			return cb.response(header, &do.CodeMsg{
				Code: "9000",
				Msg:  err.Error(),
			})
		}
		now := helper.ZeroToday()
		couponLog, err := cb.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
			BeginDate: &now,
			CouponId:  coupon.Id,
		})
		if err != nil && !ent.IsNotFound(err) {
			cb.log.Errorf("作废券码%s记录日志错误:%s", coupon.Code, err.Error())
			return cb.response(header, codeMsg)
		}
		if couponLog != nil && couponLog.Id > 0 {
			ss := strings.Split(couponLog.DateStatus, ",")
			dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusCancel)
		}
		_, _ = cb.couponLogRepo.CreateE(ctx, &do.CouponLogDo{
			CouponId:     coupon.Id,
			BeforeStatus: beforeStatus,
			Status:       valobj.CouponStatusCancel,
			DateStatus:   dateStatus,
			RequestData:  string(body),
		})
		_ = cb.stockRepo.UpdateResidue(ctx, stock.ID, 1)
	} else {
		// 其他类型商品
		return cb.response(header, &do.CodeMsg{
			Code: "9000",
			Msg:  "该类型卡密不支持作废",
		})
	}

	return cb.response(header, &do.CodeMsg{Code: "0000", Msg: "success"})
}

func (cb *CCBBiz) HandleWeChatCpnNotify(ctx context.Context, req *bo.WechatCpnNotifyBo) error {
	cb.log.Infof("微信立减金通知:%+v", req)
	if req.PlainText.Status != valobj.WechatCpnStatusUsed && req.PlainText.Status != valobj.WechatCpnStatusExpired {
		// 不做处理
		return nil
	}
	reqStr, _ := json.Marshal(req.PlainText)
	now := time.Now()
	coupon, _ := cb.couponRepo.FindByCode(ctx, defaultChannelId, req.PlainText.CouponID)
	if coupon == nil {
		return errors.Errorf("没有找到该券码:%s", req.PlainText.CouponID)
	}
	if coupon.Status != valobj.CouponStatusUnused {
		cb.log.Infof("立减金券码%s已经处理", coupon.Code)
		return nil
	}
	order, err := cb.orderRepo.Find(ctx, coupon.OrderId)
	if err != nil {
		return errors.Errorf("coupon verify notify find order error: %s", err)
	}
	oldStatus := coupon.Status
	dateStatus := fmt.Sprintf("%d,%d", oldStatus, valobj.CouponStatusVerify)
	couponLog, err := cb.couponLogRepo.FindLastLog(ctx, &bo.CouponLogBo{
		BeginDate: &now,
		CouponId:  coupon.Id,
	})
	if err != nil && !ent.IsNotFound(err) {
		return errors.Errorf("coupon verify notify find coupon log error: %s", err)
	}
	if couponLog != nil && couponLog.Id > 0 {
		ss := strings.Split(couponLog.DateStatus, ",")
		dateStatus = fmt.Sprintf("%s,%d", ss[0], valobj.CouponStatusVerify)
	}
	switch req.PlainText.Status {
	case valobj.WechatCpnStatusUsed:
		ustm := time.Now()
		if req.PlainText.CreateTime != "" {
			tm, err := helper.DateStringToTimeV2(req.PlainText.ConsumeInformation.ConsumeTime)
			if err == nil && tm.Unix() > 0 {
				ustm = tm
			}
		}
		err = cb.transactionRepo.Exec(ctx, func(tx context.Context) error {
			coupon, err = cb.couponRepo.UpdateStatus(tx, coupon.Id, -1, coupon.Status, valobj.CouponStatusUsed, req.PlainText.ConsumeInformation.TransactionID)
			if err != nil {
				return err
			}
			_, err = cb.couponLogRepo.CreateE(tx, &do.CouponLogDo{
				CouponId:      coupon.Id,
				BeforeStatus:  oldStatus,
				Status:        valobj.CouponStatusVerify,
				TransactionId: coupon.TransactionId,
				DateStatus:    dateStatus,
				RequestData:   string(reqStr),
			})
			return err
		})
		if err != nil {
			return err
		}
		retry.Retry(func() error {
			_, err = cb.ccbrpc.CouponStatusNotify(coupon.MerchantId, &bo.CCBCouponStatusNotifyReq{
				ProductId:  coupon.ProductCode,
				OrderId:    order.OrderNumber,
				CouponCode: coupon.Code,
				Operation:  valobj.CCBCouponOperationToUsed,
				UseOrderId: req.PlainText.ConsumeInformation.TransactionID,
				OrderAmt:   "0", // todo  订单金额
				PayAmt:     "0", // todo  支付金额
				PrftAmt: func() string { // 优惠金额
					prft := float64(req.PlainText.ConsumeInformation.ConsumeAmount) / 100
					return strconv.FormatFloat(prft, 'f', 2, 64)
				}(),
				StoreId2: "lsxd",
				UseTm:    ustm.Format(defaultFormatLayout),
			})
			return err
		}, retry.RetryTimes(3), retry.RetryDuration(3*time.Second))
	case valobj.WechatCpnStatusExpired:
		_, err = cb.couponRepo.UpdateStatus(ctx, coupon.Id, 0, coupon.Status, valobj.CouponStatusExpired, coupon.TransactionId)
		if err != nil {
			return err
		}
	default:
		cb.log.Infof("微信券码状态%s,不处理", req.PlainText.Status)
	}
	return nil
}

func (cb *CCBBiz) WechatCpnRegisterNotifyTag(httpCtx http.Context) error {
	ctx, header, body, codeMsg := cb.ParseBody(httpCtx, "register")
	if codeMsg.Code != "0000" {
		return errors.New("解析请求失败")
	}
	req := new(bo.WechatCpnRegisterNotifyTagBo)
	err := json.Unmarshal(body, &req)
	if err != nil {
		return err
	}
	if req.BatchNo == "" {
		return errors.New("批次号不能为空")
	}
	stock, err := cb.stockRepo.FindOne(ctx, &bo.ProductStockBo{
		ProductId:   req.ProductId,
		ProductCode: req.ProductCode,
		MerchantId:  header["Channel-Id"],
		BatchNo:     req.BatchNo,
	})
	if err != nil {
		cb.log.Errorf("微信券码注册通知错误:%s", err.Error())
		return err
	}
	if stock == nil {
		cb.log.Errorf("微信券码注册通知错误,没有找到商品:%s", req.ProductCode)
		return errors.New("没有找到商品")
	}
	if stock.Status == valobj.IsEnableDisable {
		cb.log.Errorf("微信券码注册通知错误,商品状态不正确:%s", req.ProductCode)
		return errors.New("商品状态不正确")
	}
	if stock.MerchantID != "" && stock.MerchantID != header["Channel-Id"] {
		cb.log.Errorf("微信券码注册通知错误,商户号不正确:%s", header["Channel-Id"])
		return errors.New("商户号不正确")
	}
	body, err = cb.wechatCpnRepo.RegisterNotifyTag(ctx, stock.BatchNo)
	if err != nil {
		return err
	}
	return httpCtx.String(200, string(body))
}

func (cb *CCBBiz) GetReconciliationInfo(httpCtx http.Context) *bo.ReconciliationInfo {
	date := httpCtx.Query().Get("date")
	mchId := ""
	for s := range cb.c.CcbMap {
		if mchId == "" {
			mchId = s
		}
		break
	}
	resp := &bo.ReconciliationInfo{
		Code: "200",
		Data: bo.ReconciliationInfoData{
			Status:   2,
			DataList: make([]bo.ReconciliationInfoItem, 0),
		},
		Msg: "",
	}
	start, _ := time.Parse(time.DateOnly, date)
	if start.Unix() < 1 {
		resp.Code = "500"
		resp.Msg = "参数格式不正确"
		return resp
	}
	end := start.AddDate(0, 0, 1).Add(-1 * time.Second)

	ctx := util.CopyValueCtx(httpCtx)
	stockList, _, err := cb.stockRepo.FindAll(ctx, &bo.ProductStockListBo{
		MerchantId:  mchId,
		ProductType: valobj.ProductTypeVoucherWechat.GetValue(),
	})
	if err != nil {
		resp.Code = "500"
		resp.Msg = "数据处理中..."
		return resp
	}
	if stockList == nil || len(stockList) == 0 {
		return resp
	}
	ids := make([]int, 0, len(stockList))
	for _, stock := range stockList {
		ids = append(ids, stock.ProductID)
	}
	orders, _ := cb.orderRepo.FindAll(ctx, &bo.OrderListBo{
		StartTime:  &start,
		EndTime:    &end,
		MerchantId: mchId,
		ProductIds: ids,
	})
	for _, order := range orders {
		resp.Data.DataList = append(resp.Data.DataList, bo.ReconciliationInfoItem{
			OrderId:  order.OrderNumber,
			OrderNum: order.Price.GetYuan(),
		})
	}
	return resp
}

func (cb *CCBBiz) Proxy(httpCtx http.Context) (string, error) {
	mchId, _ := base64.RawURLEncoding.DecodeString(httpCtx.Vars().Get("mchId"))
	if mchId == nil {
		return "", errors.New("参数错误!")
	}
	cb.log.Infof("商户号:%s", mchId)
	if cb.c.CcbMap[string(mchId)] == nil {
		return "", errors.New("商户不存在")
	}
	encryptData := httpCtx.Query().Get("encryptData")
	if encryptData == "" {
		return "", errors.New("参数错误")
	}
	encryptData = strings.ReplaceAll(encryptData, " ", "+")
	xx, err := crypto.TripleDESDecrypt(cb.c.CcbProxyMap[string(mchId)].DesKey, encryptData)
	sss := make(map[string]any)
	json.Unmarshal(xx, &sss)
	fmt.Println(string(xx), sss)
	return "", err
}
