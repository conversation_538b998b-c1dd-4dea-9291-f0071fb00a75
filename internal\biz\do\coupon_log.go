package do

import (
	"time"

	"ccb/internal/biz/valobj"
)

type CouponLogDo struct {
	Id            int                 `json:"id"`
	CouponId      int                 `json:"coupon_id"`
	BeforeStatus  valobj.CouponStatus `json:"before_status"`
	Status        valobj.CouponStatus `json:"status"`
	DateStatus    string              `json:"date_status"`
	RequestData   string              `json:"request_data"`
	TransactionId string              `json:"transaction_id"`
	CreateTime    *time.Time          `json:"create_time"`
}
