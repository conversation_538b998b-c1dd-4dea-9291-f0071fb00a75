package wechatrepositoryimpl

const CodeSuccess = 200

type CpnStatus string

const (
	CpnStatusAvailable = "SENDED"
	CpnStatusUsed      = "USED"
	CpnStatusExpired   = "EXPIRED"
)

var CpnStatusTextMap = map[CpnStatus]string{
	CpnStatusAvailable: "可用",
	CpnStatusUsed:      "已实扣",
	CpnStatusExpired:   "已过期",
}

func (o CpnStatus) GetText() string {
	if msg, ok := CpnStatusTextMap[o]; !ok {
		return msg
	}
	return "未知"
}
